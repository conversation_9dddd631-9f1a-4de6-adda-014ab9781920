package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.DDataSql;
import com.ruoyi.system.service.IDDataSqlService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 外部系统平台数据查询sql配置Controller
 * 
 * <AUTHOR>
 * @date 2022-03-31
 */
@RestController
@RequestMapping("/system/sql")
public class DDataSqlController extends BaseController
{
    @Autowired
    private IDDataSqlService dDataSqlService;

    /**
     * 查询外部系统平台数据查询sql配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:sql:list')")
    @GetMapping("/list")
    public TableDataInfo list(DDataSql dDataSql)
    {
        startPage();
        List<DDataSql> list = dDataSqlService.selectDDataSqlList(dDataSql);
        return getDataTable(list);
    }

    /**
     * 导出外部系统平台数据查询sql配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:sql:export')")
    @Log(title = "外部系统平台数据查询sql配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, DDataSql dDataSql)
    {
        List<DDataSql> list = dDataSqlService.selectDDataSqlList(dDataSql);
        ExcelUtil<DDataSql> util = new ExcelUtil<DDataSql>(DDataSql.class);
        util.exportExcel(response, list, "外部系统平台数据查询sql配置数据");
    }

    /**
     * 获取外部系统平台数据查询sql配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:sql:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(dDataSqlService.selectDDataSqlById(id));
    }

    /**
     * 新增外部系统平台数据查询sql配置
     */
    @PreAuthorize("@ss.hasPermi('system:sql:add')")
    @Log(title = "外部系统平台数据查询sql配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody DDataSql dDataSql)
    {
        String operName = getUsername();
        dDataSql.setCreateBy(operName);
        return toAjax(dDataSqlService.insertDDataSql(dDataSql));
    }

    /**
     * 修改外部系统平台数据查询sql配置
     */
    @PreAuthorize("@ss.hasPermi('system:sql:edit')")
    @Log(title = "外部系统平台数据查询sql配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody DDataSql dDataSql)
    {
        String operName = getUsername();
        return toAjax(dDataSqlService.updateDDataSql(dDataSql,operName));
    }

    /**
     * 删除外部系统平台数据查询sql配置
     */
    @PreAuthorize("@ss.hasPermi('system:sql:remove')")
    @Log(title = "外部系统平台数据查询sql配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(dDataSqlService.deleteDDataSqlByIds(ids));
    }




    /**
     * 下载导入模板
     * @param response
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<DDataSql> util = new ExcelUtil<DDataSql>(DDataSql.class);
        util.importTemplateExcel(response, "外部数据系统查询配置数据");
    }

    /**
     * 导入
     * @param file 导入文件
     * @param updateSupport 是否覆盖
     * @return
     * @throws Exception
     */
    @Log(title = "外部数据系统查询配置", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:sql:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<DDataSql> util = new ExcelUtil<DDataSql>(DDataSql.class);
        List<DDataSql> mappingsList = util.importExcel(file.getInputStream());
        String operName = getUsername();

        String message = dDataSqlService.importDDataSql(mappingsList, updateSupport, operName);
        return AjaxResult.success(message);
    }
}
