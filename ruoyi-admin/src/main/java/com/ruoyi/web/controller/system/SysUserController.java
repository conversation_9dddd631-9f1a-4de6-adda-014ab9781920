package com.ruoyi.web.controller.system;

import com.github.pagehelper.PageHelper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.*;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.OaFlowTreeVo;
import com.ruoyi.system.domain.vo.ProClassificationVo;
import com.ruoyi.system.domain.vo.SysUserPostVo;
import com.ruoyi.system.service.ISysPostService;
import com.ruoyi.system.service.ISysRoleService;
import com.ruoyi.system.service.ISysUserService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/user")
public class SysUserController extends BaseController
{
    @Autowired
    private ISysUserService userService;

    @Autowired
    private ISysRoleService roleService;

    @Autowired
    private ISysPostService postService;

    /**
     * 获取用户列表
     */
    // @PreAuthorize("@ss.hasPermi('system:user:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysUser user)
    {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @Log(title = "用户管理", businessType = BusinessType.EXPORT)
    @PreAuthorize("@ss.hasPermi('system:user:export')")
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysUser user)
    {
        List<SysUser> list = userService.selectUserList(user);
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.exportExcel(response, list, "用户数据");
    }

    @Log(title = "用户管理", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('system:user:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        List<SysUser> userList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = userService.importUser(userList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<SysUser> util = new ExcelUtil<SysUser>(SysUser.class);
        util.importTemplateExcel(response, "用户数据");
    }

    /**
     * 根据用户编号获取详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping(value = { "/", "/{userId}" })
    public AjaxResult getInfo(@PathVariable(value = "userId", required = false) Long userId)
    {
        //userService.checkUserDataScope(userId);
        AjaxResult ajax = AjaxResult.success();
        List<SysRole> roles = roleService.selectRoleAll();
        List<SysPost> sysPosts = postService.selectPostAll();
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        ajax.put("posts", sysPosts);
        if (StringUtils.isNotNull(userId))
        {
            SysUser sysUser = userService.selectUserById(userId);
            // 拼接部门名称
            if (sysUser.getUserPostList() != null && sysUser.getUserPostList().size() > 0){
                userService.jointDept(sysUser.getUserPostList(),sysPosts);
            }
            ajax.put(AjaxResult.DATA_TAG, sysUser);
        }
        return ajax;
    }

    /**
     * 新增用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:add')")
    @Log(title = "用户管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody SysUser user)
    {
        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(user.getUserName())))
        {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，登录账号已存在");
        }
        else if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user)))
        {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user)))
        {
            return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }else if(user.getUserPostList().size()>0) {
        	int count =0;
        	for (SysUserPost info : user.getUserPostList()) {
				if(info.getHomePost().equals("0")) {
					count++;
				}
			}
        	if(count==0) {
        		return AjaxResult.error("新增用户'" + user.getUserName() + "'失败，必须设置用户主岗位");
        	}
        }
        user.setCreateBy(getUsername());
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        return toAjax(userService.insertUser(user));
    }

    /**
     * 修改用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE, functionNode = "修改用户")
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        if (StringUtils.isNotEmpty(user.getPhonenumber())
                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(user)))
        {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，手机号码已存在");
        }
        else if (StringUtils.isNotEmpty(user.getEmail())
                && UserConstants.NOT_UNIQUE.equals(userService.checkEmailUnique(user)))
        {
            return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，邮箱账号已存在");
        }else if(user.getUserPostList().size()>0) {
        	int count =0;
        	for (SysUserPost info : user.getUserPostList()) {
				if(info.getHomePost().equals("0")) {
					count++;
				}
			}
        	if(count==0) {
        		return AjaxResult.error("修改用户'" + user.getUserName() + "'失败，必须设置用户主岗位");
        	}
        }
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUser(user));
    }

    /**
     * 删除用户
     */
    @PreAuthorize("@ss.hasPermi('system:user:remove')")
    @Log(title = "用户管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{userIds}")
    public AjaxResult remove(@PathVariable Long[] userIds)
    {
        if (ArrayUtils.contains(userIds, getUserId()))
        {
            return error("当前用户不能删除");
        }
        return toAjax(userService.deleteUserByIds(userIds));
    }

    /**
     * 重置密码
     */
    @PreAuthorize("@ss.hasPermi('system:user:resetPwd')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/resetPwd")
    public AjaxResult resetPwd(@RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        user.setPassword(SecurityUtils.encryptPassword(user.getPassword()));
        user.setUpdateBy(getUsername());
        return toAjax(userService.resetPwd(user));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody SysUser user)
    {
        userService.checkUserAllowed(user);
        user.setUpdateBy(getUsername());
        return toAjax(userService.updateUserStatus(user));
    }

    /**
     * 根据用户编号获取授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:query')")
    @GetMapping("/authRole/{userId}")
    public AjaxResult authRole(@PathVariable("userId") Long userId)
    {
        AjaxResult ajax = AjaxResult.success();
        SysUser user = userService.selectUserById(userId);
        List<SysRole> roles = roleService.selectRolesByUserId(userId);
        ajax.put("user", user);
        ajax.put("roles", SysUser.isAdmin(userId) ? roles : roles.stream().filter(r -> !r.isAdmin()).collect(Collectors.toList()));
        return ajax;
    }

    /**
     * 用户授权角色
     */
    @PreAuthorize("@ss.hasPermi('system:user:edit')")
    @Log(title = "用户管理", businessType = BusinessType.GRANT)
    @PutMapping("/authRole")
    public AjaxResult insertAuthRole(Long userId, Long[] roleIds)
    {
        userService.insertUserAuth(userId, roleIds);
        return success();
    }







    /**
     * 获取用户列表,用于下拉选择用户，所有正常用户（不含删除及禁用）
     */
    @GetMapping("/getUserListEnable")
    public AjaxResult getUserListEnable()
    {
    	List<SysUser> list = userService.selectUserListEnable();
    	if (StringUtils.isNull(list)){
    		list=new ArrayList<SysUser>();
        }
        return AjaxResult.success(list);
    }

    /**
     * 获取用户列表,用于下拉选择用户，所有用户(含删除及禁用)
     */
    @GetMapping("/getUserListAll")
    public AjaxResult getUserListAll()
    {
    	List<SysUser> list = userService.selectUserListAll();
    	if (StringUtils.isNull(list)){
    		list=new ArrayList<SysUser>();
        }
        return AjaxResult.success(list);
    }

    /**
     * 根据部门id查找该部门的所有的用户
     */
    @GetMapping("/getUserListByDeptId")
    public AjaxResult getUserListByDeptId(SysUser sysUser)
    {
        List<SysUser> list = userService.getUserListByDeptId(sysUser);
        if (StringUtils.isNull(list)){
            list=new ArrayList<SysUser>();
        }
        return AjaxResult.success(list);
    }

    /**
     * 判断用户是否有能查看原数据平台首页入口权限
     * @return
     */
    @GetMapping("/getMenuByUserList")
    public Map<String,Object> getMenuList(){
        Long userId = SecurityUtils.getUserId();
        return userService.getMenuList(userId);
    }

    /**
     * 获取用户授权列表
     */
    @GetMapping("/getUserAuthorizationList")
    public AjaxResult getUserAuthorizationList(SysUser sysUser){
        return AjaxResult.success(userService.getUserAuthorizationList(sysUser));
    }

    /**
     * 获取用户授权列表
     */
    @GetMapping("/getHandover")
    public TableDataInfo getHandover(SysUser sysUser){
        startPage();
        List<SysUserAuth> list = userService.getUserAuthorizationList(sysUser);
        return getDataTable(list);
    }

    /**
     * 根据岗位id查找该岗位的所有的用户
     */
    @GetMapping("/getUserListByPostId")
    public AjaxResult getUserListByPostId(Long postId)
    {
        LoginUser loginUser = getLoginUser();
        SysUser sysUser = new SysUser();
        SysPostAO sysPostAO = new SysPostAO();
        sysPostAO.setPostId(postId);
        sysUser.setPost(sysPostAO);
        List<SysUser> list = userService.getUserListByPostId(sysUser, loginUser);
        if (StringUtils.isNull(list)){
            list=new ArrayList<SysUser>();
        }
        return AjaxResult.success(list);
    }


    /**
     * 获取用户列表
     */
    @GetMapping("/queryUserInfoList")
    public TableDataInfo queryUserInfoList(SysUser user)
    {
        startPage();
        List<SysUser> list = userService.selectUserList(user);
        return getDataTable(list);
    }

    @GetMapping("/listForPersonnel")
    public TableDataInfo listForPersonnel(SysUser user)
    {
        startPage();
        List<SysUserAuth> list = userService.listForPersonnel(user);
        return getDataTable(list);
    }

    /**
     * 根据岗位id查询已授权用户列表
     * @param sysUserPostVo
     * @return
     */
    @GetMapping("/accreditUsers")
    public TableDataInfo accreditUsersList(SysUserPostVo sysUserPostVo) {

        startPage();
        List<SysUser> list = userService.queryAccreditUserList(sysUserPostVo);
        return getDataTable(list);
    }

    /**
     * 根据用户id查询所有岗位的所属公司的流程集合
     * @param userId
     * @return
     */
    @GetMapping("/getUserProcessList/{userId}")
    public TableDataInfo getUserProcessList(@PathVariable(value = "userId", required = false) Long userId){
        List<ProClassificationVo> oaFlows = userService.selectUserProcessListByUserId(userId);
        return getDataTable(oaFlows);
    }

    /**
     * 获取指定部门下的用户组织架构
     * @return
     */
    @PostMapping("/getAssignDeptUserList")
    public TableDataInfo getAssignDeptUserList(@RequestBody SysDept sysDept){
        List<SysDept> list = userService.getAssignDeptUserList(sysDept);
        return getDataTable(list);
    }

    /**
     * 查看用户个人中心
     */
    @GetMapping(value = { "/getUserInfo/{userId}" })
    public AjaxResult getUserInfo(@PathVariable(value = "userId", required = false) Long userId)
    {
        AjaxResult ajax = AjaxResult.success();
        List<SysPost> sysPosts = postService.selectAllPostInfo();
        ajax.put("posts", sysPosts);
        if (StringUtils.isNotNull(userId))
        {
            SysUser sysUser = userService.selectUserById(userId);
            // 拼接部门名称
            if (sysUser.getUserPostList() != null && sysUser.getUserPostList().size() > 0){
                userService.jointDept(sysUser.getUserPostList(),sysPosts);
            }
            ajax.put(AjaxResult.DATA_TAG, sysUser);
        }
        return ajax;
    }
}
