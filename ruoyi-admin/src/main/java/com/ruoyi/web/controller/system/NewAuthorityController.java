package com.ruoyi.web.controller.system;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.bo.*;
import com.ruoyi.system.domain.vo.QueryAgentVO;
import com.ruoyi.system.domain.vo.QueryNewAuthorityVO;
import com.ruoyi.system.service.INewAuthorityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 新权限Controller
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/4/12 16:30
 **/
@RestController
@RequestMapping("/system/newAuthority")
public class NewAuthorityController extends BaseController {
    @Autowired
    private INewAuthorityService newAuthorityService;

    /**
     * 查询权限列表前置 - 项目维度调用，查看具体可以使用哪些担保公司
     **/
    @GetMapping("/queryCompanyList")
    public TableDataInfo queryCompanyList(QueryNewAuthorityBO queryNewAuthorityBO) {
        LoginUser loginUser = getLoginUser();
        List<Long> companyIdList = newAuthorityService.queryAuthorityCompany(queryNewAuthorityBO, loginUser);
        return getDataTable(companyIdList);
    }

    /**
     * 查询权限列表
     **/
    @GetMapping
    public TableDataInfo queryList(QueryNewAuthorityBO queryNewAuthorityBO) {
        LoginUser loginUser = getLoginUser();
        List<QueryNewAuthorityVO> queryNewAuthorityVOList = newAuthorityService.queryAuthority(queryNewAuthorityBO, loginUser);
        return getDataTable(queryNewAuthorityVOList);
    }

    /**
     * 查询要取消的用户是否授权了下级用户 返回0代表没有 其余代表有
     **/
    @GetMapping("/queryCancelUser")
    public AjaxResult queryCancelUser(CancelAuthorizationBO cancelAuthorizationBO) {
        return AjaxResult.success(newAuthorityService.queryCancelUser(cancelAuthorizationBO));
    }

    /**
     * 查询取消所有授权的用户是否授权了下级用户 返回0代表没有 其余代表有
     **/
    @GetMapping("/queryAllCancelUser")
    public AjaxResult queryAllCancelUser(CancelAllAuthorizationBO cancelAllAuthorizationBO) {
        return AjaxResult.success(newAuthorityService.queryAllCancelUser(cancelAllAuthorizationBO));
    }

    /**
     * 取消授权
     **/
    @PostMapping("/cancelAuthorization")
    public AjaxResult cancelAuthorization(@RequestBody CancelAuthorizationBO cancelAuthorizationBO) {
        LoginUser loginUser = getLoginUser();
        return newAuthorityService.cancelAuthorization(cancelAuthorizationBO, loginUser);
    }

    /**
     * 取消所有授权
     **/
    @PostMapping("/cancelAllAuthorization")
    public AjaxResult cancelAllAuthorization(@RequestBody CancelAllAuthorizationBO cancelAuthorizationBO) {
        LoginUser loginUser = getLoginUser();
        return newAuthorityService.cancelAllAuthorization(cancelAuthorizationBO, loginUser);
    }

    /**
     * 详细版 - 新增授权
     **/
    @PostMapping("/addAuthorization")
    public AjaxResult addAuthorization(@RequestBody AddAuthorizationBO addAuthorizationBO) {
        LoginUser loginUser = getLoginUser();
        return newAuthorityService.addAuthorization(addAuthorizationBO, loginUser);
    }

    /**
     * 详细版 - 查询当前用户是什么角色（管理员、具体业务角色），个别模块用 财务项目管理 车贷绿本 项目管理
     **/
    @GetMapping("/userRoleAuthority")
    public AjaxResult userRoleAuthority(String businessType, String businessCode, Long principalId, @RequestParam List<Long> inputIdList) {
        LoginUser loginUser = getLoginUser();
        return newAuthorityService.userRoleAuthority(businessType, businessCode, loginUser, principalId, inputIdList);
    }

    /**
     * 查询代理人列表
     **/
    @GetMapping("/getAgentList")
    public TableDataInfo getAgentList(String queryType, String status, Integer pageNum, Integer pageSize) {
        LoginUser loginUser = getLoginUser();
        List<QueryAgentVO> queryAgentVOList = newAuthorityService.getAgentList(queryType, status, loginUser, pageNum, pageSize);
        return getDataTable(queryAgentVOList);
    }

    /**
     * 添加/编辑代理人前置查询权限时间
     **/
    @GetMapping("/agentHandleBefore")
    public AjaxResult agentHandleBefore(@RequestParam List<String> agencyModules, String agencyTime) {
        LoginUser loginUser = getLoginUser();
        return newAuthorityService.agentHandleBefore(agencyModules, agencyTime, loginUser);
    }

    /**
     * 添加/编辑代理人
     **/
    @PostMapping("/agentHandle")
    public AjaxResult agentHandle(@RequestBody AddEditAgentBO addEditAgentBO) {
        LoginUser loginUser = getLoginUser();
        return newAuthorityService.agentHandle(addEditAgentBO, loginUser);
    }

    /**
     * 查询是否有代理人或者是否被代理
     **/
    @GetMapping("/haveAgencyQuery")
    public AjaxResult haveAgencyQuery(String agencyType) {
        LoginUser loginUser = getLoginUser();
        return newAuthorityService.haveAgencyQuery(agencyType, loginUser);
    }

    /**
     * 通过代理授权的ids查询当时用户的授权记录
     **/
    @GetMapping("/getAgencyAuthRecord")
    public AjaxResult getAgencyAuthRecord(@RequestParam List<Long> ids) {
        String authMainIds = ids.stream().map(String::valueOf).collect(Collectors.joining(","));
        return newAuthorityService.getAgencyAuthRecord(authMainIds);
    }

    /**
     * 代理授权列表 - 我的代理人列表为空的时候，调用本方法获取代理的模块信息
     **/
    @GetMapping("/getAuthCode")
    public AjaxResult getAuthCode() {
        LoginUser loginUser = getLoginUser();
        List<String> authCode = newAuthorityService.getAuthCode(loginUser);
        return AjaxResult.success(authCode);
    }

    /**
     * 停止代理
     **/
    @PostMapping("/cancelAgent")
    public AjaxResult cancelAgent(@RequestBody Map<String, Object> objectMap) {
        List<Long> ids = (List<Long>) objectMap.get("ids");
        LoginUser loginUser = getLoginUser();
        return newAuthorityService.cancelAgent(ids, loginUser);
    }

    /**
     * 初始化部分权限（按照某个用户向下授予的某个权限，同步赋值给另外一个权限）
     * 注意：不能严格设定带有角色的那种权限类别交叉（比如一个带角色的权限复制到另外一个不带角色的权限）
     * 例：张三分发下去的权限 把他分发下去的Echarts权限全部以项目名称同步一遍
     * 参数：userName 账号（要同步哪个用户的下级以及下下级权限）
     * sourceAuth 源权限字符（从哪个权限字符复制）
     * targetAuth 目标权限字符（要完成的哪些权限）
     **/
    @PostMapping("/copyAuthByUserUserNameAndSourceAuthAndTargetAuth")
    public AjaxResult copyAuthByUserUserNameAndSourceAuthAndTargetAuth(@RequestBody Map<String, Object> objectMap) {
        String userName = (String) objectMap.get("userName");
        String sourceAuthModule = (String) objectMap.get("sourceAuthModule");
        String targetAuthModule = (String) objectMap.get("targetAuthModule");
        return toAjax(newAuthorityService.copyAuthByUserUserNameAndSourceAuthAndTargetAuth(userName, sourceAuthModule, targetAuthModule));
    }
}
