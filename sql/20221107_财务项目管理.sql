
CREATE TABLE `top_notify` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `notify_module` varchar(50) NOT NULL COMMENT '通知模块',
  `notify_type` char(1) NOT NULL COMMENT '通知类型 0通知 1待办',
  `notify_msg` varchar(500) NOT NULL COMMENT '通知内容',
  `url` varchar(255) NOT NULL COMMENT '待办url',
  `view_flag` char(1) NOT NULL COMMENT '阅读状态：0未阅 1已阅',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `createTime` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `updateTime` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知待办信息表';




CREATE TABLE `cw_project_dynamic` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '项目管理表主键',
  `dynamic_msg` text DEFAULT '' COMMENT '动态内容',
  `dynamic_time` datetime DEFAULT NULL COMMENT '动态时间',
  `oper_id` bigint(20) DEFAULT null COMMENT '操作人员id',
  `oper_name` varchar(64) DEFAULT '' COMMENT '操作人员姓名',
  `dept_name` varchar(50) DEFAULT '' COMMENT '部门名称',
  `status` int(1) DEFAULT '0' COMMENT '操作状态（0正常 1异常）',
  `error_msg` text DEFAULT '' COMMENT '错误消息',
  PRIMARY KEY (`oper_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务项目管理动态表';




CREATE TABLE `cw_project` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_name` varchar(200) NOT NULL COMMENT '项目名称',
  `cust_name` varchar(200) NOT NULL COMMENT '担保公司',
  `income_cust_name` varchar(200) NOT NULL COMMENT '汇款公司',
  `project_flag` char(1) NOT NULL COMMENT '项目状态：0正常 1终止',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务项目管理主表';


CREATE TABLE `cw_project_cust` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '项目管理表主键',
  `cust_name` varchar(200) NOT NULL COMMENT '返费公司名称',
  `rate` decimal(11,8) NULL COMMENT '费率 %',
  `tax_rate` decimal(11,8) NULL COMMENT '税率 %',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务项目管理-返费公司与费率表';


CREATE TABLE `cw_project_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '项目管理表主键',
  `user_id` bigint(20) NOT NULL COMMENT '用户id',
  `user_flag` char(1) NOT NULL COMMENT '用户标识：0会计1出纳2业务',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务项目管理-成员表';




CREATE TABLE `cw_project_income` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '项目管理表主键',
  `term` char(1) NOT NULL COMMENT '业务期次类型 0整月 1非整月',
  `term_month` char(7)  NULL COMMENT '业务期次所属月份',
  `term_begin` date  NULL COMMENT '业务期次所属开始日期',
  `term_end` date  NULL COMMENT '业务期次所属结束日期',
  `income_amt` decimal(17,2) NOT NULL COMMENT '收入金额 元',
  `gross_profit_amt` decimal(17,2) NOT NULL COMMENT '毛利金额 元',
  `gross_profit_amt2` decimal(17,2) NOT NULL COMMENT '提成毛利金额 元',
  `fee_amt` decimal(17,2) NOT NULL COMMENT '返费已结清金额 元',
  `unfee_amt` decimal(17,2) NOT NULL COMMENT '返费未结清金额 元',
  `remark` text  NULL COMMENT '备注信息',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务项目管理-收入表';




CREATE TABLE `cw_project_fee` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '项目管理表主键',
  `project_income_id` bigint(20) NOT NULL  COMMENT '项目收入表主键',
  `cust_name` varchar(1) DEFAULT '' COMMENT '出返费公司',
  `fee_cust_name` varchar(1) DEFAULT '' COMMENT '返费公司',
  `calculate_type` char(1) DEFAULT '0' COMMENT '计算方式，0自动 1手动',
  `fee_amt` decimal(17,2) NOT NULL COMMENT '返费金额 元',
  `fee_amt2` decimal(17,2) NOT NULL COMMENT '提成返费金额 元',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务项目管理-返费表';




CREATE TABLE `cw_project_pay` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '项目管理表主键',
  `project_income_id` bigint(20) NOT NULL  COMMENT '项目收入表主键',
  `project_fee_id` bigint(20) NOT NULL  COMMENT '项目返费表主键',
  `pay_date` date DEFAULT null COMMENT '打款日期',
  `pay_amt` decimal(17,2) NOT NULL COMMENT '实际打款金额 元',
  `difference_amt` decimal(17,2) NOT NULL COMMENT '抹平差额 元',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务项目管理-打款信息表';







CREATE TABLE `cw_project_ack` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `project_id` bigint(20) NOT NULL  COMMENT '项目管理表主键',
  `ack_flag` char(1) DEFAULT '0' COMMENT '确认状态 0 未确认 1已确认',
  `ack_date` datetime DEFAULT null COMMENT '确认时间',
  `ack_by` varchar(64) DEFAULT null COMMENT '确认人姓名',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务项目管理-提成基数确认表';




CREATE TABLE `cw_project_ack_ref` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `ack_id` bigint(20) NOT NULL  COMMENT '提成基数确认表主键',
  `project_fee_id` bigint(20) NOT NULL  COMMENT '项目返费表主键',
  `status` char(1) DEFAULT '0' COMMENT '状态，0正常 1禁用',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_Time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_Time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='财务项目管理-提成基数确认及返费关联表';









