/*
 Navicat Premium Data Transfer

 Source Server         : 本地
 Source Server Type    : MySQL
 Source Server Version : 50733
 Source Host           : localhost:3306
 Source Schema         : ruoyi

 Target Server Type    : MySQL
 Target Server Version : 50733
 File Encoding         : 65001

 Date: 11/05/2022 16:29:07
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for d_data
-- ----------------------------
DROP TABLE IF EXISTS `d_data`;
CREATE TABLE `d_data`  (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `platform_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '外部系统平台编码',
  `cust_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '担保公司编码',
  `partner_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作方编码',
  `fund_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '资金方编码',
  `product_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '产品编码',
  `is_portrayal` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'N' COMMENT '是否为画像数据（Y是 N否）',
  `portrayal_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '画像类型',
  `portrayal_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '画像编码',
  `recon_date` date NOT NULL COMMENT '数据统计时间',
  `total_begin_date` date NOT NULL COMMENT '历史累计-数据起算日',
  `total_end_date` date NOT NULL COMMENT '历史累计-统计截止日',
  `total_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '历史累计-贷款笔数（笔）',
  `total_human_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '历史累计-贷款人数（人）',
  `total_amount` decimal(17, 2) NOT NULL COMMENT '历史累计-累计贷款本金（元）',
  `total_average_irr` decimal(11, 8) NOT NULL COMMENT '历史累计-贷款平均IRR（%）',
  `total_average_term` decimal(17, 2) NOT NULL COMMENT '历史累计-平均贷款期限（月）',
  `total_balance_amount` decimal(17, 2) NOT NULL COMMENT '历史累计-累计贷款本金余额（元）',
  `total_uncleared_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '历史累计-未结清贷款笔数（笔）',
  `total_compensate_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '历史累计-代偿笔数（笔）',
  `total_compensate_amount` decimal(17, 2) NOT NULL COMMENT '历史累计-累计代偿本金（元）',
  `add_begin_date` date NOT NULL COMMENT '当期新增-初始起算日',
  `add_end_date` date NOT NULL COMMENT '当期新增-统计截止日',
  `add_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '当期新增-新增贷款笔数（笔）',
  `add_human_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '当期新增-新增贷款人数（人）',
  `add_amount` decimal(17, 2) NOT NULL COMMENT '当期新增-新增贷款本金（元）',
  `add_ealance_amount` decimal(17, 2) NOT NULL COMMENT '当期新增-新增贷款本金余额（元）',
  `add_median_amount` decimal(17, 2) NOT NULL COMMENT '当期新增-贷款金额中位数（元）',
  `add_average_irr` decimal(11, 8) NOT NULL COMMENT '当期新增-贷款平均IRR（%）',
  `add_average_term` decimal(17, 2) NOT NULL COMMENT '当期新增-平均贷款期限（月）',
  `add_compensate_count` bigint(20) NOT NULL DEFAULT 0 COMMENT '当期新增-代偿笔数（笔）',
  `add_compensate_amount` decimal(17, 2) NOT NULL COMMENT '当期新增-累计代偿本金（元）',
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `is_mapping` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '是否映射成功',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `create_time` datetime(0) NOT NULL COMMENT '创建时间',
  `update_time` datetime(0) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 205 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '外部系统平台运营情况数据表' ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
