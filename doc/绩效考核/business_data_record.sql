create table kh_annual_plan
(
    id                    bigint auto_increment comment '主键'
        primary key,
    year                  varchar(10)                        null comment '年度',
    type                  varchar(10)                        null comment '所属类型  1.公司 2.部门 3.个人',
    company_id            bigint                             null comment '所属公司',
    dept_id               bigint                             null comment '所属部门',
    user_id               bigint                             null comment '所属个人',
    q1_total_index        decimal(10, 2)                     null comment '一季度总指标',
    q1_distribution_index decimal(10, 2)                     null comment '一季度分配项目指标',
    q1_extension_index    decimal(10, 2)                     null comment '一季度自拓项目指标',
    q1_extension_bank     decimal(10, 2)                     null comment '一季度自拓银行',
    q2_total_index        decimal(10, 2)                     null comment '二季度总指标',
    q2_distribution_index decimal(10, 2)                     null comment '二季度分配项目指标',
    q2_extension_index    decimal(10, 2)                     null comment '二季度自拓项目指标',
    q2_extension_bank     decimal(10, 2)                     null comment '二季度自拓银行',
    q3_total_index        decimal(10, 2)                     null comment '三季度总指标',
    q3_distribution_index decimal(10, 2)                     null comment '三季度分配项目指标',
    q3_extension_index    decimal(10, 2)                     null comment '三季度自拓项目指标',
    q3_extension_bank     decimal(10, 2)                     null comment '三季度自拓银行',
    q4_total_index        decimal(10, 2)                     null comment '四季度总指标',
    q4_distribution_index decimal(10, 2)                     null comment '四季度分配项目指标',
    q4_extension_index    decimal(10, 2)                     null comment '四季度自拓项目指标',
    q4_extension_bank     decimal(10, 2)                     null comment '四季度自拓银行',
    state                 char     default '1'               null comment '状态 (未提交，审核中，审核通过，审核不通过)',
    create_by             varchar(64)                        null comment '创建者',
    create_time           datetime default CURRENT_TIMESTAMP null comment '创建时间',
    update_by             varchar(64)                        null comment '更新者',
    update_time           datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间'
)
    comment '年度计划' collate = utf8mb4_general_ci
                       row_format = DYNAMIC;


