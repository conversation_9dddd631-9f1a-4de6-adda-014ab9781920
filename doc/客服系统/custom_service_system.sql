/*
 Navicat Premium Data Transfer

 Source Server         : 客服-测试-操作
 Source Server Type    : MySQL
 Source Server Version : 80028 (8.0.28)
 Source Host           : **************:3306
 Source Schema         : custom_service_system

 Target Server Type    : MySQL
 Target Server Version : 80028 (8.0.28)
 File Encoding         : 65001

 Date: 23/05/2025 10:53:31
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS `gen_table`;
CREATE TABLE `gen_table`  (
  `table_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '表名称',
  `table_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '表描述',
  `sub_table_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '关联子表的表名',
  `sub_table_fk_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '子表关联的外键名',
  `class_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '实体类名称',
  `tpl_category` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'crud' COMMENT '使用的模板（crud单表操作 tree树表操作）',
  `tpl_web_type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '前端模板类型（element-ui模版 element-plus模版）',
  `package_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成包路径',
  `module_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成模块名',
  `business_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成业务名',
  `function_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成功能名',
  `function_author` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '生成功能作者',
  `gen_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '生成代码方式（0zip压缩包 1自定义路径）',
  `gen_path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '/' COMMENT '生成路径（不填默认项目路径）',
  `options` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '其它生成选项',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`table_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '代码生成业务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_table
-- ----------------------------

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS `gen_table_column`;
CREATE TABLE `gen_table_column`  (
  `column_id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号',
  `table_id` bigint NULL DEFAULT NULL COMMENT '归属表编号',
  `column_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列名称',
  `column_comment` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列描述',
  `column_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '列类型',
  `java_type` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'JAVA类型',
  `java_field` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'JAVA字段名',
  `is_pk` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否主键（1是）',
  `is_increment` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否自增（1是）',
  `is_required` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否必填（1是）',
  `is_insert` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否为插入字段（1是）',
  `is_edit` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否编辑字段（1是）',
  `is_list` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否列表字段（1是）',
  `is_query` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否查询字段（1是）',
  `query_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'EQ' COMMENT '查询方式（等于、不等于、大于、小于、范围）',
  `html_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）',
  `dict_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `sort` int NULL DEFAULT NULL COMMENT '排序',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`column_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '代码生成业务表字段' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------

-- ----------------------------
-- Table structure for kf_account_info
-- ----------------------------
DROP TABLE IF EXISTS `kf_account_info`;
CREATE TABLE `kf_account_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `account_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '账号编码',
  `account_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '账号姓名',
  `channel_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '渠道ID',
  `status` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `last_update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `channel_usercode`(`account_code` ASC, `channel_id` ASC) USING BTREE COMMENT '渠道和账号编码唯一'
) ENGINE = InnoDB AUTO_INCREMENT = 71 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kf_account_info
-- ----------------------------
INSERT INTO `kf_account_info` VALUES (2, 'ry', '若依', '30', '0', '2024-12-16 15:14:45', 'admin', '2024-12-16 15:14:45', NULL);
INSERT INTO `kf_account_info` VALUES (8, 'ry', '若依', '17', '0', '2024-12-23 16:56:05', 'demo', '2024-12-23 16:56:04', NULL);
INSERT INTO `kf_account_info` VALUES (9, 'ry', '若依', '18', '0', '2024-12-23 16:56:05', 'demo', '2024-12-23 16:56:04', NULL);
INSERT INTO `kf_account_info` VALUES (10, 'ry', '若依', '19', '0', '2024-12-23 16:56:05', 'demo', '2024-12-23 16:56:04', NULL);
INSERT INTO `kf_account_info` VALUES (11, 'ry', '若依', '20', '0', '2024-12-23 16:56:05', 'demo', '2024-12-23 16:56:04', NULL);
INSERT INTO `kf_account_info` VALUES (21, 'ry', '若依', '1', '0', '2024-12-23 16:57:06', 'demo', '2024-12-23 16:57:05', NULL);
INSERT INTO `kf_account_info` VALUES (22, 'ry', '若依', '2', '0', '2024-12-23 16:57:06', 'demo', '2024-12-23 16:57:05', NULL);
INSERT INTO `kf_account_info` VALUES (23, 'ry', '若依', '3', '0', '2024-12-23 16:57:06', 'demo', '2024-12-23 16:57:05', NULL);
INSERT INTO `kf_account_info` VALUES (24, 'ry', '若依', '4', '0', '2024-12-23 16:57:06', 'demo', '2024-12-23 16:57:05', NULL);
INSERT INTO `kf_account_info` VALUES (25, 'ry', '若依', '34', '0', '2024-12-23 16:57:06', 'demo', '2024-12-23 16:57:05', NULL);
INSERT INTO `kf_account_info` VALUES (48, 'demo', 'demo', '17', '0', '2025-01-14 09:47:47', 'admin', '2025-01-14 09:47:46', NULL);
INSERT INTO `kf_account_info` VALUES (49, 'demo', 'demo', '18', '0', '2025-01-14 09:47:47', 'admin', '2025-01-14 09:47:46', NULL);
INSERT INTO `kf_account_info` VALUES (50, 'demo', 'demo', '19', '0', '2025-01-14 09:47:47', 'admin', '2025-01-14 09:47:46', NULL);
INSERT INTO `kf_account_info` VALUES (51, 'demo', 'demo', '20', '0', '2025-01-14 09:47:47', 'admin', '2025-01-14 09:47:46', NULL);
INSERT INTO `kf_account_info` VALUES (52, 'ry', '若依', '25', '0', '2025-01-14 09:48:16', 'admin', '2025-01-14 09:48:16', NULL);
INSERT INTO `kf_account_info` VALUES (53, 'ry', '若依', '26', '0', '2025-01-14 09:48:16', 'admin', '2025-01-14 09:48:16', NULL);
INSERT INTO `kf_account_info` VALUES (54, 'ry', '若依', '27', '0', '2025-01-14 09:48:16', 'admin', '2025-01-14 09:48:16', NULL);
INSERT INTO `kf_account_info` VALUES (55, 'ry', '若依', '28', '0', '2025-01-14 09:48:16', 'admin', '2025-01-14 09:48:16', NULL);
INSERT INTO `kf_account_info` VALUES (56, 'demo', 'demo', '25', '0', '2025-01-14 09:48:16', 'admin', '2025-01-14 09:48:16', NULL);
INSERT INTO `kf_account_info` VALUES (57, 'demo', 'demo', '26', '0', '2025-01-14 09:48:16', 'admin', '2025-01-14 09:48:16', NULL);
INSERT INTO `kf_account_info` VALUES (58, 'demo', 'demo', '27', '0', '2025-01-14 09:48:16', 'admin', '2025-01-14 09:48:16', NULL);
INSERT INTO `kf_account_info` VALUES (59, 'demo', 'demo', '28', '0', '2025-01-14 09:48:16', 'admin', '2025-01-14 09:48:16', NULL);
INSERT INTO `kf_account_info` VALUES (66, 'admin', '超级管理员', '1', '0', '2025-01-21 10:05:00', 'admin', '2025-01-21 10:04:59', NULL);
INSERT INTO `kf_account_info` VALUES (67, 'admin', '超级管理员', '2', '0', '2025-01-21 10:05:00', 'admin', '2025-01-21 10:04:59', NULL);
INSERT INTO `kf_account_info` VALUES (68, 'admin', '超级管理员', '4', '0', '2025-01-21 10:05:00', 'admin', '2025-01-21 10:04:59', NULL);
INSERT INTO `kf_account_info` VALUES (69, 'admin', '超级管理员', '3', '0', '2025-01-21 10:05:00', 'admin', '2025-01-21 10:04:59', NULL);
INSERT INTO `kf_account_info` VALUES (70, 'admin', '超级管理员', '34', '0', '2025-01-21 10:05:00', 'admin', '2025-01-21 10:04:59', NULL);

-- ----------------------------
-- Table structure for kf_channel_info
-- ----------------------------
DROP TABLE IF EXISTS `kf_channel_info`;
CREATE TABLE `kf_channel_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `channel_code` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '渠道编码',
  `channel_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '渠道名称',
  `company_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公司编码',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  `status` varchar(5) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `last_update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `channel_company_un`(`channel_code` ASC, `company_code` ASC) USING BTREE COMMENT '公司编码和渠道编码唯一索引'
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kf_channel_info
-- ----------------------------
INSERT INTO `kf_channel_info` VALUES (1, 'KF_CHANNEL_XCX', '小程序', 'KF_ZBGX', '', '0', '2024-10-11 09:43:51', 'admin', '2024-11-18 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (2, 'KF_CHANNEL_GW', '官网', 'KF_ZBGX', NULL, '0', '2024-10-11 16:43:38', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (3, 'KF_CHANNEL_GZH', '公众号', 'KF_ZBGX', NULL, '0', '2024-10-11 16:47:40', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (4, 'KF_CHANNEL_KFTJ', '客服添加', 'KF_ZBGX', '', '0', '2024-10-11 16:47:59', 'admin', '2024-10-11 16:47:59', NULL);
INSERT INTO `kf_channel_info` VALUES (5, 'KF_CHANNEL_XCX', '小程序', 'KF_HNZH', '', '0', '2024-10-11 09:43:51', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (6, 'KF_CHANNEL_GW', '官网', 'KF_HNZH', NULL, '0', '2024-10-11 16:43:38', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (7, 'KF_CHANNEL_GZH', '公众号', 'KF_HNZH', NULL, '0', '2024-10-11 16:47:40', 'admin', '2024-10-11 16:47:39', NULL);
INSERT INTO `kf_channel_info` VALUES (8, 'KF_CHANNEL_KFTJ', '客服添加', 'KF_HNZH', '', '0', '2024-10-11 16:47:59', 'admin', '2024-10-11 16:47:59', NULL);
INSERT INTO `kf_channel_info` VALUES (9, 'KF_CHANNEL_XCX', '小程序', 'KF_QHCT', '', '0', '2024-10-11 09:43:51', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (10, 'KF_CHANNEL_GW', '官网', 'KF_QHCT', NULL, '0', '2024-10-11 16:43:38', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (11, 'KF_CHANNEL_GZH', '公众号', 'KF_QHCT', NULL, '0', '2024-10-11 16:47:40', 'admin', '2024-10-11 16:47:39', NULL);
INSERT INTO `kf_channel_info` VALUES (12, 'KF_CHANNEL_KFTJ', '客服添加', 'KF_QHCT', '', '0', '2024-10-11 16:47:59', 'admin', '2024-10-11 16:47:59', NULL);
INSERT INTO `kf_channel_info` VALUES (13, 'KF_CHANNEL_XCX', '小程序', 'KF_HBFC', '', '0', '2024-10-11 09:43:51', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (14, 'KF_CHANNEL_GW', '官网', 'KF_HBFC', NULL, '0', '2024-10-11 16:43:38', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (15, 'KF_CHANNEL_GZH', '公众号', 'KF_HBFC', NULL, '0', '2024-10-11 16:47:40', 'admin', '2024-10-11 16:47:39', NULL);
INSERT INTO `kf_channel_info` VALUES (16, 'KF_CHANNEL_KFTJ', '客服添加', 'KF_HBFC', '', '0', '2024-10-11 16:47:59', 'admin', '2024-10-11 16:47:59', NULL);
INSERT INTO `kf_channel_info` VALUES (17, 'KF_CHANNEL_XCX', '小程序', 'KF_YNDF', '', '0', '2024-10-11 09:43:51', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (18, 'KF_CHANNEL_GW', '官网', 'KF_YNDF', NULL, '0', '2024-10-11 16:43:38', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (19, 'KF_CHANNEL_GZH', '公众号', 'KF_YNDF', NULL, '0', '2024-10-11 16:47:40', 'admin', '2024-10-11 16:47:39', NULL);
INSERT INTO `kf_channel_info` VALUES (20, 'KF_CHANNEL_KFTJ', '客服添加', 'KF_YNDF', '', '0', '2024-10-11 16:47:59', 'admin', '2024-10-11 16:47:59', NULL);
INSERT INTO `kf_channel_info` VALUES (21, 'KF_CHANNEL_XCX', '小程序', 'KF_HNZT', '', '0', '2024-10-11 09:43:51', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (22, 'KF_CHANNEL_GW', '官网', 'KF_HNZT', NULL, '0', '2024-10-11 16:43:38', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (23, 'KF_CHANNEL_GZH', '公众号', 'KF_HNZT', NULL, '0', '2024-10-11 16:47:40', 'admin', '2024-10-11 16:47:39', NULL);
INSERT INTO `kf_channel_info` VALUES (24, 'KF_CHANNEL_KFTJ', '客服添加', 'KF_HNZT', '', '0', '2024-10-11 16:47:59', 'admin', '2024-10-11 16:47:59', NULL);
INSERT INTO `kf_channel_info` VALUES (25, 'KF_CHANNEL_XCX', '小程序', 'KF_FJDY', '', '0', '2024-10-11 09:43:51', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (26, 'KF_CHANNEL_GW', '官网', 'KF_FJDY', NULL, '0', '2024-10-11 16:43:38', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (27, 'KF_CHANNEL_GZH', '公众号', 'KF_FJDY', NULL, '0', '2024-10-11 16:47:40', 'admin', '2024-10-11 16:47:39', NULL);
INSERT INTO `kf_channel_info` VALUES (28, 'KF_CHANNEL_KFTJ', '客服添加', 'KF_FJDY', '', '0', '2024-10-11 16:47:59', 'admin', '2024-10-11 16:47:59', NULL);
INSERT INTO `kf_channel_info` VALUES (29, 'KF_CHANNEL_XCX', '小程序', 'KF_CQTR', '', '0', '2024-10-11 09:43:51', 'admin', '2025-01-21 10:26:28', NULL);
INSERT INTO `kf_channel_info` VALUES (30, 'KF_CHANNEL_GW', '官网', 'KF_CQTR', NULL, '0', '2024-10-11 16:43:38', 'admin', '2025-01-21 10:26:33', NULL);
INSERT INTO `kf_channel_info` VALUES (31, 'KF_CHANNEL_GZH', '公众号', 'KF_CQTR', NULL, '0', '2024-10-11 16:47:40', 'admin', '2025-01-21 10:26:35', NULL);
INSERT INTO `kf_channel_info` VALUES (32, 'KF_CHANNEL_KFTJ', '客服添加', 'KF_CQTR', '', '0', '2024-10-11 16:47:59', 'admin', '2025-01-21 10:26:39', NULL);
INSERT INTO `kf_channel_info` VALUES (36, 'KF_CHANNEL_XCX', '小程序', 'KF_FJCW', NULL, '0', '2025-05-09 13:54:27', 'admin', '2025-05-09 00:00:00', NULL);
INSERT INTO `kf_channel_info` VALUES (37, 'KF_CHANNEL_XCX', '小程序', 'KF_GSJD', NULL, '0', '2025-05-09 13:54:43', 'admin', '2025-05-09 13:54:43', NULL);

-- ----------------------------
-- Table structure for kf_company_info
-- ----------------------------
DROP TABLE IF EXISTS `kf_company_info`;
CREATE TABLE `kf_company_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `company_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公司编码',
  `company_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公司名称',
  `company_full_name` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公司全称',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注说明',
  `status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公司状态',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `last_update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `code_un`(`company_code` ASC) USING BTREE COMMENT '公司编码唯一索引'
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kf_company_info
-- ----------------------------
INSERT INTO `kf_company_info` VALUES (1, 'KF_CQTR', '重庆淘然', '重庆淘然融资担保有限公司', '重庆淘然融资担保有限公司', '0', '2024-10-11 16:43:03', 'admin', '2024-11-13 00:00:00', NULL);
INSERT INTO `kf_company_info` VALUES (2, 'KF_ZBGX', '中保国信', '中保国信融资担保有限公司', '中保国信融资担保有限公司', '0', '2024-10-10 16:56:37', 'admin', '2024-10-10 00:00:00', NULL);
INSERT INTO `kf_company_info` VALUES (3, 'KF_FJDY', '福建大有', '福建大有鼎盛融资担保有限公司', '福建大有鼎盛融资担保有限公司', '0', '2024-10-11 16:42:24', 'admin', '2024-11-13 00:00:00', NULL);
INSERT INTO `kf_company_info` VALUES (4, 'KF_YNDF', '云南鼎丰', '云南鼎丰融资担保有限公司', '云南鼎丰融资担保有限公司', '0', '2024-10-11 10:26:53', 'admin', '2024-10-11 00:00:00', NULL);
INSERT INTO `kf_company_info` VALUES (5, 'KF_QHCT', '青海昌泰', '青海昌泰融资担保有限公司', '青海昌泰融资担保有限公司', '0', '2024-10-11 16:40:31', 'admin', '2024-10-11 16:40:30', NULL);
INSERT INTO `kf_company_info` VALUES (6, 'KF_HNZH', '湖南樽昊', '湖南樽昊融资担保有限公司', '湖南樽昊融资担保有限公司', '0', '2024-10-11 16:40:59', 'admin', '2024-10-11 16:40:58', NULL);
INSERT INTO `kf_company_info` VALUES (7, 'KF_HBFC', '湖北富宸', '湖北富宸融资担保有限公司', '湖北富宸融资担保有限公司', '0', '2024-10-11 16:41:24', 'admin', '2024-10-11 16:41:23', NULL);
INSERT INTO `kf_company_info` VALUES (8, 'KF_HNZT', '海南正堂', '海南正堂融资担保有限公司', '海南正堂融资担保有限公司', '0', '2024-10-11 16:41:43', 'admin', '2024-10-11 16:41:43', NULL);
INSERT INTO `kf_company_info` VALUES (9, 'KF_FJCW', '福建创伟', '福建创伟国信融资担保有限公司', '福建创伟国信融资担保有限公司', '0', '2025-05-09 13:53:36', 'admin', '2025-05-09 00:00:00', NULL);
INSERT INTO `kf_company_info` VALUES (10, 'KF_GSJD', '甘肃嘉德', '甘肃嘉德融资担保有限公司', '甘肃嘉德融资担保有限公司', '0', '2025-05-09 13:53:52', 'admin', '2025-05-09 13:53:52', NULL);

-- ----------------------------
-- Table structure for kf_custom_update_info
-- ----------------------------
DROP TABLE IF EXISTS `kf_custom_update_info`;
CREATE TABLE `kf_custom_update_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `work_order_num` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工单编码',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户姓名',
  `user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户手机号',
  `user_id_card_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户身份证号',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `last_update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of kf_custom_update_info
-- ----------------------------
INSERT INTO `kf_custom_update_info` VALUES (1, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '123', '13200001111', '', '2025-01-20 14:18:31', 'admin', '2025-01-20 14:18:31', NULL);
INSERT INTO `kf_custom_update_info` VALUES (2, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '1', '', '', '2025-01-20 14:18:52', 'admin', '2025-01-20 14:18:51', NULL);
INSERT INTO `kf_custom_update_info` VALUES (3, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', 'ZHANGSAN', '', '', '2025-01-20 14:19:26', 'admin', '2025-01-20 14:19:25', NULL);
INSERT INTO `kf_custom_update_info` VALUES (4, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '', '15233331111', '', '2025-01-20 14:21:00', 'admin', '2025-01-20 14:20:59', NULL);
INSERT INTO `kf_custom_update_info` VALUES (5, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '张三', '', '', '2025-01-20 14:21:14', 'admin', '2025-01-20 14:21:13', NULL);
INSERT INTO `kf_custom_update_info` VALUES (6, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '李四', '', '231005199407195513', '2025-01-20 14:36:59', 'admin', '2025-01-20 14:36:58', NULL);
INSERT INTO `kf_custom_update_info` VALUES (7, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '1', '18989231121', '', '2025-01-20 14:40:40', 'admin', '2025-01-20 14:40:40', NULL);
INSERT INTO `kf_custom_update_info` VALUES (8, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '12', '18989231121', '231005199407195513', '2025-01-20 16:32:20', 'admin', '2025-01-20 16:32:20', NULL);
INSERT INTO `kf_custom_update_info` VALUES (9, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '张三', '18900001111', '410329197801195053', '2025-01-20 16:33:18', 'admin', '2025-01-20 16:33:17', NULL);
INSERT INTO `kf_custom_update_info` VALUES (10, '172950203164393184c2cb3fb49aaade5970c59ad47b9', '张1231', '***********', '', '2025-01-21 15:47:07', 'admin', '2025-01-21 15:47:07', NULL);
INSERT INTO `kf_custom_update_info` VALUES (11, '172950203164393184c2cb3fb49aaade5970c59ad47b9', '张1231121', '***********', '', '2025-01-21 15:47:18', 'admin', '2025-01-21 15:47:18', NULL);
INSERT INTO `kf_custom_update_info` VALUES (12, '1737445664914acb222a26205499b9ce2d77ce0a3ca7a', '1112', '***********', '', '2025-01-21 15:47:54', 'admin', '2025-01-21 15:47:54', NULL);

-- ----------------------------
-- Table structure for kf_file_info
-- ----------------------------
DROP TABLE IF EXISTS `kf_file_info`;
CREATE TABLE `kf_file_info`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `business_num` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '业务系统编码',
  `oss_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'oss文件路径',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '文件名称',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '创建人',
  `last_update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of kf_file_info
-- ----------------------------
INSERT INTO `kf_file_info` VALUES (1, '1734942131040e2a64c23466a4a3c810f98f9773e1de8', 'kfsystem/1734942131040e2a64c23466a4a3c810f98f9773e1de8/20241223162211/4932e72d-31fb-4eaf-a4de-2cb562491c9e', '222222.jpg', '2024-12-23 16:22:11', 'admin', '2024-12-23 16:22:11', NULL);
INSERT INTO `kf_file_info` VALUES (2, '1735097829642d2b0cdc502ea4ef3b2b54a0590daed37', 'kfsystem/1735097829642d2b0cdc502ea4ef3b2b54a0590daed37/20241225113709/fe587682-c486-4bc6-945b-44be0a7a361c', '屏幕截图 2024-04-11 151604.png', '2024-12-25 11:37:10', 'demo', '2024-12-25 11:37:09', NULL);
INSERT INTO `kf_file_info` VALUES (3, '1735194601846373773c52b2946f0bc04d981a657756b', 'kfsystem/1735194601846373773c52b2946f0bc04d981a657756b/20241226143001/79b8f6f5-a4d2-43fe-bd1a-58cb3c532f0b', 'qqqqqq.jpg', '2024-12-26 14:30:02', 'demo', '2024-12-26 14:30:02', NULL);
INSERT INTO `kf_file_info` VALUES (4, '1735194601846373773c52b2946f0bc04d981a657756b', 'kfsystem/1735194601846373773c52b2946f0bc04d981a657756b/20241226143002/14470db7-6ef3-4692-9740-58131846a54f', '22#22.pdf', '2024-12-26 14:30:02', 'demo', '2024-12-26 14:30:02', NULL);
INSERT INTO `kf_file_info` VALUES (5, '1735194601846373773c52b2946f0bc04d981a657756b', 'kfsystem/1735194601846373773c52b2946f0bc04d981a657756b/20241226143002/441cff84-92ec-4ce2-a6b4-5d705dd474fb', '111.docx', '2024-12-26 14:30:02', 'demo', '2024-12-26 14:30:02', NULL);
INSERT INTO `kf_file_info` VALUES (6, '1735200071305e73377def0cb46c8b22522f66d4f3427', 'kfsystem/1735200071305e73377def0cb46c8b22522f66d4f3427/20241226160111/6e876a19-f339-4f95-babe-510621204d7f', '镖人.png', '2024-12-26 16:01:11', 'demo', '2024-12-26 16:01:11', NULL);
INSERT INTO `kf_file_info` VALUES (7, '17368187534302b6774c40f60475f8316ad0f123b2f5a', 'kfsystem/17368187534302b6774c40f60475f8316ad0f123b2f5a/20250114093913/4680ba4c-2cf9-4cd5-8e89-4d0e229228de', '大奉打更人.jpg', '2025-01-14 09:39:14', 'admin', '2025-01-14 09:39:13', NULL);
INSERT INTO `kf_file_info` VALUES (8, '17368187534302b6774c40f60475f8316ad0f123b2f5a', 'kfsystem/17368187534302b6774c40f60475f8316ad0f123b2f5a/20250114093913/af8443c5-3982-46df-9a64-962922f3d6c9', '牧神记.jpg', '2025-01-14 09:39:14', 'admin', '2025-01-14 09:39:13', NULL);
INSERT INTO `kf_file_info` VALUES (9, '17368187534302b6774c40f60475f8316ad0f123b2f5a', 'kfsystem/17368187534302b6774c40f60475f8316ad0f123b2f5a/20250114093913/59a04476-71a8-4e4d-9491-c10197418b79', '长生界.jpg', '2025-01-14 09:39:14', 'admin', '2025-01-14 09:39:13', NULL);

-- ----------------------------
-- Table structure for kf_word_order_remark
-- ----------------------------
DROP TABLE IF EXISTS `kf_word_order_remark`;
CREATE TABLE `kf_word_order_remark`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `work_order_num` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工单编码',
  `remark_txt` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '客户备注信息',
  `status` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '0' COMMENT '逻辑删除状态（0生效、1删除）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `last_update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of kf_word_order_remark
-- ----------------------------
INSERT INTO `kf_word_order_remark` VALUES (47, '17373374680101300e630aec24c609447846c66a48381', '十多个第三方', '0', '2025-01-20 09:46:50', 'admin', '2025-01-20 09:46:49', NULL);
INSERT INTO `kf_word_order_remark` VALUES (48, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '111', '0', '2025-01-20 14:20:41', 'admin', '2025-01-20 14:20:41', NULL);
INSERT INTO `kf_word_order_remark` VALUES (49, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '22', '0', '2025-01-20 14:20:43', 'admin', '2025-01-20 14:20:43', NULL);
INSERT INTO `kf_word_order_remark` VALUES (50, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '333', '0', '2025-01-20 14:20:46', 'admin', '2025-01-20 14:20:45', NULL);

-- ----------------------------
-- Table structure for kf_work_order
-- ----------------------------
DROP TABLE IF EXISTS `kf_work_order`;
CREATE TABLE `kf_work_order`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `work_order_num` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工单编码',
  `company_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公司编码',
  `channel_num` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '渠道编码',
  `order_source_num` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工单来源编码',
  `user_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户姓名',
  `user_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户手机号',
  `user_id_card_num` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '客户身份证号',
  `work_order_text` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工单内容',
  `claim_user_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '认领人用户ID',
  `work_order_status` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工单状态（READY待认领、LOAD处理中、COMPLETE\n已完成、ABANDON不处理）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `last_update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 241 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '客服工单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of kf_work_order
-- ----------------------------
INSERT INTO `kf_work_order` VALUES (208, '172950203164393184c2cb3fb49aaade5970c59ad47b9', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KF', '张1231121', '***********', '', '这是一个工单内容', 'admin', 'ABANDON', '2024-10-21 17:13:52', 'admin', '2025-01-21 15:56:32', 'admin');
INSERT INTO `kf_work_order` VALUES (209, '1729502052797ec27b37650f44f1ab795167d354f009f', 'KF_ZBGX', 'KF_CHANNEL_GW', 'KF_SOURCE_KF', '', '13566662222', '', '这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容这是一个特别长的工单内容', 'demo', 'ABANDON', '2024-10-21 17:14:13', 'admin', '2024-10-31 13:40:55', 'demo');
INSERT INTO `kf_work_order` VALUES (210, '172950207369277e9ca42298148778135f2802d3ce31f', 'KF_ZBGX', 'KF_CHANNEL_DH', 'KF_SOURCE_KF', '', '15266663333', '', '这是一个特别长的工单内容', 'admin', 'COMPLETE', '2024-10-21 17:14:34', 'admin', '2024-10-21 17:19:51', 'admin');
INSERT INTO `kf_work_order` VALUES (211, '17303533267427a547f314e4b4c0cbce11e110d592b71', 'KF_ZBGX', 'KF_CHANNEL_GW', 'KF_SOURCE_KF', '张三', '13851212121', '130628199406201056', '这是一个比较急的客诉', 'demo', 'COMPLETE', '2024-10-31 13:42:07', 'demo', '2024-12-23 16:20:41', 'demo');
INSERT INTO `kf_work_order` VALUES (212, '1733987622343e7a16c6f71764f51b44a91101553b8da', 'KF_ZBGX', 'KF_CHANNEL_GW', 'KF_SOURCE_KF', '123', '', '', '123', 'admin', 'ABANDON', '2024-12-12 15:13:42', 'admin', '2025-01-21 15:56:23', 'admin');
INSERT INTO `kf_work_order` VALUES (213, '17343334093931cc5d567cd42443d8227a8cd19f7a3b4', 'KF_CQTR', 'KF_CHANNEL_GW', 'KF_SOURCE_KF', '123', '17898989223', '', '1231', '', 'READY', '2024-12-16 15:16:49', 'admin', '2024-12-25 11:23:26', 'admin');
INSERT INTO `kf_work_order` VALUES (214, '1734942131040e2a64c23466a4a3c810f98f9773e1de8', 'KF_CQTR', 'KF_CHANNEL_GZH', 'KF_SOURCE_KF', '张三', '13522221111', '', '1111111', 'admin', 'ABANDON', '2024-12-23 16:22:11', 'admin', '2024-12-25 11:23:01', 'admin');
INSERT INTO `kf_work_order` VALUES (215, '17349447489215e3780e30eed4bb8b13306f8e0ac049a', 'KF_ZBGX', 'KF_CHANNEL_GZH', 'KF_SOURCE_KF', '张三', '13522226666', '', '11111', 'demo', 'COMPLETE', '2024-12-23 17:05:49', 'demo', '2024-12-26 16:07:13', 'demo');
INSERT INTO `kf_work_order` VALUES (216, '17350977214134646db6b63a3497fa3566c825ed1c259', 'KF_CQTR', 'KF_CHANNEL_GW', 'KF_SOURCE_KF', 'zdr ', '17869696969', '', 'sdads', 'demo', 'LOAD', '2024-12-25 11:35:21', 'demo', '2024-12-25 11:35:30', 'demo');
INSERT INTO `kf_work_order` VALUES (217, '1735097829642d2b0cdc502ea4ef3b2b54a0590daed37', 'KF_CQTR', 'KF_CHANNEL_XCX', 'KF_SOURCE_KF', '付航', '15623456789', '', '没啥事', 'demo', 'LOAD', '2024-12-25 11:37:10', 'demo', '2024-12-25 14:04:11', 'demo');
INSERT INTO `kf_work_order` VALUES (218, '1735194601846373773c52b2946f0bc04d981a657756b', 'KF_CQTR', 'KF_CHANNEL_GZH', 'KF_SOURCE_KF', '张三', '15233332222', '', '11111111111111111', NULL, 'READY', '2024-12-26 14:30:02', 'demo', '2024-12-26 14:30:01', NULL);
INSERT INTO `kf_work_order` VALUES (219, '1735200071305e73377def0cb46c8b22522f66d4f3427', 'KF_CQTR', 'KF_CHANNEL_GZH', 'KF_SOURCE_KF', 'lisi', '15233332222', '', '111111232131232131231', 'admin', 'LOAD', '2024-12-26 16:01:11', 'demo', '2024-12-26 17:51:52', 'admin');
INSERT INTO `kf_work_order` VALUES (220, '17352062791084d172bf0bfd34b159e20a578c42a8e11', 'KF_CQTR', 'KF_CHANNEL_GW', 'KF_SOURCE_KF', 'zhangsan', '13522220000', '', '111111111111111内容', NULL, 'READY', '2024-12-26 17:44:39', 'demo', '2024-12-26 17:44:39', NULL);
INSERT INTO `kf_work_order` VALUES (221, '173521216095677a35625403d40429f25e798c6295ed3', 'KF_CQTR', 'KF_CHANNEL_XCX', 'KF_SOURCE_KF', 'zhangsan', '15233332222', '', '11111111111111', NULL, 'READY', '2024-12-26 19:22:41', 'demo', '2024-12-26 19:22:40', NULL);
INSERT INTO `kf_work_order` VALUES (222, '17368187534302b6774c40f60475f8316ad0f123b2f5a', 'KF_CQTR', 'KF_CHANNEL_GZH', 'KF_SOURCE_KF', '张三', '15233332222', '', '1111111', 'admin', 'LOAD', '2025-01-14 09:39:13', 'admin', '2025-01-14 09:39:57', 'admin');
INSERT INTO `kf_work_order` VALUES (223, '17373374680101300e630aec24c609447846c66a48381', 'KF_HNZT', 'KF_CHANNEL_GW', 'KF_SOURCE_KF', '测试mm', '13673333333', '', '阿斯顿发斯蒂芬', 'admin', 'COMPLETE', '2025-01-20 09:44:28', 'admin', '2025-01-20 09:47:00', 'admin');
INSERT INTO `kf_work_order` VALUES (224, '1737342720978e449694ca2ee451cb9eb99cd0738003b', 'KF_HNZH', 'KF_CHANNEL_GZH', 'KF_SOURCE_KF', 'sadfa', '13691231234', '', 'asdfasdf ', NULL, 'READY', '2025-01-20 11:12:01', 'admin', '2025-01-20 11:12:00', NULL);
INSERT INTO `kf_work_order` VALUES (225, '1737342775835dc099587599a47bdb48a4479069c09dc', 'KF_HNZH', 'KF_CHANNEL_KFTJ', 'KF_SOURCE_KF', 'fghfg', '13691231234', '', 'asdfassfas ', NULL, 'READY', '2025-01-20 11:12:56', 'admin', '2025-01-20 11:12:55', NULL);
INSERT INTO `kf_work_order` VALUES (226, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', 'KF_HNZT', 'KF_CHANNEL_KFTJ', 'KF_SOURCE_KF', '张三', '18900001111', '410329197801195053', '1111111111111111111111', 'admin', 'LOAD', '2025-01-20 14:18:17', 'admin', '2025-01-20 16:33:18', 'admin');
INSERT INTO `kf_work_order` VALUES (227, '1737422971869e461108ea4a54ba3bc32e76b3a4d7ec9', 'KF_CQTR', 'KF_CHANNEL_QT', 'KF_SOURCE_KF', '吴彦方', '', '430281198403127352', '我天天想着你，并且开开细心大师傅黄金卡顺丰航空久啊手机卡等哈杰卡斯东方红郡卡是否还是见客户见客户尽快发货数据库的话房间卡阿萨德会尽快发哈数据库的黄金卡是否就喀什金卡时间', NULL, 'READY', '2025-01-21 09:29:32', 'admin', '2025-01-21 09:29:31', NULL);
INSERT INTO `kf_work_order` VALUES (228, '17374233165106bbade0390254fdf8cb0475e51aef2da', 'KF_CQTR', 'KF_CHANNEL_QT', 'KF_SOURCE_KF', '吴彦方', '17892319231', '', '没事闲的', NULL, 'READY', '2025-01-21 09:35:17', 'admin', '2025-01-21 09:35:16', NULL);
INSERT INTO `kf_work_order` VALUES (229, '1737423364936a16ae6e5b2c74aa8967bb97f6e3a1ace', 'KF_CQTR', 'KF_CHANNEL_QT', 'KF_SOURCE_KF', '吴彦方', '15913122321', '', '节流阀蓝思科技付款了手机卡拉法基考拉随机发考拉了拉屎荆防颗粒金石可镂荆防颗粒双节快乐金坷垃时间考拉双节快乐久啊上课了', NULL, 'READY', '2025-01-21 09:36:05', 'admin', '2025-01-21 09:36:04', NULL);
INSERT INTO `kf_work_order` VALUES (230, '1737445664914acb222a26205499b9ce2d77ce0a3ca7a', 'KF_ZBGX', 'KF_CHANNEL_KFTJ', 'KF_SOURCE_KF', '1112', '***********', '', '1213', 'admin', 'ABANDON', '2025-01-21 15:47:45', 'admin', '2025-01-21 15:56:28', 'admin');
INSERT INTO `kf_work_order` VALUES (231, '17467620825542f367592fab9451b8b6ba2a9d326a87a', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KF', '任冬冬', '17890992922', '411326198812112424', '开开西悉尼', NULL, 'READY', '2025-05-09 11:41:23', 'admin', '2025-05-09 11:41:22', NULL);
INSERT INTO `kf_work_order` VALUES (232, '1746776024103c6e0faad877941979ef2531c85990819', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KH', '陈国武', '17707244243', '429005199901065653', '1212', NULL, 'READY', '2025-05-09 15:33:44', 'default', '2025-05-09 15:33:44', NULL);
INSERT INTO `kf_work_order` VALUES (233, '1746776193019d7597e4e960a4d5da6a8df24eb2e9608', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KH', '陈国武', '17707244243', '429005199901065653', '我来测试小程序', NULL, 'READY', '2025-05-09 15:36:33', 'default', '2025-05-09 15:36:33', NULL);
INSERT INTO `kf_work_order` VALUES (234, '17467767754084ee13d6e90744007b3b32f2cc50d039c', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KH', '陈国武', '17707244243', '429005199901065653', '小程序反馈', NULL, 'READY', '2025-05-09 15:46:15', 'default', '2025-05-09 15:46:15', NULL);
INSERT INTO `kf_work_order` VALUES (235, '17470180358455a4ed0eb230c4977ac8ea42a6dd7017e', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KH', '周伟', '15871162749', '420281199609152011', '我是周伟', NULL, 'READY', '2025-05-12 10:47:16', 'default', '2025-05-12 10:47:15', NULL);
INSERT INTO `kf_work_order` VALUES (236, '174703221245661808e4fc8b24c72a15487af67641e76', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KH', '周伟', '15871162749', '420281199609152011', '2121', NULL, 'READY', '2025-05-12 14:43:32', 'default', '2025-05-12 14:43:32', NULL);
INSERT INTO `kf_work_order` VALUES (237, '174720423657108c73249c9ff48eb9cbe4c3037e6f93e', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KH', '陈国武', '17707244243', '429005199901065653', '212', 'admin', 'LOAD', '2025-05-14 14:30:37', 'default', '2025-05-16 10:50:55', 'admin');
INSERT INTO `kf_work_order` VALUES (238, '1747641479371305873404ecb42028d9bd06654afd70c', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KH', '张赫', '15835962866', '142733199602191211', '123321', NULL, 'READY', '2025-05-19 15:57:59', 'default', '2025-05-19 15:57:59', NULL);
INSERT INTO `kf_work_order` VALUES (239, '174764148625521718b7b2b0449c1a4dca85df1e20292', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KH', '张赫', '15835962866', '142733199602191211', '123321', NULL, 'READY', '2025-05-19 15:58:06', 'default', '2025-05-19 15:58:06', NULL);
INSERT INTO `kf_work_order` VALUES (240, '1747641493572bbbcd04e34d74d5abf2c37fadbe10d41', 'KF_ZBGX', 'KF_CHANNEL_XCX', 'KF_SOURCE_KH', '张赫', '15835962866', '142733199602191211', '321123', NULL, 'READY', '2025-05-19 15:58:14', 'default', '2025-05-19 15:58:13', NULL);

-- ----------------------------
-- Table structure for kf_work_order_record
-- ----------------------------
DROP TABLE IF EXISTS `kf_work_order_record`;
CREATE TABLE `kf_work_order_record`  (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `work_order_num` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '工单编码',
  `work_record` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL COMMENT '工作记录',
  `work_option_type` int NOT NULL COMMENT '工作人操作类型（1提交工单、2认领工单、3放弃认领、4发布工作记录、5不处理、6完成工单）',
  `status` int NOT NULL DEFAULT 0 COMMENT '逻辑删除状态（0生效、1删除）',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `create_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '创建人',
  `last_update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后修改时间',
  `update_by` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '修改人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 400 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of kf_work_order_record
-- ----------------------------
INSERT INTO `kf_work_order_record` VALUES (329, '172950203164393184c2cb3fb49aaade5970c59ad47b9', NULL, 1, 0, '2024-10-21 17:13:52', 'admin', '2024-10-21 17:13:51', NULL);
INSERT INTO `kf_work_order_record` VALUES (330, '1729502052797ec27b37650f44f1ab795167d354f009f', NULL, 1, 0, '2024-10-21 17:14:13', 'admin', '2024-10-21 17:14:12', NULL);
INSERT INTO `kf_work_order_record` VALUES (331, '172950207369277e9ca42298148778135f2802d3ce31f', NULL, 1, 0, '2024-10-21 17:14:34', 'admin', '2024-10-21 17:14:33', NULL);
INSERT INTO `kf_work_order_record` VALUES (332, '172950207369277e9ca42298148778135f2802d3ce31f', NULL, 2, 0, '2024-10-21 17:19:25', 'admin', '2024-10-21 17:19:24', NULL);
INSERT INTO `kf_work_order_record` VALUES (333, '172950207369277e9ca42298148778135f2802d3ce31f', 'sdfghj', 4, 0, '2024-10-21 17:19:42', 'admin', '2024-10-21 17:19:41', NULL);
INSERT INTO `kf_work_order_record` VALUES (334, '172950207369277e9ca42298148778135f2802d3ce31f', NULL, 6, 0, '2024-10-21 17:19:51', 'admin', '2024-10-21 17:19:50', NULL);
INSERT INTO `kf_work_order_record` VALUES (335, '1729502052797ec27b37650f44f1ab795167d354f009f', NULL, 2, 0, '2024-10-31 13:40:20', 'demo', '2024-10-31 13:40:19', NULL);
INSERT INTO `kf_work_order_record` VALUES (336, '1729502052797ec27b37650f44f1ab795167d354f009f', NULL, 5, 0, '2024-10-31 13:40:55', 'demo', '2024-10-31 13:40:55', NULL);
INSERT INTO `kf_work_order_record` VALUES (337, '17303533267427a547f314e4b4c0cbce11e110d592b71', NULL, 1, 0, '2024-10-31 13:42:07', 'demo', '2024-10-31 13:42:06', NULL);
INSERT INTO `kf_work_order_record` VALUES (338, '17303533267427a547f314e4b4c0cbce11e110d592b71', NULL, 2, 0, '2024-10-31 15:56:10', 'demo', '2024-10-31 15:56:09', NULL);
INSERT INTO `kf_work_order_record` VALUES (339, '1733987622343e7a16c6f71764f51b44a91101553b8da', NULL, 1, 0, '2024-12-12 15:13:42', 'admin', '2024-12-12 15:13:42', NULL);
INSERT INTO `kf_work_order_record` VALUES (340, '172950203164393184c2cb3fb49aaade5970c59ad47b9', NULL, 2, 0, '2024-12-12 15:14:07', 'admin', '2024-12-12 15:14:06', NULL);
INSERT INTO `kf_work_order_record` VALUES (341, '17343334093931cc5d567cd42443d8227a8cd19f7a3b4', NULL, 1, 0, '2024-12-16 15:16:49', 'admin', '2024-12-16 15:16:49', NULL);
INSERT INTO `kf_work_order_record` VALUES (342, '17343334093931cc5d567cd42443d8227a8cd19f7a3b4', NULL, 2, 0, '2024-12-16 15:17:00', 'admin', '2024-12-16 15:17:00', NULL);
INSERT INTO `kf_work_order_record` VALUES (343, '17303533267427a547f314e4b4c0cbce11e110d592b71', NULL, 6, 0, '2024-12-23 16:20:41', 'demo', '2024-12-23 16:20:40', NULL);
INSERT INTO `kf_work_order_record` VALUES (344, '1734942131040e2a64c23466a4a3c810f98f9773e1de8', NULL, 1, 0, '2024-12-23 16:22:11', 'admin', '2024-12-23 16:22:11', NULL);
INSERT INTO `kf_work_order_record` VALUES (345, '1734942131040e2a64c23466a4a3c810f98f9773e1de8', NULL, 2, 0, '2024-12-23 16:23:22', 'admin', '2024-12-23 16:23:22', NULL);
INSERT INTO `kf_work_order_record` VALUES (346, '17349447489215e3780e30eed4bb8b13306f8e0ac049a', NULL, 1, 0, '2024-12-23 17:05:49', 'demo', '2024-12-23 17:05:48', NULL);
INSERT INTO `kf_work_order_record` VALUES (347, '17349447489215e3780e30eed4bb8b13306f8e0ac049a', NULL, 2, 0, '2024-12-23 17:06:33', 'demo', '2024-12-23 17:06:33', NULL);
INSERT INTO `kf_work_order_record` VALUES (348, '1733987622343e7a16c6f71764f51b44a91101553b8da', NULL, 2, 0, '2024-12-25 11:09:52', 'demo', '2024-12-25 11:09:52', NULL);
INSERT INTO `kf_work_order_record` VALUES (349, '1733987622343e7a16c6f71764f51b44a91101553b8da', NULL, 3, 0, '2024-12-25 11:09:56', 'demo', '2024-12-25 11:09:55', NULL);
INSERT INTO `kf_work_order_record` VALUES (350, '1734942131040e2a64c23466a4a3c810f98f9773e1de8', NULL, 5, 0, '2024-12-25 11:23:01', 'admin', '2024-12-25 11:23:01', NULL);
INSERT INTO `kf_work_order_record` VALUES (351, '17343334093931cc5d567cd42443d8227a8cd19f7a3b4', NULL, 3, 0, '2024-12-25 11:23:26', 'admin', '2024-12-25 11:23:26', NULL);
INSERT INTO `kf_work_order_record` VALUES (352, '17350977214134646db6b63a3497fa3566c825ed1c259', NULL, 1, 0, '2024-12-25 11:35:21', 'demo', '2024-12-25 11:35:21', NULL);
INSERT INTO `kf_work_order_record` VALUES (353, '17350977214134646db6b63a3497fa3566c825ed1c259', NULL, 2, 0, '2024-12-25 11:35:30', 'demo', '2024-12-25 11:35:30', NULL);
INSERT INTO `kf_work_order_record` VALUES (354, '1735097829642d2b0cdc502ea4ef3b2b54a0590daed37', NULL, 1, 0, '2024-12-25 11:37:10', 'demo', '2024-12-25 11:37:09', NULL);
INSERT INTO `kf_work_order_record` VALUES (355, '1735097829642d2b0cdc502ea4ef3b2b54a0590daed37', NULL, 2, 0, '2024-12-25 14:04:11', 'demo', '2024-12-25 14:04:10', NULL);
INSERT INTO `kf_work_order_record` VALUES (356, '1735194601846373773c52b2946f0bc04d981a657756b', NULL, 1, 0, '2024-12-26 14:30:02', 'demo', '2024-12-26 14:30:01', NULL);
INSERT INTO `kf_work_order_record` VALUES (357, '1735200071305e73377def0cb46c8b22522f66d4f3427', NULL, 1, 0, '2024-12-26 16:01:11', 'demo', '2024-12-26 16:01:11', NULL);
INSERT INTO `kf_work_order_record` VALUES (358, '17349447489215e3780e30eed4bb8b13306f8e0ac049a', NULL, 6, 0, '2024-12-26 16:07:13', 'demo', '2024-12-26 16:07:13', NULL);
INSERT INTO `kf_work_order_record` VALUES (359, '17352062791084d172bf0bfd34b159e20a578c42a8e11', NULL, 1, 0, '2024-12-26 17:44:39', 'demo', '2024-12-26 17:44:39', NULL);
INSERT INTO `kf_work_order_record` VALUES (360, '1735200071305e73377def0cb46c8b22522f66d4f3427', NULL, 2, 0, '2024-12-26 17:51:52', 'admin', '2024-12-26 17:51:52', NULL);
INSERT INTO `kf_work_order_record` VALUES (361, '1735200071305e73377def0cb46c8b22522f66d4f3427', '1111111', 4, 0, '2024-12-26 17:51:59', 'admin', '2024-12-26 17:51:58', NULL);
INSERT INTO `kf_work_order_record` VALUES (362, '1735200071305e73377def0cb46c8b22522f66d4f3427', '2222', 4, 0, '2024-12-26 17:52:01', 'admin', '2024-12-26 17:52:01', NULL);
INSERT INTO `kf_work_order_record` VALUES (363, '173521216095677a35625403d40429f25e798c6295ed3', NULL, 1, 0, '2024-12-26 19:22:41', 'demo', '2024-12-26 19:22:40', NULL);
INSERT INTO `kf_work_order_record` VALUES (364, '17368187534302b6774c40f60475f8316ad0f123b2f5a', NULL, 1, 0, '2025-01-14 09:39:13', 'admin', '2025-01-14 09:39:13', NULL);
INSERT INTO `kf_work_order_record` VALUES (365, '17368187534302b6774c40f60475f8316ad0f123b2f5a', NULL, 2, 0, '2025-01-14 09:39:47', 'admin', '2025-01-14 09:39:46', NULL);
INSERT INTO `kf_work_order_record` VALUES (366, '17368187534302b6774c40f60475f8316ad0f123b2f5a', NULL, 3, 0, '2025-01-14 09:39:53', 'admin', '2025-01-14 09:39:52', NULL);
INSERT INTO `kf_work_order_record` VALUES (367, '17368187534302b6774c40f60475f8316ad0f123b2f5a', NULL, 2, 0, '2025-01-14 09:39:57', 'admin', '2025-01-14 09:39:56', NULL);
INSERT INTO `kf_work_order_record` VALUES (368, '17373374680101300e630aec24c609447846c66a48381', NULL, 1, 0, '2025-01-20 09:44:28', 'admin', '2025-01-20 09:44:28', NULL);
INSERT INTO `kf_work_order_record` VALUES (369, '17373374680101300e630aec24c609447846c66a48381', NULL, 2, 0, '2025-01-20 09:45:16', 'admin', '2025-01-20 09:45:15', NULL);
INSERT INTO `kf_work_order_record` VALUES (370, '17373374680101300e630aec24c609447846c66a48381', NULL, 6, 0, '2025-01-20 09:47:00', 'admin', '2025-01-20 09:46:59', NULL);
INSERT INTO `kf_work_order_record` VALUES (371, '1737342720978e449694ca2ee451cb9eb99cd0738003b', NULL, 1, 0, '2025-01-20 11:12:01', 'admin', '2025-01-20 11:12:00', NULL);
INSERT INTO `kf_work_order_record` VALUES (372, '1737342775835dc099587599a47bdb48a4479069c09dc', NULL, 1, 0, '2025-01-20 11:12:56', 'admin', '2025-01-20 11:12:55', NULL);
INSERT INTO `kf_work_order_record` VALUES (373, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', NULL, 1, 0, '2025-01-20 14:18:17', 'admin', '2025-01-20 14:18:17', NULL);
INSERT INTO `kf_work_order_record` VALUES (374, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', NULL, 2, 0, '2025-01-20 14:18:23', 'admin', '2025-01-20 14:18:22', NULL);
INSERT INTO `kf_work_order_record` VALUES (375, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '111', 4, 0, '2025-01-20 14:20:30', 'admin', '2025-01-20 14:20:29', NULL);
INSERT INTO `kf_work_order_record` VALUES (376, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '222', 4, 0, '2025-01-20 14:20:32', 'admin', '2025-01-20 14:20:31', NULL);
INSERT INTO `kf_work_order_record` VALUES (377, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', '333', 4, 0, '2025-01-20 14:20:34', 'admin', '2025-01-20 14:20:34', NULL);
INSERT INTO `kf_work_order_record` VALUES (378, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', NULL, 3, 0, '2025-01-20 14:37:14', 'admin', '2025-01-20 14:37:13', NULL);
INSERT INTO `kf_work_order_record` VALUES (379, '1737353897398b62c8c3afb7c428fb4d5370574f9c40e', NULL, 2, 0, '2025-01-20 14:37:20', 'admin', '2025-01-20 14:37:20', NULL);
INSERT INTO `kf_work_order_record` VALUES (380, '1737422971869e461108ea4a54ba3bc32e76b3a4d7ec9', NULL, 1, 0, '2025-01-21 09:29:32', 'admin', '2025-01-21 09:29:31', NULL);
INSERT INTO `kf_work_order_record` VALUES (381, '17374233165106bbade0390254fdf8cb0475e51aef2da', NULL, 1, 0, '2025-01-21 09:35:17', 'admin', '2025-01-21 09:35:16', NULL);
INSERT INTO `kf_work_order_record` VALUES (382, '1737423364936a16ae6e5b2c74aa8967bb97f6e3a1ace', NULL, 1, 0, '2025-01-21 09:36:05', 'admin', '2025-01-21 09:36:04', NULL);
INSERT INTO `kf_work_order_record` VALUES (383, '1737445664914acb222a26205499b9ce2d77ce0a3ca7a', NULL, 1, 0, '2025-01-21 15:47:45', 'admin', '2025-01-21 15:47:44', NULL);
INSERT INTO `kf_work_order_record` VALUES (384, '1737445664914acb222a26205499b9ce2d77ce0a3ca7a', NULL, 2, 0, '2025-01-21 15:47:52', 'admin', '2025-01-21 15:47:52', NULL);
INSERT INTO `kf_work_order_record` VALUES (385, '1733987622343e7a16c6f71764f51b44a91101553b8da', NULL, 2, 0, '2025-01-21 15:56:21', 'admin', '2025-01-21 15:56:20', NULL);
INSERT INTO `kf_work_order_record` VALUES (386, '1733987622343e7a16c6f71764f51b44a91101553b8da', NULL, 5, 0, '2025-01-21 15:56:23', 'admin', '2025-01-21 15:56:22', NULL);
INSERT INTO `kf_work_order_record` VALUES (387, '1737445664914acb222a26205499b9ce2d77ce0a3ca7a', NULL, 5, 0, '2025-01-21 15:56:28', 'admin', '2025-01-21 15:56:28', NULL);
INSERT INTO `kf_work_order_record` VALUES (388, '172950203164393184c2cb3fb49aaade5970c59ad47b9', NULL, 5, 0, '2025-01-21 15:56:32', 'admin', '2025-01-21 15:56:32', NULL);
INSERT INTO `kf_work_order_record` VALUES (389, '17467620825542f367592fab9451b8b6ba2a9d326a87a', NULL, 1, 0, '2025-05-09 11:41:23', 'admin', '2025-05-09 11:41:22', NULL);
INSERT INTO `kf_work_order_record` VALUES (390, '1746776024103c6e0faad877941979ef2531c85990819', NULL, 1, 0, '2025-05-09 15:33:44', 'default', '2025-05-09 15:33:44', NULL);
INSERT INTO `kf_work_order_record` VALUES (391, '1746776193019d7597e4e960a4d5da6a8df24eb2e9608', NULL, 1, 0, '2025-05-09 15:36:33', 'default', '2025-05-09 15:36:33', NULL);
INSERT INTO `kf_work_order_record` VALUES (392, '17467767754084ee13d6e90744007b3b32f2cc50d039c', NULL, 1, 0, '2025-05-09 15:46:15', 'default', '2025-05-09 15:46:15', NULL);
INSERT INTO `kf_work_order_record` VALUES (393, '17470180358455a4ed0eb230c4977ac8ea42a6dd7017e', NULL, 1, 0, '2025-05-12 10:47:16', 'default', '2025-05-12 10:47:15', NULL);
INSERT INTO `kf_work_order_record` VALUES (394, '174703221245661808e4fc8b24c72a15487af67641e76', NULL, 1, 0, '2025-05-12 14:43:32', 'default', '2025-05-12 14:43:32', NULL);
INSERT INTO `kf_work_order_record` VALUES (395, '174720423657108c73249c9ff48eb9cbe4c3037e6f93e', NULL, 1, 0, '2025-05-14 14:30:37', 'default', '2025-05-14 14:30:36', NULL);
INSERT INTO `kf_work_order_record` VALUES (396, '174720423657108c73249c9ff48eb9cbe4c3037e6f93e', NULL, 2, 0, '2025-05-16 10:50:55', 'admin', '2025-05-16 10:50:55', NULL);
INSERT INTO `kf_work_order_record` VALUES (397, '1747641479371305873404ecb42028d9bd06654afd70c', NULL, 1, 0, '2025-05-19 15:57:59', 'default', '2025-05-19 15:57:59', NULL);
INSERT INTO `kf_work_order_record` VALUES (398, '174764148625521718b7b2b0449c1a4dca85df1e20292', NULL, 1, 0, '2025-05-19 15:58:06', 'default', '2025-05-19 15:58:06', NULL);
INSERT INTO `kf_work_order_record` VALUES (399, '1747641493572bbbcd04e34d74d5abf2c37fadbe10d41', NULL, 1, 0, '2025-05-19 15:58:14', 'default', '2025-05-19 15:58:13', NULL);

-- ----------------------------
-- Table structure for qrtz_blob_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_blob_triggers`;
CREATE TABLE `qrtz_blob_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `blob_data` blob NULL COMMENT '存放持久化Trigger对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_blob_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'Blob类型的触发器表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_blob_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_calendars
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_calendars`;
CREATE TABLE `qrtz_calendars`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '日历名称',
  `calendar` blob NOT NULL COMMENT '存放持久化calendar对象',
  PRIMARY KEY (`sched_name`, `calendar_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '日历信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_calendars
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_cron_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_cron_triggers`;
CREATE TABLE `qrtz_cron_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `cron_expression` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'cron表达式',
  `time_zone_id` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '时区',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_cron_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = 'Cron类型的触发器表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_cron_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_fired_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_fired_triggers`;
CREATE TABLE `qrtz_fired_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `entry_id` varchar(95) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度器实例id',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度器实例名',
  `fired_time` bigint NOT NULL COMMENT '触发的时间',
  `sched_time` bigint NOT NULL COMMENT '定时器制定的时间',
  `priority` int NOT NULL COMMENT '优先级',
  `state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '任务组名',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否并发',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '是否接受恢复执行',
  PRIMARY KEY (`sched_name`, `entry_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '已触发的触发器表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_fired_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_job_details
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_job_details`;
CREATE TABLE `qrtz_job_details`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `job_class_name` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '执行任务类名称',
  `is_durable` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否持久化',
  `is_nonconcurrent` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否并发',
  `is_update_data` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否更新数据',
  `requests_recovery` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '是否接受恢复执行',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '任务详细信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_job_details
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_locks
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_locks`;
CREATE TABLE `qrtz_locks`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `lock_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '悲观锁名称',
  PRIMARY KEY (`sched_name`, `lock_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '存储的悲观锁信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_locks
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_paused_trigger_grps
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_paused_trigger_grps`;
CREATE TABLE `qrtz_paused_trigger_grps`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  PRIMARY KEY (`sched_name`, `trigger_group`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '暂停的触发器表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_paused_trigger_grps
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_scheduler_state
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_scheduler_state`;
CREATE TABLE `qrtz_scheduler_state`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `instance_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '实例名称',
  `last_checkin_time` bigint NOT NULL COMMENT '上次检查时间',
  `checkin_interval` bigint NOT NULL COMMENT '检查间隔时间',
  PRIMARY KEY (`sched_name`, `instance_name`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '调度器状态表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_scheduler_state
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simple_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simple_triggers`;
CREATE TABLE `qrtz_simple_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `repeat_count` bigint NOT NULL COMMENT '重复的次数统计',
  `repeat_interval` bigint NOT NULL COMMENT '重复的间隔时间',
  `times_triggered` bigint NOT NULL COMMENT '已经触发的次数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simple_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '简单触发器的信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_simple_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_simprop_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_simprop_triggers`;
CREATE TABLE `qrtz_simprop_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_name的外键',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_triggers表trigger_group的外键',
  `str_prop_1` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第一个参数',
  `str_prop_2` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第二个参数',
  `str_prop_3` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'String类型的trigger的第三个参数',
  `int_prop_1` int NULL DEFAULT NULL COMMENT 'int类型的trigger的第一个参数',
  `int_prop_2` int NULL DEFAULT NULL COMMENT 'int类型的trigger的第二个参数',
  `long_prop_1` bigint NULL DEFAULT NULL COMMENT 'long类型的trigger的第一个参数',
  `long_prop_2` bigint NULL DEFAULT NULL COMMENT 'long类型的trigger的第二个参数',
  `dec_prop_1` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第一个参数',
  `dec_prop_2` decimal(13, 4) NULL DEFAULT NULL COMMENT 'decimal类型的trigger的第二个参数',
  `bool_prop_1` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第一个参数',
  `bool_prop_2` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT 'Boolean类型的trigger的第二个参数',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  CONSTRAINT `qrtz_simprop_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `trigger_name`, `trigger_group`) REFERENCES `qrtz_triggers` (`sched_name`, `trigger_name`, `trigger_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '同步机制的行锁表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_simprop_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for qrtz_triggers
-- ----------------------------
DROP TABLE IF EXISTS `qrtz_triggers`;
CREATE TABLE `qrtz_triggers`  (
  `sched_name` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调度名称',
  `trigger_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器的名字',
  `trigger_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器所属组的名字',
  `job_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_job_details表job_name的外键',
  `job_group` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'qrtz_job_details表job_group的外键',
  `description` varchar(250) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '相关介绍',
  `next_fire_time` bigint NULL DEFAULT NULL COMMENT '上一次触发时间（毫秒）',
  `prev_fire_time` bigint NULL DEFAULT NULL COMMENT '下一次触发时间（默认为-1表示不触发）',
  `priority` int NULL DEFAULT NULL COMMENT '优先级',
  `trigger_state` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器状态',
  `trigger_type` varchar(8) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '触发器的类型',
  `start_time` bigint NOT NULL COMMENT '开始时间',
  `end_time` bigint NULL DEFAULT NULL COMMENT '结束时间',
  `calendar_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日程表名称',
  `misfire_instr` smallint NULL DEFAULT NULL COMMENT '补偿执行的策略',
  `job_data` blob NULL COMMENT '存放持久化job对象',
  PRIMARY KEY (`sched_name`, `trigger_name`, `trigger_group`) USING BTREE,
  INDEX `sched_name`(`sched_name` ASC, `job_name` ASC, `job_group` ASC) USING BTREE,
  CONSTRAINT `qrtz_triggers_ibfk_1` FOREIGN KEY (`sched_name`, `job_name`, `job_group`) REFERENCES `qrtz_job_details` (`sched_name`, `job_name`, `job_group`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '触发器详细信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of qrtz_triggers
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` int NOT NULL AUTO_INCREMENT COMMENT '参数主键',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '参数配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 'admin', '2024-09-27 13:47:04', '', NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2024-09-27 13:47:04', '', NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2024-09-27 13:47:04', '', NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (4, '账号自助-验证码开关', 'sys.account.captchaEnabled', 'true', 'Y', 'admin', '2024-09-27 13:47:04', '', NULL, '是否开启验证码功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (5, '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 'admin', '2024-09-27 13:47:04', '', NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (6, '用户登录-黑名单列表', 'sys.login.blackIPList', '', 'Y', 'admin', '2024-09-27 13:47:04', '', NULL, '设置登录IP黑名单限制，多个匹配项以;分隔，支持匹配（*通配、网段）');
INSERT INTO `sys_config` VALUES (100, '客服人员内部展示脱敏数据', 'sys.kf.tmData', 'true', 'Y', 'admin', '2024-10-12 15:33:38', 'admin', '2024-10-18 15:47:49', '客服人员内部展示脱敏数据（true进行脱敏，false或者其他不需要脱敏）');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint NOT NULL AUTO_INCREMENT COMMENT '部门id',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '邮箱',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 201 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, 0, '0', '客服管理系统TOP', 0, '马朔', '', '', '0', '0', 'admin', '2024-09-27 13:47:03', 'admin', '2024-09-27 16:35:11');
INSERT INTO `sys_dept` VALUES (200, 100, '0,100', '客服部', 1, '马朔', NULL, NULL, '0', '0', 'admin', '2024-09-27 16:35:22', '', NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint NOT NULL AUTO_INCREMENT COMMENT '字典编码',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 143 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (8, 1, '正常', '0', 'sys_job_status', '', 'primary', 'Y', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (9, 2, '暂停', '1', 'sys_job_status', '', 'danger', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (10, 1, '默认', 'DEFAULT', 'sys_job_group', '', '', 'Y', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '默认分组');
INSERT INTO `sys_dict_data` VALUES (11, 2, '系统', 'SYSTEM', 'sys_job_group', '', '', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '系统分组');
INSERT INTO `sys_dict_data` VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (19, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (20, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (21, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (22, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (23, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (24, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (25, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (26, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (27, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (28, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (29, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2024-09-27 13:47:04', '', NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (100, 0, '中保国信', 'KF_ZBGX', 'kf_company_list', NULL, 'default', 'N', '0', 'admin', '2024-09-29 11:21:48', 'admin', '2024-10-08 09:47:17', NULL);
INSERT INTO `sys_dict_data` VALUES (101, 0, '湖南樽昊', 'KF_HNZH', 'kf_company_list', NULL, 'default', 'N', '0', 'admin', '2024-09-29 11:22:05', 'admin', '2024-10-08 09:47:24', NULL);
INSERT INTO `sys_dict_data` VALUES (102, 0, '青海昌泰', 'KF_QHCT', 'kf_company_list', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:42:10', 'admin', '2024-10-08 09:47:30', NULL);
INSERT INTO `sys_dict_data` VALUES (103, 0, '提交工单', '1', 'kf_work_option_type', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:48:03', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (104, 0, '认领工单', '2', 'kf_work_option_type', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:48:19', '', NULL, '认领工单');
INSERT INTO `sys_dict_data` VALUES (105, 0, '放弃认领', '3', 'kf_work_option_type', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:48:25', '', NULL, '放弃认领');
INSERT INTO `sys_dict_data` VALUES (106, 0, '发布工作记录', '4', 'kf_work_option_type', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:48:33', '', NULL, '发布工作记录');
INSERT INTO `sys_dict_data` VALUES (107, 0, '不处理', '5', 'kf_work_option_type', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:48:43', '', NULL, '不处理');
INSERT INTO `sys_dict_data` VALUES (108, 0, '完成工单', '6', 'kf_work_option_type', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:48:50', '', NULL, '完成工单');
INSERT INTO `sys_dict_data` VALUES (109, 0, '生效', '0', 'kf_delete_status', NULL, 'success', 'N', '0', 'admin', '2024-09-30 10:52:46', '', NULL, '生效');
INSERT INTO `sys_dict_data` VALUES (110, 0, '失效', '1', 'kf_delete_status', NULL, 'danger', 'N', '0', 'admin', '2024-09-30 10:53:03', '', NULL, '失效');
INSERT INTO `sys_dict_data` VALUES (111, 0, '待认领', 'READY', 'kf_work_order_status', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:55:31', '', NULL, '待认领');
INSERT INTO `sys_dict_data` VALUES (112, 0, '处理中', 'LOAD', 'kf_work_order_status', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:55:54', '', NULL, '处理中');
INSERT INTO `sys_dict_data` VALUES (113, 0, '已完成', 'COMPLETE', 'kf_work_order_status', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:56:06', '', NULL, '已完成');
INSERT INTO `sys_dict_data` VALUES (114, 0, '不处理', 'ABANDON', 'kf_work_order_status', NULL, 'default', 'N', '0', 'admin', '2024-09-30 10:56:17', '', NULL, '不处理');
INSERT INTO `sys_dict_data` VALUES (115, 0, '官网', 'KF_CHANNEL_GW', 'kf_channel_type', NULL, 'default', 'N', '0', 'admin', '2024-09-30 11:19:55', 'admin', '2024-09-30 11:37:58', '官网');
INSERT INTO `sys_dict_data` VALUES (116, 2, '小程序', 'KF_CHANNEL_XCX', 'kf_channel_type', NULL, 'default', 'N', '0', 'admin', '2024-09-30 11:20:24', 'demo', '2024-10-16 18:01:48', '小程序');
INSERT INTO `sys_dict_data` VALUES (117, 3, '公众号', 'KF_CHANNEL_GZH', 'kf_channel_type', NULL, 'default', 'N', '0', 'admin', '2024-10-08 09:38:27', 'demo', '2024-10-16 18:01:55', '公众号');
INSERT INTO `sys_dict_data` VALUES (118, 0, '湖北富宸', 'KF_HBFC', 'kf_company_list', NULL, 'default', 'N', '0', 'admin', '2024-10-08 09:47:44', 'admin', '2024-10-08 09:48:00', NULL);
INSERT INTO `sys_dict_data` VALUES (119, 0, '云南鼎丰', 'KF_YNDF', 'kf_company_list', NULL, 'default', 'N', '0', 'admin', '2024-10-08 09:48:16', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (120, 0, '海南正堂', 'KF_HNZT', 'kf_company_list', NULL, 'default', 'N', '0', 'admin', '2024-10-08 09:48:38', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (121, 0, '福建大有', 'KF_FJDY', 'kf_company_list', NULL, 'default', 'N', '0', 'admin', '2024-10-08 09:48:53', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (122, 0, '重庆淘然', 'KF_CQTR', 'kf_company_list', NULL, 'default', 'N', '0', 'admin', '2024-10-08 09:49:15', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (123, 1, '电话', 'KF_CHANNEL_DH', 'kf_channel_type', NULL, 'default', 'N', '0', 'admin', '2024-10-08 10:15:56', 'admin', '2024-10-18 10:10:52', NULL);
INSERT INTO `sys_dict_data` VALUES (124, 4, '其他', 'KF_CHANNEL_QT', 'kf_channel_type', NULL, 'default', 'N', '0', 'demo', '2024-10-16 18:03:07', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (125, 0, '客户', 'KF_SOURCE_KH', 'kf_source_type', NULL, 'default', 'N', '0', 'admin', '2024-10-18 10:31:14', '', NULL, '客户');
INSERT INTO `sys_dict_data` VALUES (126, 0, '客服', 'KF_SOURCE_KF', 'kf_source_type', NULL, 'default', 'N', '0', 'admin', '2024-10-18 10:31:24', 'admin', '2024-10-18 14:29:27', '客户');
INSERT INTO `sys_dict_data` VALUES (139, 0, '启用', '0', 'kf_status_type', NULL, 'success', 'N', '0', 'admin', '2024-10-10 15:20:03', 'admin', '2024-10-10 15:20:42', '启用');
INSERT INTO `sys_dict_data` VALUES (140, 0, '停用', '1', 'kf_status_type', NULL, 'danger', 'N', '0', 'admin', '2024-10-10 15:20:14', 'admin', '2024-10-10 15:20:29', '停用');
INSERT INTO `sys_dict_data` VALUES (141, 0, '福建创伟', 'KF_FJCW', 'kf_company_list', NULL, 'default', 'N', '0', 'admin', '2025-05-09 13:50:54', '', NULL, NULL);
INSERT INTO `sys_dict_data` VALUES (142, 0, '甘肃嘉德', 'KF_GSJD', 'kf_company_list', NULL, 'default', 'N', '0', 'admin', '2025-05-09 13:51:28', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL AUTO_INCREMENT COMMENT '字典主键',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '字典类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `dict_type`(`dict_type` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 107 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '用户性别', 'sys_user_sex', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '菜单状态', 'sys_show_hide', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '任务状态列表');
INSERT INTO `sys_dict_type` VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '任务分组列表');
INSERT INTO `sys_dict_type` VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '系统状态', 'sys_common_status', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (100, '客服系统公司列表', 'kf_company_list', '0', 'admin', '2024-09-29 11:21:03', '', NULL, NULL);
INSERT INTO `sys_dict_type` VALUES (101, '客服工单操作类型', 'kf_work_option_type', '0', 'admin', '2024-09-30 10:47:37', 'admin', '2024-09-30 10:50:07', '客服工单操作类型');
INSERT INTO `sys_dict_type` VALUES (102, '客服逻辑删除状态', 'kf_delete_status', '0', 'admin', '2024-09-30 10:51:13', '', NULL, '客服逻辑删除状态');
INSERT INTO `sys_dict_type` VALUES (103, '客服工单状态', 'kf_work_order_status', '0', 'admin', '2024-09-30 10:55:17', '', NULL, '客服工单状态');
INSERT INTO `sys_dict_type` VALUES (104, '客服渠道类型', 'kf_channel_type', '0', 'admin', '2024-09-30 11:19:29', '', NULL, '客服渠道类型');
INSERT INTO `sys_dict_type` VALUES (105, '客服工单来源类型', 'kf_source_type', '0', 'admin', '2024-10-18 10:30:34', '', NULL, '客服工单来源类型');
INSERT INTO `sys_dict_type` VALUES (106, '客服启用停用状态', 'kf_status_type', '0', 'admin', '2024-12-16 15:18:55', '', NULL, NULL);

-- ----------------------------
-- Table structure for sys_job
-- ----------------------------
DROP TABLE IF EXISTS `sys_job`;
CREATE TABLE `sys_job`  (
  `job_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT '' COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL DEFAULT 'DEFAULT' COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `cron_expression` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT 'cron执行表达式',
  `misfire_policy` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '3' COMMENT '计划执行错误策略（1立即执行 2执行一次 3放弃执行）',
  `concurrent` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '是否并发执行（0允许 1禁止）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '状态（0正常 1暂停）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`job_id`, `job_name`, `job_group`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job
-- ----------------------------

-- ----------------------------
-- Table structure for sys_job_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_job_log`;
CREATE TABLE `sys_job_log`  (
  `job_log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务日志ID',
  `job_name` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务名称',
  `job_group` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '任务组名',
  `invoke_target` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '调用目标字符串',
  `job_message` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '日志信息',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '执行状态（0正常 1失败）',
  `exception_info` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '异常信息',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`job_log_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '定时任务调度日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_job_log
-- ----------------------------

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户账号',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 440 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '系统访问记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO `sys_logininfor` VALUES (234, 'admin', '***********', 'XX XX', 'Firefox 13', 'Windows 10', '0', '登录成功', '2024-10-18 18:35:05');
INSERT INTO `sys_logininfor` VALUES (235, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 09:30:23');
INSERT INTO `sys_logininfor` VALUES (236, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-21 09:30:27');
INSERT INTO `sys_logininfor` VALUES (237, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-21 14:06:31');
INSERT INTO `sys_logininfor` VALUES (238, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-21 15:57:56');
INSERT INTO `sys_logininfor` VALUES (239, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '用户不存在/密码错误', '2024-10-21 17:07:32');
INSERT INTO `sys_logininfor` VALUES (240, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 17:07:45');
INSERT INTO `sys_logininfor` VALUES (241, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 17:07:58');
INSERT INTO `sys_logininfor` VALUES (242, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 17:08:49');
INSERT INTO `sys_logininfor` VALUES (243, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '用户不存在/密码错误', '2024-10-21 17:09:09');
INSERT INTO `sys_logininfor` VALUES (244, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '用户不存在/密码错误', '2024-10-21 17:10:12');
INSERT INTO `sys_logininfor` VALUES (245, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 17:10:29');
INSERT INTO `sys_logininfor` VALUES (246, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-21 17:10:34');
INSERT INTO `sys_logininfor` VALUES (247, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '退出成功', '2024-10-21 17:10:53');
INSERT INTO `sys_logininfor` VALUES (248, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 17:11:08');
INSERT INTO `sys_logininfor` VALUES (249, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 17:11:15');
INSERT INTO `sys_logininfor` VALUES (250, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-21 17:11:20');
INSERT INTO `sys_logininfor` VALUES (251, 'ry', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-21 17:11:25');
INSERT INTO `sys_logininfor` VALUES (252, 'ry', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '退出成功', '2024-10-21 17:11:35');
INSERT INTO `sys_logininfor` VALUES (253, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 17:11:43');
INSERT INTO `sys_logininfor` VALUES (254, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 17:11:48');
INSERT INTO `sys_logininfor` VALUES (255, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-21 17:11:54');
INSERT INTO `sys_logininfor` VALUES (256, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '退出成功', '2024-10-21 17:11:59');
INSERT INTO `sys_logininfor` VALUES (257, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-21 17:13:47');
INSERT INTO `sys_logininfor` VALUES (258, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 18:20:26');
INSERT INTO `sys_logininfor` VALUES (259, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-10-21 18:20:37');
INSERT INTO `sys_logininfor` VALUES (260, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '1', '用户不存在/密码错误', '2024-10-21 18:20:45');
INSERT INTO `sys_logininfor` VALUES (261, 'admin', '127.0.0.1', '内网IP', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-10-21 18:20:54');
INSERT INTO `sys_logininfor` VALUES (262, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-10-31 13:39:18');
INSERT INTO `sys_logininfor` VALUES (263, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-10-31 13:39:45');
INSERT INTO `sys_logininfor` VALUES (264, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-10-31 15:54:22');
INSERT INTO `sys_logininfor` VALUES (265, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-11-21 16:17:28');
INSERT INTO `sys_logininfor` VALUES (266, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-11-21 16:17:32');
INSERT INTO `sys_logininfor` VALUES (267, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-12 15:12:47');
INSERT INTO `sys_logininfor` VALUES (268, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-12-13 17:11:00');
INSERT INTO `sys_logininfor` VALUES (269, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '用户不存在/密码错误', '2024-12-13 17:11:13');
INSERT INTO `sys_logininfor` VALUES (270, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '用户不存在/密码错误', '2024-12-13 17:11:53');
INSERT INTO `sys_logininfor` VALUES (271, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-13 17:12:57');
INSERT INTO `sys_logininfor` VALUES (272, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-13 17:13:03');
INSERT INTO `sys_logininfor` VALUES (273, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-12-13 17:14:36');
INSERT INTO `sys_logininfor` VALUES (274, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-13 17:18:28');
INSERT INTO `sys_logininfor` VALUES (275, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2024-12-16 09:16:18');
INSERT INTO `sys_logininfor` VALUES (276, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-16 09:16:27');
INSERT INTO `sys_logininfor` VALUES (277, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-16 14:32:31');
INSERT INTO `sys_logininfor` VALUES (278, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-16 14:46:54');
INSERT INTO `sys_logininfor` VALUES (279, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-16 14:47:03');
INSERT INTO `sys_logininfor` VALUES (280, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-16 14:47:07');
INSERT INTO `sys_logininfor` VALUES (281, 'admin', '***********', 'XX XX', 'Chrome 11', 'Windows 10', '0', '登录成功', '2024-12-16 15:15:45');
INSERT INTO `sys_logininfor` VALUES (282, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-16 15:28:49');
INSERT INTO `sys_logininfor` VALUES (283, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2024-12-16 16:01:49');
INSERT INTO `sys_logininfor` VALUES (284, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2024-12-16 16:01:58');
INSERT INTO `sys_logininfor` VALUES (285, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-12-16 16:02:06');
INSERT INTO `sys_logininfor` VALUES (286, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-16 18:41:06');
INSERT INTO `sys_logininfor` VALUES (287, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-17 13:51:31');
INSERT INTO `sys_logininfor` VALUES (288, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-17 14:52:46');
INSERT INTO `sys_logininfor` VALUES (289, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-17 19:31:21');
INSERT INTO `sys_logininfor` VALUES (290, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-17 19:39:10');
INSERT INTO `sys_logininfor` VALUES (291, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-18 20:03:50');
INSERT INTO `sys_logininfor` VALUES (292, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-18 20:04:07');
INSERT INTO `sys_logininfor` VALUES (293, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-19 09:52:39');
INSERT INTO `sys_logininfor` VALUES (294, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-19 11:01:49');
INSERT INTO `sys_logininfor` VALUES (295, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-19 11:01:57');
INSERT INTO `sys_logininfor` VALUES (296, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-19 11:02:06');
INSERT INTO `sys_logininfor` VALUES (297, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-20 11:44:05');
INSERT INTO `sys_logininfor` VALUES (298, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-20 11:44:12');
INSERT INTO `sys_logininfor` VALUES (299, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-20 11:44:20');
INSERT INTO `sys_logininfor` VALUES (300, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-20 11:44:29');
INSERT INTO `sys_logininfor` VALUES (301, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-20 13:37:06');
INSERT INTO `sys_logininfor` VALUES (302, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-20 17:34:38');
INSERT INTO `sys_logininfor` VALUES (303, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-20 17:34:45');
INSERT INTO `sys_logininfor` VALUES (304, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-20 17:34:55');
INSERT INTO `sys_logininfor` VALUES (305, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-20 19:14:00');
INSERT INTO `sys_logininfor` VALUES (306, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-20 19:14:08');
INSERT INTO `sys_logininfor` VALUES (307, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-20 19:14:16');
INSERT INTO `sys_logininfor` VALUES (308, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-20 19:14:24');
INSERT INTO `sys_logininfor` VALUES (309, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-20 19:14:32');
INSERT INTO `sys_logininfor` VALUES (310, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-23 09:22:21');
INSERT INTO `sys_logininfor` VALUES (311, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-23 11:03:00');
INSERT INTO `sys_logininfor` VALUES (312, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-12-23 13:58:44');
INSERT INTO `sys_logininfor` VALUES (313, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-23 14:00:06');
INSERT INTO `sys_logininfor` VALUES (314, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-23 14:00:18');
INSERT INTO `sys_logininfor` VALUES (315, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2024-12-23 15:14:49');
INSERT INTO `sys_logininfor` VALUES (316, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-23 15:14:58');
INSERT INTO `sys_logininfor` VALUES (317, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-12-23 16:11:19');
INSERT INTO `sys_logininfor` VALUES (318, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-23 16:16:40');
INSERT INTO `sys_logininfor` VALUES (319, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '退出成功', '2024-12-23 16:21:57');
INSERT INTO `sys_logininfor` VALUES (320, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-12-23 16:22:28');
INSERT INTO `sys_logininfor` VALUES (321, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-24 16:59:25');
INSERT INTO `sys_logininfor` VALUES (322, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-24 16:59:29');
INSERT INTO `sys_logininfor` VALUES (323, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-24 16:59:34');
INSERT INTO `sys_logininfor` VALUES (324, 'admin', '***********', 'XX XX', 'Chrome 11', 'Windows 10', '0', '登录成功', '2024-12-24 17:26:58');
INSERT INTO `sys_logininfor` VALUES (325, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-24 17:30:39');
INSERT INTO `sys_logininfor` VALUES (326, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-24 17:30:50');
INSERT INTO `sys_logininfor` VALUES (327, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 10:52:27');
INSERT INTO `sys_logininfor` VALUES (328, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 10:52:32');
INSERT INTO `sys_logininfor` VALUES (329, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 10:52:46');
INSERT INTO `sys_logininfor` VALUES (330, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 10:52:53');
INSERT INTO `sys_logininfor` VALUES (331, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-25 11:16:18');
INSERT INTO `sys_logininfor` VALUES (332, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 11:16:22');
INSERT INTO `sys_logininfor` VALUES (333, 'admin', '***********', 'XX XX', 'Chrome 11', 'Windows 10', '0', '登录成功', '2024-12-25 11:22:27');
INSERT INTO `sys_logininfor` VALUES (334, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-25 11:23:37');
INSERT INTO `sys_logininfor` VALUES (335, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 11:23:41');
INSERT INTO `sys_logininfor` VALUES (336, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 11:24:06');
INSERT INTO `sys_logininfor` VALUES (337, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 11:24:12');
INSERT INTO `sys_logininfor` VALUES (338, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 11:24:15');
INSERT INTO `sys_logininfor` VALUES (339, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-25 11:24:59');
INSERT INTO `sys_logininfor` VALUES (340, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 11:25:03');
INSERT INTO `sys_logininfor` VALUES (341, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 11:39:02');
INSERT INTO `sys_logininfor` VALUES (342, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-25 11:40:27');
INSERT INTO `sys_logininfor` VALUES (343, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 11:40:37');
INSERT INTO `sys_logininfor` VALUES (344, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 11:40:46');
INSERT INTO `sys_logininfor` VALUES (345, 'demo', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 11:46:35');
INSERT INTO `sys_logininfor` VALUES (346, 'demo', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 11:46:38');
INSERT INTO `sys_logininfor` VALUES (347, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 11:48:12');
INSERT INTO `sys_logininfor` VALUES (348, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-25 11:55:41');
INSERT INTO `sys_logininfor` VALUES (349, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 11:55:48');
INSERT INTO `sys_logininfor` VALUES (350, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '退出成功', '2024-12-25 11:56:25');
INSERT INTO `sys_logininfor` VALUES (351, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 11:56:31');
INSERT INTO `sys_logininfor` VALUES (352, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2024-12-25 13:39:14');
INSERT INTO `sys_logininfor` VALUES (353, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 13:39:23');
INSERT INTO `sys_logininfor` VALUES (354, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 13:39:42');
INSERT INTO `sys_logininfor` VALUES (355, 'demo', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 14:03:47');
INSERT INTO `sys_logininfor` VALUES (356, 'admin', '***********', 'XX XX', 'Firefox 13', 'Windows 10', '1', '验证码错误', '2024-12-25 14:37:32');
INSERT INTO `sys_logininfor` VALUES (357, 'admin', '***********', 'XX XX', 'Firefox 13', 'Windows 10', '1', '验证码错误', '2024-12-25 14:37:37');
INSERT INTO `sys_logininfor` VALUES (358, 'admin', '***********', 'XX XX', 'Firefox 13', 'Windows 10', '1', '验证码错误', '2024-12-25 14:37:41');
INSERT INTO `sys_logininfor` VALUES (359, 'admin', '***********', 'XX XX', 'Firefox 13', 'Windows 10', '0', '登录成功', '2024-12-25 14:37:48');
INSERT INTO `sys_logininfor` VALUES (360, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 14:47:15');
INSERT INTO `sys_logininfor` VALUES (361, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 14:47:20');
INSERT INTO `sys_logininfor` VALUES (362, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2024-12-25 14:47:27');
INSERT INTO `sys_logininfor` VALUES (363, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-25 14:47:38');
INSERT INTO `sys_logininfor` VALUES (364, 'admin', '127.0.0.1', '内网IP', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 14:47:49');
INSERT INTO `sys_logininfor` VALUES (365, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 14:55:30');
INSERT INTO `sys_logininfor` VALUES (366, 'admin', '***********', 'XX XX', 'Chrome 11', 'Windows 10', '0', '登录成功', '2024-12-25 15:02:06');
INSERT INTO `sys_logininfor` VALUES (367, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-25 17:48:46');
INSERT INTO `sys_logininfor` VALUES (368, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-26 11:26:11');
INSERT INTO `sys_logininfor` VALUES (369, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-26 11:26:18');
INSERT INTO `sys_logininfor` VALUES (370, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-12-26 11:31:43');
INSERT INTO `sys_logininfor` VALUES (371, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-26 11:34:17');
INSERT INTO `sys_logininfor` VALUES (372, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-26 13:43:35');
INSERT INTO `sys_logininfor` VALUES (373, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-26 14:27:10');
INSERT INTO `sys_logininfor` VALUES (374, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-12-26 14:28:01');
INSERT INTO `sys_logininfor` VALUES (375, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-12-26 15:58:55');
INSERT INTO `sys_logininfor` VALUES (376, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2024-12-26 16:00:19');
INSERT INTO `sys_logininfor` VALUES (377, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-26 16:00:25');
INSERT INTO `sys_logininfor` VALUES (378, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-12-26 17:35:20');
INSERT INTO `sys_logininfor` VALUES (379, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-26 17:35:39');
INSERT INTO `sys_logininfor` VALUES (380, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-26 18:01:59');
INSERT INTO `sys_logininfor` VALUES (381, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-26 18:47:06');
INSERT INTO `sys_logininfor` VALUES (382, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-26 19:20:33');
INSERT INTO `sys_logininfor` VALUES (383, 'demo', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2024-12-26 19:22:26');
INSERT INTO `sys_logininfor` VALUES (384, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-31 15:51:00');
INSERT INTO `sys_logininfor` VALUES (385, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2024-12-31 17:23:26');
INSERT INTO `sys_logininfor` VALUES (386, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-01-02 09:30:47');
INSERT INTO `sys_logininfor` VALUES (387, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-02 09:30:53');
INSERT INTO `sys_logininfor` VALUES (388, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-01-13 15:33:08');
INSERT INTO `sys_logininfor` VALUES (389, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-01-13 15:33:16');
INSERT INTO `sys_logininfor` VALUES (390, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-01-14 09:38:35');
INSERT INTO `sys_logininfor` VALUES (391, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-01-14 09:51:06');
INSERT INTO `sys_logininfor` VALUES (392, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-14 09:51:10');
INSERT INTO `sys_logininfor` VALUES (393, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-14 10:24:51');
INSERT INTO `sys_logininfor` VALUES (394, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-01-14 10:24:56');
INSERT INTO `sys_logininfor` VALUES (395, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-14 10:25:01');
INSERT INTO `sys_logininfor` VALUES (396, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-14 10:25:07');
INSERT INTO `sys_logininfor` VALUES (397, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-16 09:25:25');
INSERT INTO `sys_logininfor` VALUES (398, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-16 10:28:52');
INSERT INTO `sys_logininfor` VALUES (399, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-01-16 13:35:35');
INSERT INTO `sys_logininfor` VALUES (400, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-16 13:35:43');
INSERT INTO `sys_logininfor` VALUES (401, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-01-17 13:47:08');
INSERT INTO `sys_logininfor` VALUES (402, 'admin', '***********', 'XX XX', 'Firefox 13', 'Windows 10', '1', '验证码错误', '2025-01-20 09:40:12');
INSERT INTO `sys_logininfor` VALUES (403, 'admin', '***********', 'XX XX', 'Firefox 13', 'Windows 10', '0', '登录成功', '2025-01-20 09:40:17');
INSERT INTO `sys_logininfor` VALUES (404, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2025-01-20 09:57:33');
INSERT INTO `sys_logininfor` VALUES (405, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-01-20 09:57:45');
INSERT INTO `sys_logininfor` VALUES (406, 'admin', '***********', 'XX XX', 'Firefox 13', 'Windows 10', '0', '登录成功', '2025-01-20 11:09:40');
INSERT INTO `sys_logininfor` VALUES (407, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码已失效', '2025-01-20 11:51:23');
INSERT INTO `sys_logininfor` VALUES (408, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-20 11:51:31');
INSERT INTO `sys_logininfor` VALUES (409, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-01-20 11:51:40');
INSERT INTO `sys_logininfor` VALUES (410, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-01-20 14:17:51');
INSERT INTO `sys_logininfor` VALUES (411, 'admin', '***********', 'XX XX', 'Firefox 13', 'Windows 10', '0', '登录成功', '2025-01-20 14:26:27');
INSERT INTO `sys_logininfor` VALUES (412, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 14:34:54');
INSERT INTO `sys_logininfor` VALUES (413, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:17:59');
INSERT INTO `sys_logininfor` VALUES (414, 'admin', '8.152.154.23', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-01-20 15:44:49');
INSERT INTO `sys_logininfor` VALUES (415, 'admin', '8.152.154.23', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-20 15:44:57');
INSERT INTO `sys_logininfor` VALUES (416, 'admin', '***********', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-01-20 16:28:55');
INSERT INTO `sys_logininfor` VALUES (417, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-01-21 09:23:52');
INSERT INTO `sys_logininfor` VALUES (418, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-01-21 09:23:56');
INSERT INTO `sys_logininfor` VALUES (419, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-21 09:24:00');
INSERT INTO `sys_logininfor` VALUES (420, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-01-21 13:43:27');
INSERT INTO `sys_logininfor` VALUES (421, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-01-21 13:43:40');
INSERT INTO `sys_logininfor` VALUES (422, 'admin', '***********', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-21 13:43:49');
INSERT INTO `sys_logininfor` VALUES (423, 'admin', '8.152.154.23', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-01-21 15:19:54');
INSERT INTO `sys_logininfor` VALUES (424, 'admin', '8.152.154.23', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-01-21 15:19:59');
INSERT INTO `sys_logininfor` VALUES (425, 'admin', '8.152.154.23', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码已失效', '2025-05-09 11:40:04');
INSERT INTO `sys_logininfor` VALUES (426, 'admin', '8.152.154.23', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-05-09 11:40:09');
INSERT INTO `sys_logininfor` VALUES (427, 'admin', '8.152.154.23', 'XX XX', 'Chrome 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-05-09 13:48:59');
INSERT INTO `sys_logininfor` VALUES (428, 'admin', '8.152.154.23', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-05-09 13:49:09');
INSERT INTO `sys_logininfor` VALUES (429, 'admin', '8.152.154.23', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-09 14:18:39');
INSERT INTO `sys_logininfor` VALUES (430, 'admin', '8.152.154.23', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-05-09 15:30:26');
INSERT INTO `sys_logininfor` VALUES (431, 'admin', '8.152.154.23', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-09 15:30:57');
INSERT INTO `sys_logininfor` VALUES (432, 'admin', '8.152.154.23', 'XX XX', 'Chrome 13', 'Windows 10', '1', '验证码错误', '2025-05-09 15:35:33');
INSERT INTO `sys_logininfor` VALUES (433, 'admin', '8.152.154.23', 'XX XX', 'Chrome 13', 'Windows 10', '0', '登录成功', '2025-05-09 15:35:41');
INSERT INTO `sys_logininfor` VALUES (434, 'admin', '8.152.154.23', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-12 10:44:16');
INSERT INTO `sys_logininfor` VALUES (435, 'zbgx', '**********', 'XX XX', 'Firefox 13', 'Windows 10', '1', '用户不存在/密码错误', '2025-05-16 10:43:55');
INSERT INTO `sys_logininfor` VALUES (436, 'admin', '8.152.154.23', 'XX XX', 'Chrome 12', 'Windows 10', '1', '验证码错误', '2025-05-16 10:44:29');
INSERT INTO `sys_logininfor` VALUES (437, 'admin', '8.152.154.23', 'XX XX', 'Chrome 12', 'Windows 10', '0', '登录成功', '2025-05-16 10:44:34');
INSERT INTO `sys_logininfor` VALUES (438, 'admin', '**********', 'XX XX', 'Firefox 13', 'Windows 10', '1', '验证码错误', '2025-05-16 10:45:05');
INSERT INTO `sys_logininfor` VALUES (439, 'admin', '**********', 'XX XX', 'Firefox 13', 'Windows 10', '0', '登录成功', '2025-05-16 10:45:13');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL AUTO_INCREMENT COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '路由参数',
  `route_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '路由名称',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2079 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1, '系统管理', 0, 1, 'system', NULL, '', '', 1, 0, 'M', '0', '0', '', 'system', 'admin', '2024-09-27 13:47:03', '', NULL, '系统管理目录');
INSERT INTO `sys_menu` VALUES (2, '系统监控', 0, 2, 'monitor', NULL, '', '', 1, 0, 'M', '0', '0', '', 'monitor', 'admin', '2024-09-27 13:47:03', '', NULL, '系统监控目录');
INSERT INTO `sys_menu` VALUES (3, '系统工具', 0, 3, 'tool', NULL, '', '', 1, 0, 'M', '0', '0', '', 'tool', 'admin', '2024-09-27 13:47:03', '', NULL, '系统工具目录');
INSERT INTO `sys_menu` VALUES (100, '用户管理', 1, 1, 'user', 'system/user/index', '', '', 1, 0, 'C', '0', '0', 'system:user:list', 'user', 'admin', '2024-09-27 13:47:03', '', NULL, '用户管理菜单');
INSERT INTO `sys_menu` VALUES (101, '角色管理', 1, 2, 'role', 'system/role/index', '', '', 1, 0, 'C', '0', '0', 'system:role:list', 'peoples', 'admin', '2024-09-27 13:47:03', '', NULL, '角色管理菜单');
INSERT INTO `sys_menu` VALUES (102, '菜单管理', 1, 3, 'menu', 'system/menu/index', '', '', 1, 0, 'C', '0', '0', 'system:menu:list', 'tree-table', 'admin', '2024-09-27 13:47:03', '', NULL, '菜单管理菜单');
INSERT INTO `sys_menu` VALUES (103, '部门管理', 1, 4, 'dept', 'system/dept/index', '', '', 1, 0, 'C', '0', '0', 'system:dept:list', 'tree', 'admin', '2024-09-27 13:47:03', 'admin', '2024-09-27 14:20:55', '部门管理菜单');
INSERT INTO `sys_menu` VALUES (104, '岗位管理', 1, 5, 'post', 'system/post/index', '', '', 1, 0, 'C', '0', '0', 'system:post:list', 'post', 'admin', '2024-09-27 13:47:03', '', NULL, '岗位管理菜单');
INSERT INTO `sys_menu` VALUES (105, '字典管理', 1, 6, 'dict', 'system/dict/index', '', '', 1, 0, 'C', '0', '0', 'system:dict:list', 'dict', 'admin', '2024-09-27 13:47:03', '', NULL, '字典管理菜单');
INSERT INTO `sys_menu` VALUES (106, '参数设置', 1, 7, 'config', 'system/config/index', '', '', 1, 0, 'C', '0', '0', 'system:config:list', 'edit', 'admin', '2024-09-27 13:47:03', '', NULL, '参数设置菜单');
INSERT INTO `sys_menu` VALUES (107, '通知公告', 1, 8, 'notice', 'system/notice/index', '', '', 1, 0, 'C', '0', '0', 'system:notice:list', 'message', 'admin', '2024-09-27 13:47:03', '', NULL, '通知公告菜单');
INSERT INTO `sys_menu` VALUES (108, '日志管理', 1, 9, 'log', '', '', '', 1, 0, 'M', '0', '0', '', 'log', 'admin', '2024-09-27 13:47:03', '', NULL, '日志管理菜单');
INSERT INTO `sys_menu` VALUES (109, '在线用户', 2, 1, 'online', 'monitor/online/index', '', '', 1, 0, 'C', '0', '0', 'monitor:online:list', 'online', 'admin', '2024-09-27 13:47:03', '', NULL, '在线用户菜单');
INSERT INTO `sys_menu` VALUES (110, '定时任务', 2, 2, 'job', 'monitor/job/index', '', '', 1, 0, 'C', '0', '0', 'monitor:job:list', 'job', 'admin', '2024-09-27 13:47:03', '', NULL, '定时任务菜单');
INSERT INTO `sys_menu` VALUES (111, '数据监控', 2, 3, 'druid', 'monitor/druid/index', '', '', 1, 0, 'C', '0', '0', 'monitor:druid:list', 'druid', 'admin', '2024-09-27 13:47:03', '', NULL, '数据监控菜单');
INSERT INTO `sys_menu` VALUES (112, '服务监控', 2, 4, 'server', 'monitor/server/index', '', '', 1, 0, 'C', '0', '0', 'monitor:server:list', 'server', 'admin', '2024-09-27 13:47:03', '', NULL, '服务监控菜单');
INSERT INTO `sys_menu` VALUES (113, '缓存监控', 2, 5, 'cache', 'monitor/cache/index', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis', 'admin', '2024-09-27 13:47:03', '', NULL, '缓存监控菜单');
INSERT INTO `sys_menu` VALUES (114, '缓存列表', 2, 6, 'cacheList', 'monitor/cache/list', '', '', 1, 0, 'C', '0', '0', 'monitor:cache:list', 'redis-list', 'admin', '2024-09-27 13:47:03', '', NULL, '缓存列表菜单');
INSERT INTO `sys_menu` VALUES (115, '表单构建', 3, 1, 'build', 'tool/build/index', '', '', 1, 0, 'C', '0', '0', 'tool:build:list', 'build', 'admin', '2024-09-27 13:47:03', '', NULL, '表单构建菜单');
INSERT INTO `sys_menu` VALUES (116, '代码生成', 3, 2, 'gen', 'tool/gen/index', '', '', 1, 0, 'C', '0', '0', 'tool:gen:list', 'code', 'admin', '2024-09-27 13:47:03', '', NULL, '代码生成菜单');
INSERT INTO `sys_menu` VALUES (117, '系统接口', 3, 3, 'swagger', 'tool/swagger/index', '', '', 1, 0, 'C', '0', '0', 'tool:swagger:list', 'swagger', 'admin', '2024-09-27 13:47:03', '', NULL, '系统接口菜单');
INSERT INTO `sys_menu` VALUES (500, '操作日志', 108, 1, 'operlog', 'monitor/operlog/index', '', '', 1, 0, 'C', '0', '0', 'monitor:operlog:list', 'form', 'admin', '2024-09-27 13:47:03', '', NULL, '操作日志菜单');
INSERT INTO `sys_menu` VALUES (501, '登录日志', 108, 2, 'logininfor', 'monitor/logininfor/index', '', '', 1, 0, 'C', '0', '0', 'monitor:logininfor:list', 'logininfor', 'admin', '2024-09-27 13:47:03', '', NULL, '登录日志菜单');
INSERT INTO `sys_menu` VALUES (1000, '用户查询', 100, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1001, '用户新增', 100, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:add', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1002, '用户修改', 100, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:edit', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1003, '用户删除', 100, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1004, '用户导出', 100, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:export', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1005, '用户导入', 100, 6, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:import', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1006, '重置密码', 100, 7, '', '', '', '', 1, 0, 'F', '0', '0', 'system:user:resetPwd', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1007, '角色查询', 101, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1008, '角色新增', 101, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:add', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1009, '角色修改', 101, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:edit', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1010, '角色删除', 101, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1011, '角色导出', 101, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:role:export', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1012, '菜单查询', 102, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1013, '菜单新增', 102, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:add', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1014, '菜单修改', 102, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:edit', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1015, '菜单删除', 102, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:menu:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1016, '部门查询', 103, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1017, '部门新增', 103, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:add', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1018, '部门修改', 103, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:edit', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1019, '部门删除', 103, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:dept:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1020, '岗位查询', 104, 1, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1021, '岗位新增', 104, 2, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:add', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1022, '岗位修改', 104, 3, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:edit', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1023, '岗位删除', 104, 4, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1024, '岗位导出', 104, 5, '', '', '', '', 1, 0, 'F', '0', '0', 'system:post:export', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1025, '字典查询', 105, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1026, '字典新增', 105, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:add', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1027, '字典修改', 105, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:edit', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1028, '字典删除', 105, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1029, '字典导出', 105, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:dict:export', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1030, '参数查询', 106, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1031, '参数新增', 106, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:add', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1032, '参数修改', 106, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:edit', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1033, '参数删除', 106, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1034, '参数导出', 106, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:config:export', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1035, '公告查询', 107, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1036, '公告新增', 107, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:add', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1037, '公告修改', 107, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:edit', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1038, '公告删除', 107, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'system:notice:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1039, '操作查询', 500, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1040, '操作删除', 500, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1041, '日志导出', 500, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:operlog:export', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1042, '登录查询', 501, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1043, '登录删除', 501, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1044, '日志导出', 501, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:export', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1045, '账户解锁', 501, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:logininfor:unlock', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1046, '在线查询', 109, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1047, '批量强退', 109, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:batchLogout', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1048, '单条强退', 109, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:online:forceLogout', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1049, '任务查询', 110, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1050, '任务新增', 110, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:add', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1051, '任务修改', 110, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:edit', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1052, '任务删除', 110, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1053, '状态修改', 110, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:changeStatus', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1054, '任务导出', 110, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'monitor:job:export', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1055, '生成查询', 116, 1, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:query', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1056, '生成修改', 116, 2, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:edit', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1057, '生成删除', 116, 3, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:remove', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1058, '导入代码', 116, 4, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:import', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1059, '预览代码', 116, 5, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:preview', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (1060, '生成代码', 116, 6, '#', '', '', '', 1, 0, 'F', '0', '0', 'tool:gen:code', '#', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2001, '工单', 0, 4, 'service/workOrder', 'service/workOrder/index', NULL, '', 1, 0, 'C', '0', '0', '', 'documentation', 'admin', '2024-09-30 13:52:37', 'admin', '2024-09-30 13:54:19', '');
INSERT INTO `sys_menu` VALUES (2003, '工单管理', 0, 5, 'service/wqinfo', 'service/wqinfo/index', NULL, '', 1, 0, 'C', '0', '0', '', 'list', 'admin', '2024-10-08 15:25:48', 'admin', '2024-10-08 19:00:15', '');
INSERT INTO `sys_menu` VALUES (2004, '搜索', 2001, 0, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:work:list', '#', 'admin', '2024-10-09 15:43:39', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2005, '修改工单状态', 2001, 1, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:workstatus:update', '#', 'admin', '2024-10-09 15:44:16', 'admin', '2024-10-09 15:47:17', '');
INSERT INTO `sys_menu` VALUES (2006, '发布工作记录或者客户备注', 2001, 2, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:workremark:insert', '#', 'admin', '2024-10-09 15:44:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2007, '查询工作记录和客户备注信息', 2001, 3, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:workremark:query', '#', 'admin', '2024-10-09 15:45:11', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2008, '删除备注信息或工作记录接口', 2001, 4, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:workremark:delete', '#', 'admin', '2024-10-09 15:45:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2009, '查询明文客户信息', 2001, 5, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:custominfo:query', '#', 'admin', '2024-10-09 15:45:44', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2010, '查询公司未完成数量', 2001, 6, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:undone:list', '#', 'admin', '2024-10-10 10:05:31', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2011, '通过工单ID查看详情', 2001, 7, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:work:getById', '#', 'admin', '2024-10-10 10:17:41', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2012, '后台新增工单', 2001, 8, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:order:insert', '#', 'ry', '2024-10-10 10:40:46', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2013, '后台提交工单', 2001, 9, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:order:insert', '#', 'ry', '2024-10-10 10:46:36', 'admin', '2024-10-14 15:01:18', '');
INSERT INTO `sys_menu` VALUES (2014, '公司管理', 0, 6, 'company', 'kf/company/index', NULL, '', 1, 0, 'C', '0', '0', 'kf:company:list', 'clipboard', 'admin', '2024-10-10 15:10:18', 'admin', '2024-10-10 17:06:08', '客服-公司管理菜单');
INSERT INTO `sys_menu` VALUES (2016, '渠道管理', 0, 7, 'channel', 'kf/channel/index', NULL, '', 1, 0, 'C', '0', '0', 'kf:channel:list', 'documentation', 'admin', '2024-10-10 17:22:58', 'admin', '2024-10-10 17:25:01', '渠道菜单');
INSERT INTO `sys_menu` VALUES (2023, '客服人员', 0, 8, 'account', 'kf/account/index', NULL, '', 1, 0, 'C', '0', '0', 'kf:account:list', 'clipboard', 'admin', '2024-10-11 13:45:23', 'admin', '2024-10-11 13:55:27', '客服人员菜单');
INSERT INTO `sys_menu` VALUES (2033, '获取公司列表字典集合-公司管理', 2001, 10, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:channel:dict', '#', 'admin', '2024-11-20 11:48:51', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2034, '工单编辑记录条数', 2001, 11, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'custom:update:count', '#', 'admin', '2024-11-20 13:50:16', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2035, '查看编辑记录详情', 2001, 12, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'custom:record:id', '#', 'admin', '2024-11-20 13:53:04', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2036, '修改工单用户信息', 2001, 13, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:work:update', '#', 'admin', '2024-11-20 13:55:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2037, '查询公司未完成数量', 2003, 1, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:undone:list', '#', 'admin', '2024-11-20 14:05:31', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2038, '获取工单页面列表', 2003, 2, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:work:list', '#', 'admin', '2024-11-20 14:06:47', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2039, '获取工单页面列表通过主键', 2003, 3, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:work:getById', '#', 'admin', '2024-11-20 14:08:10', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2040, '查询工作记录和客户备注信息', 2003, 4, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:workremark:query', '#', 'admin', '2024-11-20 14:09:57', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2041, '获取公司列表字典集合-公司管理', 2003, 5, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:channel:dict', '#', 'admin', '2024-11-20 14:10:28', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2042, '查询明文客户信息', 2003, 6, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:custominfo:query', '#', 'admin', '2024-11-20 14:16:25', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2043, '编辑记录条数', 2003, 7, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'custom:update:count', '#', 'admin', '2024-11-20 14:18:14', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2044, '编辑记录详情', 2003, 8, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'custom:record:id', '#', 'admin', '2024-11-20 14:18:35', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2053, '下载附件', 2001, 14, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:work:downloadFileZip', '#', 'admin', '2024-12-13 16:38:18', 'demo', '2024-12-25 11:02:55', '');
INSERT INTO `sys_menu` VALUES (2054, '客服-公司管理删除', 2014, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:company:remove', '#', 'admin', '2024-10-10 15:10:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2055, '客服-公司管理查询', 2014, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:company:query', '#', 'admin', '2024-10-10 15:10:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2056, '客服-公司管理新增', 2014, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:company:add', '#', 'admin', '2024-10-10 15:10:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2057, '客服-公司管理修改', 2014, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:company:edit', '#', 'admin', '2024-10-10 15:10:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2058, '客服-公司管理删除', 2014, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:company:remove', '#', 'admin', '2024-10-10 15:10:18', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2059, '公司列表集合', 2014, 6, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:company:dict', '#', 'admin', '2024-10-11 10:11:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2060, '渠道查询', 2016, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:channel:query', '#', 'admin', '2024-10-10 17:22:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2061, '渠道新增', 2016, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:channel:add', '#', 'admin', '2024-10-10 17:22:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2062, '渠道修改', 2016, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:channel:edit', '#', 'admin', '2024-10-10 17:22:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2063, '渠道删除', 2016, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:channel:remove', '#', 'admin', '2024-10-10 17:22:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2064, '获取公司列表字典集合-公司管理', 2016, 6, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:company:dict', '#', 'admin', '2024-11-20 14:33:03', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2065, '获取渠道列表字典集合-渠道管理', 2016, 7, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:channel:dict', '#', 'admin', '2024-11-20 14:34:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2066, '客服人员查询', 2023, 1, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:account:query', '#', 'admin', '2024-10-11 13:45:23', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2067, '客服人员新增', 2023, 2, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:account:add', '#', 'admin', '2024-10-11 13:45:23', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2068, '客服人员修改', 2023, 3, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:account:edit', '#', 'admin', '2024-10-11 13:45:23', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2069, '客服人员删除', 2023, 4, '#', '', NULL, '', 1, 0, 'F', '0', '0', 'kf:account:remove', '#', 'admin', '2024-10-11 13:45:23', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2070, '按照渠道查询树形结构', 2023, 6, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:channel:tree', '#', 'admin', '2024-11-20 14:42:58', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2071, '查询客服人员列表通过渠道', 2023, 7, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:account:listByChannel', '#', 'admin', '2024-11-20 14:44:29', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2072, '查询客服人员列表按照客户', 2023, 8, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:account:listByAccount', '#', 'admin', '2024-11-20 14:44:45', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2073, '通过公司编码查询渠道数据', 2023, 9, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:account:channelList', '#', 'admin', '2024-11-20 14:46:44', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2074, '通过多个渠道ID查询能够选择人员', 2023, 10, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:account:accountList', '#', 'admin', '2024-11-20 14:47:38', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2075, '人员启用/停用按钮', 2023, 11, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:account:updateStatus', '#', 'admin', '2024-11-20 14:49:04', 'admin', '2024-11-20 14:49:08', '');
INSERT INTO `sys_menu` VALUES (2076, '查询附件列表信息', 2003, 9, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:work:getFileListInfo', '#', 'demo', '2024-12-24 17:22:24', '', NULL, '');
INSERT INTO `sys_menu` VALUES (2077, '查询附件列表', 2001, 15, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:work:getFileListInfo', '#', 'admin', '2024-12-24 17:28:01', 'admin', '2024-12-24 17:28:11', '');
INSERT INTO `sys_menu` VALUES (2078, '下载附件', 2003, 10, '', NULL, NULL, '', 1, 0, 'F', '0', '0', 'kf:work:downloadFileZip', '#', 'demo', '2024-12-25 11:03:18', '', NULL, '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` int NOT NULL AUTO_INCREMENT COMMENT '公告ID',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 10 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '通知公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_notice
-- ----------------------------

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志主键',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '请求参数',
  `json_result` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 196 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '操作日志记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO `sys_oper_log` VALUES (121, '字典类型', 9, 'com.ruoyi.web.controller.system.SysDictTypeController.refreshCache()', 'DELETE', 1, 'admin', '客服管理系统TOP', '/system/dict/type/refreshCache', '***********', 'XX XX', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-10-18 17:29:08', 7);
INSERT INTO `sys_oper_log` VALUES (122, '定时任务', 1, 'com.ruoyi.quartz.controller.SysJobController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/monitor/job', '***********', 'XX XX', '{\"concurrent\":\"1\",\"cronExpression\":\"0 0 18 * * ?\",\"invokeTarget\":\"getSftpFileUpOssTask.updateForDay()\",\"jobGroup\":\"SYSTEM\",\"jobName\":\"获取sftp文件目录上传到oss\",\"misfirePolicy\":\"1\",\"nextValidTime\":\"2024-10-21 18:00:00\",\"params\":{},\"status\":\"0\"}', NULL, 1, 'No bean named \'getSftpFileUpOssTask\' available', '2024-10-21 15:58:29', 10);
INSERT INTO `sys_oper_log` VALUES (123, '定时任务', 1, 'com.ruoyi.quartz.controller.SysJobController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/monitor/job', '***********', 'XX XX', '{\"concurrent\":\"1\",\"cronExpression\":\"0 0 18 * * ?\",\"invokeTarget\":\"getSftpFileUpOssTask.updateForDay()\",\"jobGroup\":\"SYSTEM\",\"jobName\":\"获取sftp文件目录上传到oss\",\"misfirePolicy\":\"1\",\"nextValidTime\":\"2024-10-21 18:00:00\",\"params\":{},\"status\":\"0\"}', NULL, 1, 'No bean named \'getSftpFileUpOssTask\' available', '2024-10-21 15:58:32', 0);
INSERT INTO `sys_oper_log` VALUES (124, '定时任务', 1, 'com.ruoyi.quartz.controller.SysJobController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/monitor/job', '***********', 'XX XX', '{\"concurrent\":\"1\",\"cronExpression\":\"0 0 18 * * ?\",\"invokeTarget\":\"getSftpFileUpOssTask.updateForDay()\",\"jobGroup\":\"SYSTEM\",\"jobName\":\"获取sftp文件目录上传到oss\",\"misfirePolicy\":\"1\",\"nextValidTime\":\"2024-10-21 18:00:00\",\"params\":{},\"status\":\"0\"}', NULL, 1, 'No bean named \'getSftpFileUpOssTask\' available', '2024-10-21 15:58:34', 1);
INSERT INTO `sys_oper_log` VALUES (125, '定时任务', 1, 'com.ruoyi.quartz.controller.SysJobController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/monitor/job', '***********', 'XX XX', '{\"concurrent\":\"1\",\"cronExpression\":\"0 0 18 * * ?\",\"invokeTarget\":\"getSftpFileUpOssTask.updateForDay()\",\"jobGroup\":\"SYSTEM\",\"jobName\":\"获取sftp文件目录上传到oss\",\"misfirePolicy\":\"1\",\"nextValidTime\":\"2024-10-21 18:00:00\",\"params\":{},\"status\":\"0\"}', NULL, 1, 'No bean named \'getSftpFileUpOssTask\' available', '2024-10-21 16:00:39', 0);
INSERT INTO `sys_oper_log` VALUES (126, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"30\"],\"status\":\"0\",\"userNameList\":[\"admin\",\"ry\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-16 15:14:45', 304);
INSERT INTO `sys_oper_log` VALUES (127, '字典类型', 1, 'com.ruoyi.web.controller.system.SysDictTypeController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/system/dict/type', '***********', 'XX XX', '{\"createBy\":\"admin\",\"dictName\":\"客服启用停用状态\",\"dictType\":\"kf_status_type\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-16 15:18:56', 11);
INSERT INTO `sys_oper_log` VALUES (128, '字典类型', 9, 'com.ruoyi.web.controller.system.SysDictTypeController.refreshCache()', 'DELETE', 1, 'admin', '客服管理系统TOP', '/system/dict/type/refreshCache', '***********', 'XX XX', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-16 15:24:02', 13);
INSERT INTO `sys_oper_log` VALUES (129, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/system/role', '***********', 'XX XX', '{\"admin\":false,\"createTime\":\"2024-09-27 13:47:03\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2001,1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2003,2014,2054,2016,2023],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-23 14:02:18', 19);
INSERT INTO `sys_oper_log` VALUES (130, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/system/role', '***********', 'XX XX', '{\"admin\":false,\"createTime\":\"2024-09-27 13:47:03\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2001,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2033,2034,2035,2036,2053,2003,2037,2038,2039,2040,2041,2042,2043,2044,2014,2055,2056,2057,2054,2058,2059,2016,2060,2061,2062,2063,2064,2065,2023,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-23 16:21:19', 15);
INSERT INTO `sys_oper_log` VALUES (131, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/system/role', '***********', 'XX XX', '{\"admin\":false,\"createTime\":\"2024-09-27 13:47:03\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2001,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2033,2034,2035,2036,2053,2003,2037,2038,2039,2040,2041,2042,2043,2044,2014,2055,2056,2057,2054,2058,2059,2016,2060,2061,2062,2063,2064,2065,2023,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-23 16:30:05', 16);
INSERT INTO `sys_oper_log` VALUES (132, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'demo', '客服部', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_GW\",\"channelName\":\"官网\",\"companyCode\":\"KF_CQTR\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 16:43:38\",\"id\":31,\"lastUpdateTime\":\"2024-10-11\",\"params\":{},\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-23 16:38:53', 10);
INSERT INTO `sys_oper_log` VALUES (133, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'demo', '客服部', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_GW\",\"channelName\":\"官网\",\"companyCode\":\"KF_CQTR\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 16:43:38\",\"id\":31,\"lastUpdateTime\":\"2024-10-11\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-23 16:39:05', 7);
INSERT INTO `sys_oper_log` VALUES (134, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'demo', '客服部', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"30\"],\"status\":\"0\",\"userNameList\":[\"demo\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-23 16:53:04', 8);
INSERT INTO `sys_oper_log` VALUES (135, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'demo', '客服部', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"17\",\"18\",\"19\",\"20\"],\"status\":\"0\",\"userNameList\":[\"demo\",\"ry\",\"admin\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-23 16:56:04', 8);
INSERT INTO `sys_oper_log` VALUES (136, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'demo', '客服部', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"30\",\"31\",\"32\",\"33\",\"35\"],\"status\":\"0\"}', '{\"msg\":\"请求参数缺失。\",\"code\":500}', 0, NULL, '2024-12-23 16:56:15', 1);
INSERT INTO `sys_oper_log` VALUES (137, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'demo', '客服部', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"30\",\"31\",\"32\",\"33\",\"35\"],\"status\":\"0\"}', '{\"msg\":\"请求参数缺失。\",\"code\":500}', 0, NULL, '2024-12-23 16:56:25', 0);
INSERT INTO `sys_oper_log` VALUES (138, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'demo', '客服部', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"1\",\"2\",\"3\",\"4\",\"34\"],\"status\":\"0\",\"userNameList\":[\"admin\",\"ry\",\"demo\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-23 16:57:05', 11);
INSERT INTO `sys_oper_log` VALUES (139, '菜单管理', 1, 'com.ruoyi.web.controller.system.SysMenuController.add()', 'POST', 1, 'demo', '客服部', '/system/menu', '***********', 'XX XX', '{\"children\":[],\"createBy\":\"demo\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"查询附件列表信息\",\"menuType\":\"F\",\"orderNum\":8,\"params\":{},\"parentId\":2003,\"perms\":\"kf:work:getFileListInfo\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-24 17:22:24', 12);
INSERT INTO `sys_oper_log` VALUES (140, '菜单管理', 1, 'com.ruoyi.web.controller.system.SysMenuController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/system/menu', '***********', 'XX XX', '{\"children\":[],\"createBy\":\"admin\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"查询附件列表\",\"menuType\":\"F\",\"orderNum\":0,\"params\":{},\"parentId\":2001,\"perms\":\"kf:work:getFileListInfo\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-24 17:28:01', 6);
INSERT INTO `sys_oper_log` VALUES (141, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/system/menu', '***********', 'XX XX', '{\"children\":[],\"createTime\":\"2024-12-24 17:28:01\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2077,\"menuName\":\"查询附件列表\",\"menuType\":\"F\",\"orderNum\":15,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"kf:work:getFileListInfo\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"admin\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-24 17:28:11', 6);
INSERT INTO `sys_oper_log` VALUES (142, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/system/role', '***********', 'XX XX', '{\"admin\":false,\"createTime\":\"2024-09-27 13:47:03\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2001,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2033,2034,2035,2036,2053,2077,2003,2037,2038,2039,2040,2041,2042,2043,2044,2076,2014,2055,2056,2057,2054,2058,2059,2016,2060,2061,2062,2063,2064,2065,2023,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-24 17:30:35', 13);
INSERT INTO `sys_oper_log` VALUES (143, '菜单管理', 2, 'com.ruoyi.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'demo', '客服部', '/system/menu', '***********', 'XX XX', '{\"children\":[],\"createTime\":\"2024-12-13 16:38:18\",\"icon\":\"#\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuId\":2053,\"menuName\":\"下载附件\",\"menuType\":\"F\",\"orderNum\":14,\"params\":{},\"parentId\":2001,\"path\":\"\",\"perms\":\"kf:work:downloadFileZip\",\"routeName\":\"\",\"status\":\"0\",\"updateBy\":\"demo\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-25 11:02:55', 7);
INSERT INTO `sys_oper_log` VALUES (144, '菜单管理', 1, 'com.ruoyi.web.controller.system.SysMenuController.add()', 'POST', 1, 'demo', '客服部', '/system/menu', '***********', 'XX XX', '{\"children\":[],\"createBy\":\"demo\",\"isCache\":\"0\",\"isFrame\":\"1\",\"menuName\":\"下载附件\",\"menuType\":\"F\",\"orderNum\":10,\"params\":{},\"parentId\":2003,\"perms\":\"kf:work:downloadFileZip\",\"status\":\"0\",\"visible\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-25 11:03:18', 6);
INSERT INTO `sys_oper_log` VALUES (145, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/system/role', '***********', 'XX XX', '{\"admin\":false,\"createTime\":\"2024-09-27 13:47:03\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2001,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2033,2034,2035,2036,2053,2077,2003,2037,2038,2039,2040,2041,2042,2043,2044,2076,2078,2014,2055,2056,2057,2054,2058,2059,2016,2060,2061,2062,2063,2064,2065,2023,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-25 11:22:38', 15);
INSERT INTO `sys_oper_log` VALUES (146, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/system/role', '***********', 'XX XX', '{\"admin\":false,\"createTime\":\"2024-09-27 13:47:03\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2001,1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2033,2034,2035,2036,2053,2003,2037,2038,2039,2040,2041,2042,2043,2044,2076,2078,2014,2055,2056,2057,2054,2058,2059,2016,2060,2061,2062,2063,2064,2065,2023,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-25 11:24:51', 16);
INSERT INTO `sys_oper_log` VALUES (147, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/system/role', '***********', 'XX XX', '{\"admin\":false,\"createTime\":\"2024-09-27 13:47:03\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2001,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2033,2034,2035,2036,2053,2077,2003,2037,2038,2039,2040,2041,2042,2043,2044,2076,2078,2014,2055,2056,2057,2054,2058,2059,2016,2060,2061,2062,2063,2064,2065,2023,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-25 11:24:55', 12);
INSERT INTO `sys_oper_log` VALUES (148, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'demo', '客服部', '/system/role', '***********', 'XX XX', '{\"admin\":false,\"createTime\":\"2024-09-27 13:47:03\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2001,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2033,2034,2035,2036,2053,2077,2003,2037,2038,2039,2040,2041,2042,2043,2044,2076,2078,2014,2055,2056,2057,2054,2058,2059,2016,2060,2061,2062,2063,2064,2065,2023,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"demo\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-25 11:56:55', 21);
INSERT INTO `sys_oper_log` VALUES (149, '角色管理', 2, 'com.ruoyi.web.controller.system.SysRoleController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/system/role', '***********', 'XX XX', '{\"admin\":false,\"createTime\":\"2024-09-27 13:47:03\",\"dataScope\":\"1\",\"delFlag\":\"0\",\"deptCheckStrictly\":true,\"flag\":false,\"menuCheckStrictly\":true,\"menuIds\":[2003,1,100,1000,1001,1002,1003,1004,1005,1006,101,1007,1008,1009,1010,1011,102,1012,1013,1014,1015,103,1016,1017,1018,1019,104,1020,1021,1022,1023,1024,105,1025,1026,1027,1028,1029,106,1030,1031,1032,1033,1034,107,1035,1036,1037,1038,108,500,1039,1040,1041,501,1042,1043,1044,1045,2,109,1046,1047,1048,110,1049,1050,1051,1052,1053,1054,111,112,113,114,3,115,116,1055,1056,1057,1058,1059,1060,117,2001,2004,2005,2006,2007,2008,2009,2010,2011,2012,2013,2033,2034,2035,2036,2053,2077,2037,2038,2039,2040,2041,2042,2043,2044,2076,2014,2055,2056,2057,2054,2058,2059,2016,2060,2061,2062,2063,2064,2065,2023,2066,2067,2068,2069,2070,2071,2072,2073,2074,2075],\"params\":{},\"remark\":\"普通角色\",\"roleId\":2,\"roleKey\":\"common\",\"roleName\":\"普通角色\",\"roleSort\":2,\"status\":\"0\",\"updateBy\":\"admin\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-25 11:58:48', 16);
INSERT INTO `sys_oper_log` VALUES (150, '客服人员', 3, 'com.ruoyi.web.controller.order.KfAccountInfoController.remove()', 'GET', 1, 'admin', '客服管理系统TOP', '/kf/account/remove', '***********', 'XX XX', '{\"accountCode\":\"admin\",\"channelId\":\"30\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-25 14:38:08', 7);
INSERT INTO `sys_oper_log` VALUES (151, '客服人员', 3, 'com.ruoyi.web.controller.order.KfAccountInfoController.remove()', 'GET', 1, 'admin', '客服管理系统TOP', '/kf/account/remove', '***********', 'XX XX', '{\"accountCode\":\"admin\",\"channelId\":\"\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-25 14:38:29', 6);
INSERT INTO `sys_oper_log` VALUES (152, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"33\",\"32\",\"31\",\"30\",\"35\"],\"status\":\"0\",\"userNameList\":[]}', '{\"msg\":\"请求参数缺失。\",\"code\":500}', 0, NULL, '2024-12-26 14:27:27', 5);
INSERT INTO `sys_oper_log` VALUES (153, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"33\",\"32\",\"31\",\"30\",\"35\"],\"status\":\"0\",\"userNameList\":[\"admin\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-26 14:27:30', 10);
INSERT INTO `sys_oper_log` VALUES (154, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_GZH\",\"channelName\":\"公众号\",\"companyCode\":\"KF_ZBGX\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 16:47:40\",\"id\":3,\"lastUpdateTime\":\"2024-10-11\",\"params\":{},\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-26 16:06:38', 10);
INSERT INTO `sys_oper_log` VALUES (155, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_GZH\",\"channelName\":\"公众号\",\"companyCode\":\"KF_ZBGX\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 16:47:40\",\"id\":3,\"lastUpdateTime\":\"2024-10-11\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-26 16:06:49', 7);
INSERT INTO `sys_oper_log` VALUES (156, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_ZBGX\",\"companyFullName\":\"中保国信融资担保有限公司\",\"companyName\":\"中保国信\",\"createBy\":\"admin\",\"createTime\":\"2024-10-10 16:56:37\",\"id\":2,\"lastUpdateTime\":\"2024-10-10\",\"params\":{},\"remark\":\"中保国信融资担保有限公司\",\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2024-12-26 16:06:55', 9);
INSERT INTO `sys_oper_log` VALUES (157, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"17\",\"18\",\"19\",\"20\"],\"status\":\"0\",\"userNameList\":[\"admin\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-13 15:41:05', 7);
INSERT INTO `sys_oper_log` VALUES (158, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"21\",\"22\",\"23\",\"24\"],\"status\":\"0\",\"userNameList\":[\"admin\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-13 15:41:21', 7);
INSERT INTO `sys_oper_log` VALUES (159, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"25\",\"26\",\"27\",\"28\"],\"status\":\"0\",\"userNameList\":[\"admin\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-14 09:46:48', 7);
INSERT INTO `sys_oper_log` VALUES (160, '客服人员', 3, 'com.ruoyi.web.controller.order.KfAccountInfoController.remove()', 'GET', 1, 'admin', '客服管理系统TOP', '/kf/account/remove', '***********', 'XX XX', '{\"accountCode\":\"demo\",\"channelId\":\"\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-14 09:47:23', 6);
INSERT INTO `sys_oper_log` VALUES (161, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"17\",\"18\",\"19\",\"20\"],\"status\":\"0\",\"userNameList\":[\"demo\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-14 09:47:46', 7);
INSERT INTO `sys_oper_log` VALUES (162, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"25\",\"26\",\"27\",\"28\"],\"status\":\"0\",\"userNameList\":[\"ry\",\"demo\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-14 09:48:16', 8);
INSERT INTO `sys_oper_log` VALUES (163, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"5\"],\"status\":\"0\",\"userNameList\":[\"admin\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-14 09:48:32', 7);
INSERT INTO `sys_oper_log` VALUES (164, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_XCX\",\"channelName\":\"小程序\",\"companyCode\":\"KF_CQTR\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 09:43:51\",\"id\":1,\"lastUpdateTime\":\"2024-11-18\",\"params\":{},\"remark\":\"\",\"status\":\"0\"}', '{\"msg\":\"修改渠道失败，修改后的公司信息和渠道信息重复。\",\"code\":500}', 0, NULL, '2025-01-14 09:56:52', 3);
INSERT INTO `sys_oper_log` VALUES (165, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_XCX\",\"channelName\":\"小程序\",\"companyCode\":\"KF_FJDY\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 09:43:51\",\"id\":1,\"lastUpdateTime\":\"2024-11-18\",\"params\":{},\"remark\":\"\",\"status\":\"0\"}', '{\"msg\":\"修改渠道失败，修改后的公司信息和渠道信息重复。\",\"code\":500}', 0, NULL, '2025-01-14 09:56:56', 2);
INSERT INTO `sys_oper_log` VALUES (166, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_XCX\",\"channelName\":\"小程序\",\"companyCode\":\"KF_YNDF\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 09:43:51\",\"id\":1,\"lastUpdateTime\":\"2024-11-18\",\"params\":{},\"remark\":\"\",\"status\":\"0\"}', '{\"msg\":\"修改渠道失败，修改后的公司信息和渠道信息重复。\",\"code\":500}', 0, NULL, '2025-01-14 09:56:58', 2);
INSERT INTO `sys_oper_log` VALUES (167, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_GZH\",\"channelName\":\"公众号\",\"companyCode\":\"KF_CQTR\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 16:47:40\",\"id\":32,\"lastUpdateTime\":\"2024-10-11\",\"params\":{},\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-14 09:58:15', 6);
INSERT INTO `sys_oper_log` VALUES (168, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_XCX\",\"channelName\":\"小程序1\",\"companyCode\":\"KF_ZBGX\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 09:43:51\",\"id\":1,\"lastUpdateTime\":\"2024-11-18\",\"params\":{},\"remark\":\"\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-14 10:15:36', 6);
INSERT INTO `sys_oper_log` VALUES (169, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_XCX\",\"channelName\":\"小程序\",\"companyCode\":\"KF_ZBGX\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 09:43:51\",\"id\":1,\"lastUpdateTime\":\"2024-11-18\",\"params\":{},\"remark\":\"\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-14 10:15:40', 6);
INSERT INTO `sys_oper_log` VALUES (170, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_CQTR\",\"companyFullName\":\"重庆淘然融资担保有限公司\",\"companyName\":\"重庆淘然\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 16:43:03\",\"id\":1,\"lastUpdateTime\":\"2024-11-13\",\"params\":{},\"remark\":\"重庆淘然融资担保有限公司\",\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-14 10:27:11', 5);
INSERT INTO `sys_oper_log` VALUES (171, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_ZBGX\",\"companyFullName\":\"中保国信融资担保有限公司\",\"companyName\":\"中保国信\",\"createBy\":\"admin\",\"createTime\":\"2024-10-10 16:56:37\",\"id\":2,\"lastUpdateTime\":\"2024-10-10\",\"params\":{},\"remark\":\"中保国信融资担保有限公司\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-16 10:29:07', 16);
INSERT INTO `sys_oper_log` VALUES (172, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_YNDF\",\"companyFullName\":\"云南鼎丰融资担保有限公司\",\"companyName\":\"云南鼎丰\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 10:26:53\",\"id\":4,\"lastUpdateTime\":\"2024-10-11\",\"params\":{},\"remark\":\"云南鼎丰融资担保有限公司\",\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-16 10:29:17', 7);
INSERT INTO `sys_oper_log` VALUES (173, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_FJDY\",\"companyFullName\":\"福建大有鼎盛融资担保有限公司\",\"companyName\":\"福建大有\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 16:42:24\",\"id\":3,\"lastUpdateTime\":\"2024-11-13\",\"params\":{},\"remark\":\"福建大有鼎盛融资担保有限公司\",\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-17 13:50:53', 10);
INSERT INTO `sys_oper_log` VALUES (174, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"1\",\"2\",\"3\",\"4\",\"34\"],\"status\":\"0\",\"userNameList\":[\"admin\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-20 16:34:40', 17);
INSERT INTO `sys_oper_log` VALUES (175, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_YNDF\",\"companyFullName\":\"云南鼎丰融资担保有限公司\",\"companyName\":\"云南鼎丰\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 10:26:53\",\"id\":4,\"lastUpdateTime\":\"2024-10-11\",\"params\":{},\"remark\":\"云南鼎丰融资担保有限公司\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-21 09:24:19', 15);
INSERT INTO `sys_oper_log` VALUES (176, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_FJDY\",\"companyFullName\":\"福建大有鼎盛融资担保有限公司\",\"companyName\":\"福建大有\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 16:42:24\",\"id\":3,\"lastUpdateTime\":\"2024-11-13\",\"params\":{},\"remark\":\"福建大有鼎盛融资担保有限公司\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-21 09:24:22', 10);
INSERT INTO `sys_oper_log` VALUES (177, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_CQTR\",\"companyFullName\":\"重庆淘然融资担保有限公司\",\"companyName\":\"重庆淘然\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 16:43:03\",\"id\":1,\"lastUpdateTime\":\"2024-11-13\",\"params\":{},\"remark\":\"重庆淘然融资担保有限公司\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-21 09:24:25', 9);
INSERT INTO `sys_oper_log` VALUES (178, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/channel', '***********', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_GZH\",\"channelName\":\"公众号\",\"companyCode\":\"KF_CQTR\",\"createBy\":\"admin\",\"createTime\":\"2024-10-11 16:47:40\",\"id\":32,\"lastUpdateTime\":\"2024-10-11\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-21 09:33:24', 12);
INSERT INTO `sys_oper_log` VALUES (179, '字典类型', 9, 'com.ruoyi.web.controller.system.SysDictTypeController.refreshCache()', 'DELETE', 1, 'admin', '客服管理系统TOP', '/system/dict/type/refreshCache', '***********', 'XX XX', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-21 09:34:11', 16);
INSERT INTO `sys_oper_log` VALUES (180, '客服人员', 3, 'com.ruoyi.web.controller.order.KfAccountInfoController.remove()', 'GET', 1, 'admin', '客服管理系统TOP', '/kf/account/remove', '***********', 'XX XX', '{\"accountCode\":\"admin\",\"channelId\":\"\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-21 09:44:50', 8);
INSERT INTO `sys_oper_log` VALUES (181, '客服-公司管理', 1, 'com.ruoyi.web.controller.order.KfCompanyInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_QHCT\",\"companyFullName\":\"1\",\"companyName\":\"青海昌泰\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"当前公司已经存在！\",\"code\":500}', 0, NULL, '2025-01-21 09:59:26', 2);
INSERT INTO `sys_oper_log` VALUES (182, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_ZBGX\",\"companyFullName\":\"中保国信融资担保有限公司\",\"companyName\":\"中保国信\",\"createBy\":\"admin\",\"createTime\":\"2024-10-10 16:56:37\",\"id\":2,\"lastUpdateTime\":\"2024-10-10\",\"params\":{},\"remark\":\"中保国信融资担保有限公司\",\"status\":\"1\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-21 09:59:34', 6);
INSERT INTO `sys_oper_log` VALUES (183, '客服-公司管理', 1, 'com.ruoyi.web.controller.order.KfCompanyInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_ZBGX\",\"companyFullName\":\"11\",\"companyName\":\"中保国信\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"当前公司已经存在！\",\"code\":500}', 0, NULL, '2025-01-21 09:59:39', 2);
INSERT INTO `sys_oper_log` VALUES (184, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '***********', 'XX XX', '{\"companyCode\":\"KF_ZBGX\",\"companyFullName\":\"中保国信融资担保有限公司\",\"companyName\":\"中保国信\",\"createBy\":\"admin\",\"createTime\":\"2024-10-10 16:56:37\",\"id\":2,\"lastUpdateTime\":\"2024-10-10\",\"params\":{},\"remark\":\"中保国信融资担保有限公司\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-21 09:59:43', 9);
INSERT INTO `sys_oper_log` VALUES (185, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"1\",\"2\",\"4\",\"3\",\"34\"],\"status\":\"0\"}', '{\"msg\":\"请求参数缺失。\",\"code\":500}', 0, NULL, '2025-01-21 10:04:56', 0);
INSERT INTO `sys_oper_log` VALUES (186, '客服人员', 1, 'com.ruoyi.web.controller.order.KfAccountInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/account/add', '***********', 'XX XX', '{\"channelIdList\":[\"1\",\"2\",\"4\",\"3\",\"34\"],\"status\":\"0\",\"userNameList\":[\"admin\"]}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-01-21 10:04:59', 10);
INSERT INTO `sys_oper_log` VALUES (187, '字典数据', 1, 'com.ruoyi.web.controller.system.SysDictDataController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/system/dict/data', '8.152.154.23', 'XX XX', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"福建创伟\",\"dictSort\":0,\"dictType\":\"kf_company_list\",\"dictValue\":\"KF_FJCW\",\"listClass\":\"default\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-05-09 13:50:54', 15);
INSERT INTO `sys_oper_log` VALUES (188, '字典数据', 1, 'com.ruoyi.web.controller.system.SysDictDataController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/system/dict/data', '8.152.154.23', 'XX XX', '{\"createBy\":\"admin\",\"default\":false,\"dictLabel\":\"甘肃嘉德\",\"dictSort\":0,\"dictType\":\"kf_company_list\",\"dictValue\":\"KF_GSJD\",\"listClass\":\"default\",\"params\":{},\"remark\":\"\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-05-09 13:51:28', 10);
INSERT INTO `sys_oper_log` VALUES (189, '字典类型', 9, 'com.ruoyi.web.controller.system.SysDictTypeController.refreshCache()', 'DELETE', 1, 'admin', '客服管理系统TOP', '/system/dict/type/refreshCache', '8.152.154.23', 'XX XX', '{}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-05-09 13:51:46', 16);
INSERT INTO `sys_oper_log` VALUES (190, '客服-公司管理', 1, 'com.ruoyi.web.controller.order.KfCompanyInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/company', '8.152.154.23', 'XX XX', '{\"companyCode\":\"KF_FJCW\",\"companyFullName\":\"福建创伟国信融资担保有限公司\",\"companyName\":\"福建创伟\",\"createBy\":\"admin\",\"createTime\":\"2025-05-09 13:53:35\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-05-09 13:53:35', 9);
INSERT INTO `sys_oper_log` VALUES (191, '客服-公司管理', 2, 'com.ruoyi.web.controller.order.KfCompanyInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/company', '8.152.154.23', 'XX XX', '{\"companyCode\":\"KF_FJCW\",\"companyFullName\":\"福建创伟国信融资担保有限公司\",\"companyName\":\"福建创伟\",\"createBy\":\"admin\",\"createTime\":\"2025-05-09 13:53:36\",\"id\":9,\"lastUpdateTime\":\"2025-05-09\",\"params\":{},\"remark\":\"福建创伟国信融资担保有限公司\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-05-09 13:53:43', 12);
INSERT INTO `sys_oper_log` VALUES (192, '客服-公司管理', 1, 'com.ruoyi.web.controller.order.KfCompanyInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/company', '8.152.154.23', 'XX XX', '{\"companyCode\":\"KF_GSJD\",\"companyFullName\":\"甘肃嘉德融资担保有限公司\",\"companyName\":\"甘肃嘉德\",\"createBy\":\"admin\",\"createTime\":\"2025-05-09 13:53:52\",\"params\":{},\"remark\":\"甘肃嘉德融资担保有限公司\",\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-05-09 13:53:52', 7);
INSERT INTO `sys_oper_log` VALUES (193, '渠道', 1, 'com.ruoyi.web.controller.order.KfChannelInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/channel', '8.152.154.23', 'XX XX', '{\"channelCode\":\"小程序\",\"channelName\":\"KF_CHANNEL_XCX\",\"companyCode\":\"KF_FJCW\",\"createBy\":\"admin\",\"createTime\":\"2025-05-09 13:54:27\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-05-09 13:54:27', 13);
INSERT INTO `sys_oper_log` VALUES (194, '渠道', 1, 'com.ruoyi.web.controller.order.KfChannelInfoController.add()', 'POST', 1, 'admin', '客服管理系统TOP', '/kf/channel', '8.152.154.23', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_XCX\",\"channelName\":\"小程序\",\"companyCode\":\"KF_GSJD\",\"createBy\":\"admin\",\"createTime\":\"2025-05-09 13:54:43\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-05-09 13:54:43', 7);
INSERT INTO `sys_oper_log` VALUES (195, '渠道', 2, 'com.ruoyi.web.controller.order.KfChannelInfoController.edit()', 'PUT', 1, 'admin', '客服管理系统TOP', '/kf/channel', '8.152.154.23', 'XX XX', '{\"channelCode\":\"KF_CHANNEL_XCX\",\"channelName\":\"小程序\",\"companyCode\":\"KF_FJCW\",\"createBy\":\"admin\",\"createTime\":\"2025-05-09 13:54:27\",\"id\":36,\"lastUpdateTime\":\"2025-05-09\",\"params\":{},\"status\":\"0\"}', '{\"msg\":\"操作成功\",\"code\":200}', 0, NULL, '2025-05-09 13:55:10', 7);

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint NOT NULL AUTO_INCREMENT COMMENT '岗位ID',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '岗位信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_post
-- ----------------------------
INSERT INTO `sys_post` VALUES (1, 'ceo', '董事长', 1, '0', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_post` VALUES (2, 'se', '项目经理', 2, '0', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_post` VALUES (3, 'hr', '人力资源', 3, '0', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_post` VALUES (4, 'user', '普通员工', 4, '0', 'admin', '2024-09-27 13:47:03', '', NULL, '');
INSERT INTO `sys_post` VALUES (5, 'kfry', '客服人员', 5, '0', 'admin', '2024-09-27 14:24:51', '', NULL, '客服工作人员');

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `role_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 100 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '超级管理员', 'admin', 1, '1', 1, 1, '0', '0', 'admin', '2024-09-27 13:47:03', '', NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (2, '普通角色', 'common', 2, '1', 1, 1, '0', '0', 'admin', '2024-09-27 13:47:03', 'admin', '2024-12-25 11:58:48', '普通角色');

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO `sys_role_dept` VALUES (1, 100);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (2, 1);
INSERT INTO `sys_role_menu` VALUES (2, 2);
INSERT INTO `sys_role_menu` VALUES (2, 3);
INSERT INTO `sys_role_menu` VALUES (2, 100);
INSERT INTO `sys_role_menu` VALUES (2, 101);
INSERT INTO `sys_role_menu` VALUES (2, 102);
INSERT INTO `sys_role_menu` VALUES (2, 103);
INSERT INTO `sys_role_menu` VALUES (2, 104);
INSERT INTO `sys_role_menu` VALUES (2, 105);
INSERT INTO `sys_role_menu` VALUES (2, 106);
INSERT INTO `sys_role_menu` VALUES (2, 107);
INSERT INTO `sys_role_menu` VALUES (2, 108);
INSERT INTO `sys_role_menu` VALUES (2, 109);
INSERT INTO `sys_role_menu` VALUES (2, 110);
INSERT INTO `sys_role_menu` VALUES (2, 111);
INSERT INTO `sys_role_menu` VALUES (2, 112);
INSERT INTO `sys_role_menu` VALUES (2, 113);
INSERT INTO `sys_role_menu` VALUES (2, 114);
INSERT INTO `sys_role_menu` VALUES (2, 115);
INSERT INTO `sys_role_menu` VALUES (2, 116);
INSERT INTO `sys_role_menu` VALUES (2, 117);
INSERT INTO `sys_role_menu` VALUES (2, 500);
INSERT INTO `sys_role_menu` VALUES (2, 501);
INSERT INTO `sys_role_menu` VALUES (2, 1000);
INSERT INTO `sys_role_menu` VALUES (2, 1001);
INSERT INTO `sys_role_menu` VALUES (2, 1002);
INSERT INTO `sys_role_menu` VALUES (2, 1003);
INSERT INTO `sys_role_menu` VALUES (2, 1004);
INSERT INTO `sys_role_menu` VALUES (2, 1005);
INSERT INTO `sys_role_menu` VALUES (2, 1006);
INSERT INTO `sys_role_menu` VALUES (2, 1007);
INSERT INTO `sys_role_menu` VALUES (2, 1008);
INSERT INTO `sys_role_menu` VALUES (2, 1009);
INSERT INTO `sys_role_menu` VALUES (2, 1010);
INSERT INTO `sys_role_menu` VALUES (2, 1011);
INSERT INTO `sys_role_menu` VALUES (2, 1012);
INSERT INTO `sys_role_menu` VALUES (2, 1013);
INSERT INTO `sys_role_menu` VALUES (2, 1014);
INSERT INTO `sys_role_menu` VALUES (2, 1015);
INSERT INTO `sys_role_menu` VALUES (2, 1016);
INSERT INTO `sys_role_menu` VALUES (2, 1017);
INSERT INTO `sys_role_menu` VALUES (2, 1018);
INSERT INTO `sys_role_menu` VALUES (2, 1019);
INSERT INTO `sys_role_menu` VALUES (2, 1020);
INSERT INTO `sys_role_menu` VALUES (2, 1021);
INSERT INTO `sys_role_menu` VALUES (2, 1022);
INSERT INTO `sys_role_menu` VALUES (2, 1023);
INSERT INTO `sys_role_menu` VALUES (2, 1024);
INSERT INTO `sys_role_menu` VALUES (2, 1025);
INSERT INTO `sys_role_menu` VALUES (2, 1026);
INSERT INTO `sys_role_menu` VALUES (2, 1027);
INSERT INTO `sys_role_menu` VALUES (2, 1028);
INSERT INTO `sys_role_menu` VALUES (2, 1029);
INSERT INTO `sys_role_menu` VALUES (2, 1030);
INSERT INTO `sys_role_menu` VALUES (2, 1031);
INSERT INTO `sys_role_menu` VALUES (2, 1032);
INSERT INTO `sys_role_menu` VALUES (2, 1033);
INSERT INTO `sys_role_menu` VALUES (2, 1034);
INSERT INTO `sys_role_menu` VALUES (2, 1035);
INSERT INTO `sys_role_menu` VALUES (2, 1036);
INSERT INTO `sys_role_menu` VALUES (2, 1037);
INSERT INTO `sys_role_menu` VALUES (2, 1038);
INSERT INTO `sys_role_menu` VALUES (2, 1039);
INSERT INTO `sys_role_menu` VALUES (2, 1040);
INSERT INTO `sys_role_menu` VALUES (2, 1041);
INSERT INTO `sys_role_menu` VALUES (2, 1042);
INSERT INTO `sys_role_menu` VALUES (2, 1043);
INSERT INTO `sys_role_menu` VALUES (2, 1044);
INSERT INTO `sys_role_menu` VALUES (2, 1045);
INSERT INTO `sys_role_menu` VALUES (2, 1046);
INSERT INTO `sys_role_menu` VALUES (2, 1047);
INSERT INTO `sys_role_menu` VALUES (2, 1048);
INSERT INTO `sys_role_menu` VALUES (2, 1049);
INSERT INTO `sys_role_menu` VALUES (2, 1050);
INSERT INTO `sys_role_menu` VALUES (2, 1051);
INSERT INTO `sys_role_menu` VALUES (2, 1052);
INSERT INTO `sys_role_menu` VALUES (2, 1053);
INSERT INTO `sys_role_menu` VALUES (2, 1054);
INSERT INTO `sys_role_menu` VALUES (2, 1055);
INSERT INTO `sys_role_menu` VALUES (2, 1056);
INSERT INTO `sys_role_menu` VALUES (2, 1057);
INSERT INTO `sys_role_menu` VALUES (2, 1058);
INSERT INTO `sys_role_menu` VALUES (2, 1059);
INSERT INTO `sys_role_menu` VALUES (2, 1060);
INSERT INTO `sys_role_menu` VALUES (2, 2001);
INSERT INTO `sys_role_menu` VALUES (2, 2003);
INSERT INTO `sys_role_menu` VALUES (2, 2004);
INSERT INTO `sys_role_menu` VALUES (2, 2005);
INSERT INTO `sys_role_menu` VALUES (2, 2006);
INSERT INTO `sys_role_menu` VALUES (2, 2007);
INSERT INTO `sys_role_menu` VALUES (2, 2008);
INSERT INTO `sys_role_menu` VALUES (2, 2009);
INSERT INTO `sys_role_menu` VALUES (2, 2010);
INSERT INTO `sys_role_menu` VALUES (2, 2011);
INSERT INTO `sys_role_menu` VALUES (2, 2012);
INSERT INTO `sys_role_menu` VALUES (2, 2013);
INSERT INTO `sys_role_menu` VALUES (2, 2014);
INSERT INTO `sys_role_menu` VALUES (2, 2016);
INSERT INTO `sys_role_menu` VALUES (2, 2023);
INSERT INTO `sys_role_menu` VALUES (2, 2033);
INSERT INTO `sys_role_menu` VALUES (2, 2034);
INSERT INTO `sys_role_menu` VALUES (2, 2035);
INSERT INTO `sys_role_menu` VALUES (2, 2036);
INSERT INTO `sys_role_menu` VALUES (2, 2037);
INSERT INTO `sys_role_menu` VALUES (2, 2038);
INSERT INTO `sys_role_menu` VALUES (2, 2039);
INSERT INTO `sys_role_menu` VALUES (2, 2040);
INSERT INTO `sys_role_menu` VALUES (2, 2041);
INSERT INTO `sys_role_menu` VALUES (2, 2042);
INSERT INTO `sys_role_menu` VALUES (2, 2043);
INSERT INTO `sys_role_menu` VALUES (2, 2044);
INSERT INTO `sys_role_menu` VALUES (2, 2053);
INSERT INTO `sys_role_menu` VALUES (2, 2054);
INSERT INTO `sys_role_menu` VALUES (2, 2055);
INSERT INTO `sys_role_menu` VALUES (2, 2056);
INSERT INTO `sys_role_menu` VALUES (2, 2057);
INSERT INTO `sys_role_menu` VALUES (2, 2058);
INSERT INTO `sys_role_menu` VALUES (2, 2059);
INSERT INTO `sys_role_menu` VALUES (2, 2060);
INSERT INTO `sys_role_menu` VALUES (2, 2061);
INSERT INTO `sys_role_menu` VALUES (2, 2062);
INSERT INTO `sys_role_menu` VALUES (2, 2063);
INSERT INTO `sys_role_menu` VALUES (2, 2064);
INSERT INTO `sys_role_menu` VALUES (2, 2065);
INSERT INTO `sys_role_menu` VALUES (2, 2066);
INSERT INTO `sys_role_menu` VALUES (2, 2067);
INSERT INTO `sys_role_menu` VALUES (2, 2068);
INSERT INTO `sys_role_menu` VALUES (2, 2069);
INSERT INTO `sys_role_menu` VALUES (2, 2070);
INSERT INTO `sys_role_menu` VALUES (2, 2071);
INSERT INTO `sys_role_menu` VALUES (2, 2072);
INSERT INTO `sys_role_menu` VALUES (2, 2073);
INSERT INTO `sys_role_menu` VALUES (2, 2074);
INSERT INTO `sys_role_menu` VALUES (2, 2075);
INSERT INTO `sys_role_menu` VALUES (2, 2076);
INSERT INTO `sys_role_menu` VALUES (2, 2077);

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户昵称',
  `user_type` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '00' COMMENT '用户类型（00系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '帐号状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 102 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 100, 'admin', '超级管理员', '00', '<EMAIL>', '18888888888', '0', '', '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '0', '0', '**********', '2025-05-16 10:45:13', 'admin', '2024-09-27 13:47:03', '', '2025-05-16 10:45:13', '管理员');
INSERT INTO `sys_user` VALUES (2, 200, 'ry', '若依', '00', '<EMAIL>', '15666666666', '1', '', '$2a$10$59kOkjDvNvg2x1hClCO9oOxHja4ej0p6MFI1iPdXtjSVmdOANHTUa', '0', '0', '***********', '2024-10-21 17:11:25', 'admin', '2024-09-27 13:47:03', 'demo', '2024-10-21 17:11:25', '测试员');
INSERT INTO `sys_user` VALUES (101, 200, 'demo', 'demo', '00', '', '', '0', '', '$2a$10$rmVJCBNKLP1Njb9XUc8as.Gsx9dWvDzNkDKmXG0GMBEbH8iNVKtZ2', '0', '0', '***********', '2024-12-26 19:22:26', 'admin', '2024-10-10 15:31:09', 'admin', '2024-12-26 19:22:26', NULL);

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);
INSERT INTO `sys_user_post` VALUES (2, 4);
INSERT INTO `sys_user_post` VALUES (101, 5);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '用户和角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);
INSERT INTO `sys_user_role` VALUES (2, 2);
INSERT INTO `sys_user_role` VALUES (101, 2);

SET FOREIGN_KEY_CHECKS = 1;
