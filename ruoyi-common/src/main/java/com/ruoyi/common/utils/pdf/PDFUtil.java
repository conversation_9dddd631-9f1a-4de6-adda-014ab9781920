package com.ruoyi.common.utils.pdf;

import java.io.BufferedWriter;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintStream;
import java.io.StringWriter;
import java.nio.charset.Charset;
import java.util.Map;

import com.itextpdf.text.Document;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerHelper;

import freemarker.template.Configuration;
import freemarker.template.DefaultObjectWrapper;
import freemarker.template.Template;

public class PDFUtil {
    private String htmlFilePath;//文件相对路径     生成的html及pdf临时文件均存放在此路径中
    private String htmlFileName;//生成的html文件名  xxx.html
    private String pdfFileName;//生成的pdf文件名  xxx.pdf
    private String fontPath;//字体路径

    public PDFUtil() {}

    public PDFUtil(String htmlFilePath, String htmlFileName,String pdfFileName,String fontPath) {
        this.htmlFilePath=htmlFilePath;
        this.htmlFileName=htmlFileName;
        this.pdfFileName=pdfFileName;
        this.fontPath=fontPath;
    }

	public String getHtmlFilePath() {
		return htmlFilePath;
	}
	public void setHtmlFilePath(String htmlFilePath) {
		this.htmlFilePath = htmlFilePath;
	}
	public String getHtmlFileName() {
		return htmlFileName;
	}
	public void setHtmlFileName(String htmlFileName) {
		this.htmlFileName = htmlFileName;
	}
	public String getPdfFileName() {
		return pdfFileName;
	}
	public void setPdfFileName(String pdfFileName) {
		this.pdfFileName = pdfFileName;
	}
	public String getFontPath() {
		return fontPath;
	}
	public void setFontPath(String fontPath) {
		this.fontPath = fontPath;
	}

	/**
     * 填充模板
     * @param paramMap
     * @throws Exception
     */
    public  String  fillTemplate(Map<String, Object> paramMap) throws Exception {
        File modelFile = new File(htmlFilePath);
        if(!modelFile.exists()) {
            modelFile.mkdirs();
        }
        Configuration configuration = new Configuration(Configuration.VERSION_2_3_23);
        configuration.setDirectoryForTemplateLoading(modelFile);
        configuration.setObjectWrapper(new DefaultObjectWrapper(Configuration.VERSION_2_3_23));
        configuration.setDefaultEncoding("UTF-8");   //这个一定要设置，不然在生成的页面中 会乱码
        //获取或创建一个模版。
        Template template = configuration.getTemplate(htmlFileName);

        StringWriter stringWriter = new StringWriter();
        BufferedWriter writer = new BufferedWriter(stringWriter);
        template.process(paramMap, writer);

        String htmlStr = stringWriter.toString();
        writer.flush();
        writer.close();

        String tmpPath = htmlFilePath;// + "/temp";
        File tmepFilePath = new File(tmpPath);
        if (!tmepFilePath.exists()) {
            tmepFilePath.mkdirs();
        }
        String outputFile = tmpPath + "/" + pdfFileName;//+ File.separatorChar
        FileOutputStream outFile = new FileOutputStream(outputFile);
        createPDFFile(htmlStr, outFile);    

       return outputFile;
    }


    /**
     * 根据HTML字符串创建PDF文件
     * @param htmlStr
     * @param outputFile
     * @throws Exception 
     */
    private  void createPDFFile(String htmlStr,OutputStream os) throws Exception{
        ByteArrayInputStream bais = new ByteArrayInputStream(htmlStr.getBytes("UTF-8"));
        // step 1
        Document document = new Document();    
        try {
        	//设置纸张的大小，并且设置默认的pdf大小
        	document.setPageSize(new PageSize().A4);
            // step 2
            PdfWriter writer = PdfWriter.getInstance(document, os);
            // step 3
            document.open();
            FontProvider  provider = new FontProvider(fontPath);
            XMLWorkerHelper.getInstance().parseXHtml(writer, document, bais,null, Charset.forName("UTF-8"),provider);
            System.out.println("生成pdf文件成功!pdfFileName:"+pdfFileName);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }finally {
            try {
                document.close();
            } catch (Exception e) {
                e.printStackTrace();
                throw e;
            }
            try {
                bais.close();
            } catch (Exception e) {
                e.printStackTrace();
                throw e;
            }
        }
    }

    /**
     * 生成html模板
     * @param content
     * @return
     * @throws IOException 
     */
    public String createdHtmlTemplate(String content) throws IOException{
    	
    	
		/*
		 * content = content.replace("&nbsp;", " "); content =
		 * content.replace("&ndash;", "–"); content = content.replace("&mdash;", "—");
		 * content = content.replace("&lsquo;", "‘"); // left single quotation mark
		 * content = content.replace("&rsquo;", "’"); // right single quotation mark
		 * content = content.replace("&sbquo;", "‚"); // single low-9 quotation mark
		 * content = content.replace("&ldquo;", "“"); // left double quotation mark
		 * content = content.replace("&rdquo;", "”"); // right double quotation mark
		 * content = content.replace("&bdquo;", "„"); // double low-9 quotation mark
		 * content = content.replace("&prime;", "′"); // minutes content =
		 * content.replace("&Prime;", "″"); // seconds content =
		 * content.replace("&lsaquo;", "‹"); // single left angle quotation content =
		 * content.replace("&rsaquo;", "›"); // single right angle quotation content =
		 * content.replace("&oline;", "‾"); // overline
		 */
    	
    	



    	String fileName = htmlFilePath + "/" + htmlFileName;
    	File file = new File(htmlFilePath);
    	if(!file.isDirectory()) {
    		file.mkdir();
    	}
    	file = new File(fileName);
    	if(!file.isFile()) {
    		file.createNewFile();
    	}
    	//打开文件
    	PrintStream printStream = new PrintStream(new FileOutputStream(fileName));

    	//将HTML文件内容写入文件中
    	printStream.println(content);
    	printStream.flush();
    	printStream.close();
    	System.out.println("生成html模板成功!htmlFileName:"+htmlFileName);
    	return fileName;
    }
    /**
     * 读取模板内容
     * @param filePath
     * @return
     * @throws IOException 
     */
    @SuppressWarnings("null")
	public String readfile(String filePath) throws IOException{
        File file = new File(filePath);  
        InputStream input = null;
        try {
            input = new FileInputStream(file);
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            input.close();
            throw e;
        }  
        StringBuffer buffer = new StringBuffer();  
        byte[] bytes = new byte[1024];
        try {
            for(int n ; (n = input.read(bytes))!=-1 ; ){  
                buffer.append(new String(bytes,0,n,"UTF-8"));  
            }
        } catch (IOException e) {
        } finally {
        	input.close();
		}
        return buffer.toString();  
    }
    

}
