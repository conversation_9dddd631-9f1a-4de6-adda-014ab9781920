<template>
  <div style="padding: 16px">
    <p>说明：本页面展示在OA系统发起支付信息费流程后，同步至本系统的情况。如果成功，将在对应项目记录一笔已支付信息费金额</p>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column prop="oaCompleteTime" label="OA流程完成时间">
        <template slot-scope="scope">
                  <span>{{
                    parseTime(scope.row.oaCompleteTime, "{y}-{m}-{d} {h}:{mi}:{s}")
                  }}</span>
                </template>
      </el-table-column>
      <el-table-column prop="unitName" label="公司" />
      <el-table-column prop="templateName" label="流程模板" />
      <el-table-column prop="nickName" label="发起人"/>
      <el-table-column prop="projectName" label="项目名称" />
      <el-table-column prop="date" label="项目类型" >
        <template slot-scope="scope">{{ scope.row.projectType | projectType }}</template>
      </el-table-column>
      <!-- <el-table-column prop="payName" label="付款人" width="180" />
      <el-table-column prop="payNumber" label="付款人尾号" width="180" />
      <el-table-column prop="collName" label="收款人" width="180" />
      <el-table-column prop="collNumber" label="收款人尾号" width="180" />-->
      <el-table-column prop="date" label="收款人是否为信息费公司">
        <template slot-scope="scope" slot="header">
          <span>
            收款人是否为信息费公司
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div
                slot="content"
                style="width: 227px"
              >如果OA的支付信息费流程中填写的收款人为该项目在本系统已录入的信息费公司，则支付金额会同步到本系统，视为该信息费公司的已支付金额</div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </span>
        </template>
        <template slot-scope="scope">{{ scope.row.isRebateCom=='N'?'否':'是' }}</template>
      </el-table-column>
      <el-table-column prop="amount" label="支付金额" />
      <el-table-column prop="date" label="同步状态">
        <template slot-scope="scope" slot="header">
          <span>
            同步状态
            <el-tooltip class="item" effect="dark" placement="top-start">
              <div slot="content" style="width: 227px">
                OA支付信息费流程成功后，是否同步到本系统对应项目的已支付信息费金额。
                如果在本系统找不到对应项目，则同步失败
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </span>
        </template>
        <template slot-scope="scope">{{ scope.row.synStatus=='N'?'未同步':'已同步' }}</template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { oasystemList } from "@/api/cwxmgl/income";

export default {
  data() {
    return {
      tableData: [],
      total: 0,
      queryParams: {
        pageNum: 1,
        pageSize: 10
      }
    };
  },
  mounted() {
    this.getList();
  },
  filters: {
    projectType(e) {
      if (e == 1) {
        return "法催业务";
      } else if (e == 2) {
        return "分润业务";
      } else if (e == 3) {
        return "保函业务";
      } else if (e == 0) {
        return "通道业务";
      } else {
        return "--";
      }
    }
  },
  methods: {
    getList() {
      oasystemList(this.queryParams).then(res => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.total = res.total;
        }
      });
    }
  }
};
</script>

<style>
</style>