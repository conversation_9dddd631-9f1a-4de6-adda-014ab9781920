import { getToken } from "@/utils/auth";

export default {
  data() {
    return {
      fileListPro: [],
      fileListBecome: [],
      uploadPro: Object.freeze({
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/personnel/formal/uploadFilePro",
      }),
      uploadBecome: Object.freeze({
        // 是否禁用上传
        // isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: "Bearer " + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + "/personnel/formal/uploadFileBecome",
      }),
    };
  },
  watch: {
    "form.filesPro": {
      handler(val) {
        if (val && val.length) {
          this.fileListPro = val.map((item) => {
            return {
              name: item.fileName,
              id: item.id,
              url: process.env.VUE_APP_BASE_API + item.fileUrl,
              downLoadUrl: item.fileUrl,
            };
          });
        } else {
          this.fileListPro = [];
        }
        this.getFileListIdsPro(this.fileListPro);
      },
      immediate: true,
    },
    "form.filesBecome": {
      handler(val) {
        if (val && val.length) {
          this.fileListBecome = val.map((item) => {
            return {
              name: item.fileName,
              id: item.id,
              url: process.env.VUE_APP_BASE_API + item.fileUrl,
              downLoadUrl: item.fileUrl,
            };
          });
        } else {
          this.fileListBecome = [];
        }
        this.getFileListIdsBecome(this.fileListBecome);
      },
      immediate: true,
    },
  },
  methods: {
    handleFileSuccessPro(response, file, fileList) {
      this.fileListPro = fileList;
      this.getFileListIdsPro(fileList);
    },
    handleRemovePro(file, fileList) {
      this.getFileListIdsPro(fileList);
    },
    getFileListIdsPro(value) {
      const fileListIds = value.map((item) => item.id || item.response.data.id);
      this.$set(
        this.form,
        "professionalFileIds",
        fileListIds.length ? fileListIds : undefined
      );
    },
    handleFileSuccessBecome(response, file, fileList) {
      this.fileListBecome = fileList;
      this.getFileListIdsBecome(fileList);
    },
    handleRemoveBecome(file, fileList) {
      this.getFileListIdsBecome(fileList);
    },
    getFileListIdsBecome(value) {
      const fileListIds = value.map((item) => item.id || item.response.data.id);
      this.$set(
        this.form,
        "becomeFileIds",
        fileListIds.length ? fileListIds : undefined
      );
    },
    beforeUpload(file) {
      // 截取上传文件的后缀名
      let fileType = file.name.substring(file.name.lastIndexOf(".") + 1);
      const fileTypeList = [
        "word",
        "doc",
        "docx",
        "xls",
        "xlsx",
        "PPT",
        "PPTx",
        "pdf",
        "jpg",
        "jpeg",
        "png",
      ];
      if (!fileTypeList.includes(fileType)) {
        this.$message.error(`文件类型必须为${fileTypeList}格式`);
        return false;
      }
    },
    getFileList() {
      const filesPro = this.fileListPro.map((item) => {
        return {
          fileName: item.response?.data.fileName || item.name,
          fileUrl: item.response?.data.fileUrl || item.downLoadUrl,
          id: item.response?.data.id || item.id,
        };
      });
      const filesBecome = this.fileListBecome.map((item) => {
        return {
          fileName: item.response?.data.fileName || item.name,
          fileUrl: item.response?.data.fileUrl || item.downLoadUrl,
          id: item.response?.data.id || item.id,
        };
      });
      return { filesPro, filesBecome };
    },
  },
};
