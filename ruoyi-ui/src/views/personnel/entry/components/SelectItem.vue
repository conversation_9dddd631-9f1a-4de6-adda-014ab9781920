<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      :visible.sync="innerValue"
      width="1050px"
      @close="handleClose"
    >
      <div class="content">
        <el-table :data="tableList">
          <el-table-column label="员工姓名" align="center" prop="name" />
          <el-table-column
            label="入职岗位"
            align="center"
            prop="onboardingPostName"
          />
          <el-table-column
            label="入职部门"
            align="center"
            prop="onboardingDeptName"
          />
          <el-table-column label="状态" align="center">
            <template #default="{ row }">
              {{ auditState[row.auditState] }}
            </template>
          </el-table-column>
          <el-table-column
            label="身份证号码"
            align="center"
            prop="idCard"
            :show-overflow-tooltip="true"
          />

          <el-table-column
            label="入职日期"
            align="center"
            prop="onboardingTime"
            width="180"
          >
            <template #default="{ row }">
              <span>{{ parseTime(row.onboardingTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人" align="center" prop="createBy" />
          <el-table-column
            label="创建时间"
            align="center"
            prop="createTime"
            width="180"
          >
            <template #default="{ row }">
              <span>{{ parseTime(row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            class-name="small-padding fixed-width"
            fixed="right"
          >
            <template #default="{ row }">
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="handleDelete(row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="footer">
          <el-button type="primary" @click="onSubmit">确定</el-button>
          <el-button @click="innerValue = false">取消</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import vModelMixin from "@/mixin/v-model";
import config from "./config";

export default {
  mixins: [vModelMixin],

  props: {
    tableData: {
      type: Array,
      required: true,
      default: () => {
        return [];
      },
    },
  },
  watch: {
    tableData(val) {
      this.tableList = [...val];
      this.deleteId=[];
    },
  },
  data() {
    return {
      ...config,
      tableList: [],
      deleteId:[],
    };
  },
  mounted() {
    this.init();
  },
  methods: {
    async init() {},

    handleDelete(row) {
      this.tableList = this.tableList.filter((item) => item.id != row.id);
      this.deleteId.push(row.id);
    },
    onSubmit() {
      this.$emit("on-submit-success",this.deleteId);
    },
    onSave() {},
    handleClose() {},
  },
};
</script>

<style lang="less" scoped>
.content {
  max-height: 70vh;
  padding-right: 20px;
}
.footer {
  display: flex;
  justify-content: flex-end;
}
</style>