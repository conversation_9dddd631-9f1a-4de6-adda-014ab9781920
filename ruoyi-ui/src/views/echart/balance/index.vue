<template>
  <div  style="width: 1500px;height:1000px;" >
    <!--     start 进行添加-->
<!--   <div style="font-size: 15px"> <div :title="tipsMsg"  style="color: red"  ><pre>{{tipsMsg}}</pre></div></div>-->
    <div style="font-size: 15px;color:#999999;"><pre style="margin: 0px;height: 15px;">   </pre></div>
    <div style="font-size: 15px;color:#999999;"><pre style="margin: 0px;height: 22px;font-weight:600">   说明：</pre></div>
    <div style="font-size: 15px;color:#999999;"><pre style="margin: 0px;height: 22px;">    C：统计时点月末的在贷余额（未逾期的剩余未还本金，不含逾期未还）</pre></div>
    <div style="font-size: 15px;color:#999999;"><pre style="margin: 0px;height: 22px;">    M1：统计时点月末时，逾期1-30天的所有剩余未还本金</pre></div>
    <div style="font-size: 15px;color:#999999;"><pre style="margin: 0px;height: 22px;">    M2：统计时点月末时，逾期31-60天的所有剩余未还本金</pre></div>
    <div style="font-size: 15px;color:#999999;"><pre style="margin: 0px;height: 22px;">    M3：统计时点月末时，逾期61-90天的所有剩余未还本金</pre></div>
    <div style="font-size: 15px;color:#999999;"><pre style="margin: 0px;height: 22px;">    M4：统计时点月末时，逾期91-120天的所有剩余未还本金</pre></div>
    <div style="font-size: 15px;color:#999999;"><pre style="margin: 0px;height: 22px;">    M5：统计时点月末时，逾期121-150天的所有剩余未还本金</pre></div>
    <div style="font-size: 15px;color:#999999;"><pre style="margin: 0px;height: 22px;">    M6：统计时点月末时，逾期151-180天的所有剩余未还本金</pre></div>
    <div style="font-size: 15px;color:#999999;"><pre style="margin: 0px;height: 40px;">    M7+：统计时点月末时，逾期大于180天的所有剩余未还本金</pre></div>
<!-- end 结束表头-->
    <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="130px">
      <el-row>
         <el-col :span="112">
            <el-form-item label="系统" prop="platformNo">
              <el-select v-model="platformNoParam" placeholder="请选择系统名称" filterable multiple size="small"
                        >
                <el-option
                  v-for="dict in platformNoSelect"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="112">
            <el-form-item label="担保公司" prop="custNo">
              <el-select v-model="custNoParam" placeholder="请选择担保公司" filterable multiple size="small"
                        >
                <el-option
                  v-for="dict in custNoSelect"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="112">
            <el-form-item label="合作方" prop="partnerNo">
              <el-select v-model="partnerNoParam" placeholder="请选择合作方" filterable multiple size="small"
                        >
                <el-option
                  v-for="dict in partnerNoSelect"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
      </el-row>
      <el-row>

        <el-col :span="132">
          <el-form-item label="资金方" prop="fundNo">
            <el-select v-model="fundNoParam" placeholder="请选择资金方" filterable multiple size="small"
                      >
              <el-option
                v-for="dict in fundNoSelect"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="132">
          <el-form-item label="产品编码" prop="productNo">
            <el-select v-model="productNoParam" placeholder="请选择产品" filterable multiple size="small"
                      >
              <el-option
                v-for="dict in productNoSelect"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="132">
     <!--   end 前端页面变成多选页面    -->
        <el-form-item label="统计日期"></el-form-item>
         <el-date-picker
          v-model="queryParams.reconMonth"
          value-format="yyyy-MM"
          type="month"
          placeholder="选择月">
        </el-date-picker>
            <!-- <el-date-picker
              v-model="dateRange"
              type="monthrange"
              value-format="yyyy-MM"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              :picker-options="pickerOptions">
            </el-date-picker>-->
          </el-col>
        <el-col :span="132">
          <MoreSearch modelCode="ECHARTS" :params="queryParams" v-show="showMoreSearch" byId="companyCode"></MoreSearch>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery" class="ml-14">统计</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button @click="showMoreSearch=!showMoreSearch;queryParams.moreSearch=undefined" type="text"
              >更多搜索条件<i  :class="showMoreSearch?'el-icon-arrow-down':'el-icon-arrow-up'"/></el-button
            >
          </el-form-item>
        </el-col>
       </el-row>
      </el-form>

        <!-- <div id="charts"  style="width: 900px;height:350px; float:left"></div>  -->
        <div v-loading="loading" id="cakeCharts"  style="width: 1100px;height:500px; float:center"></div>
   </div>



</template>

<script>
import * as echarts from 'echarts';
import { listVintage,cakeData } from '@/api/system/echarts'
//  start 引用联级字典查询
import {getSysDictRefList,getSelectSysDictRefList} from '@/api/ref/ref'
//  end 引用联级字典查询
import {tipsMsg} from '../../data/common'
import { clone } from "xe-utils";

export default {
  name: 'Balance',
   dicts: ['sys_platform_code', 'product_no', 'fund_no', 'partner_no', 'platform_no', 'cust_no'],
  data() {
    return {


tipsMsg:null,
  // 日期范围
      //  start 新增参数
      platformNoParam: '',
      custNoParam: '',
      partnerNoParam: '',
      fundNoParam: '',
      productNoParam: '',


      sysDictRefParam: {
        dictType: '',
        dictValue: '',
        pDictType: '',
        pDictValue: '',
        selectDictDatas:''
      },
      platformNoSelect: [],
      custNoSelect: [],
      fundNoSelect: [],
      partnerNoSelect: [],
      productNoSelect: [],
      //  end 新增参数



      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
       // 表单参数
      form: {},

       // 查询参数
      queryParams: {
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        productNo: null,
        reconMonth:null,
        params: {
         moduleTypeOfNewAuth: 'ECHARTS',
        }
      },

      cakeEchartData:[],
      legendDatas:[],
      //产品映射字典列表
      stackbarXaxis:[],
      stackbarData:[],
      // 日期范围
      dateRange: [],
      showMoreSearch:false
    };
  },
  mounted() {
    this.nowtime();
    this.getList();
    // start 页面刷新时对数据的处理

    // this.initSelect()
     this.initSelectData()
    //  end 页面刷新时对数据的处理
    this.tipsMsg= tipsMsg

  },
  methods: {


      //start

      //wzy渲染下拉框
      initSelectData() {
      getSelectSysDictRefList({ unitType: 4,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.platformNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 0,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.custNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 3,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.productNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 2,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.fundNoSelect = response;
      });
      getSelectSysDictRefList({ unitType: 1,moduleTypeOfNewAuth: 'ECHARTS' }).then((response) => {
        this.partnerNoSelect = response;
      });
    },
     getCustNoList(val) {
      const flag = this.lateByte(this.queryParams.platformNo) > this.lateByte(val.toString())
      this.queryParams.platformNo = this.platformNoParam.toString()
      if (val == null || val === '' || flag) {
        this.custNoSelect = null
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.custNo = null
        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.custNoParam = null
        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {

            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = 'cust_no'
        this.sysDictRefParam.pDictType = 'platform_no'
        this.sysDictRefParam.pDictValue = this.queryParams.platformNo
        this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            // this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getPartnerNoList(val) {
      const flag = this.lateByte(this.queryParams.custNo) > this.lateByte(val.toString())
      this.queryParams.custNo = this.custNoParam.toString()
      if (val == null || val === '' || flag) {
        this.partnerNoSelect = null
        this.fundNoSelect = null
        this.productNoSelect = null

        // this.queryParams.partnerNo = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null

        // this.partnerNoParam = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'partner_no'
        // this.sysDictRefParam.dictValue=val
        this.sysDictRefParam.pDictType = 'cust_no'
        this.sysDictRefParam.pDictValue = this.queryParams.custNo
        this.sysDictRefParam.selectDictDatas =""

          if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            // this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getFundNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())
      this.queryParams.partnerNo = this.partnerNoParam.toString()
      if (val == null || val === ''|| flag) {
        this.fundNoSelect = null
        this.productNoSelect = null
        // this.queryParams.fundNo = null
        // this.queryParams.productNo = null
        // this.fundNoParam = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
         this.sysDictRefParam.selectDictDatas =""

      if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
      else {
        this.sysDictRefParam.dictType = 'fund_no'
        this.sysDictRefParam.pDictType = 'partner_no'
        this.sysDictRefParam.pDictValue = this.queryParams.partnerNo
        this.sysDictRefParam.selectDictDatas =""
         if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            // this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
    getProductNoList(val) {
      const flag = this.lateByte(this.queryParams.partnerNo) > this.lateByte(val.toString())

      this.queryParams.fundNo = this.fundNoParam.toString()
      if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        // this.productNoParam = null
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo
this.sysDictRefParam.selectDictDatas =""
       if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {
        this.sysDictRefParam.dictType = 'product_no'
        this.sysDictRefParam.pDictType = 'fund_no'
        this.sysDictRefParam.pDictValue = this.queryParams.fundNo

this.sysDictRefParam.selectDictDatas =""
        if(this.queryParams.platformNo!=null && this.queryParams.platformNo!=""){
          this.sysDictRefParam.selectDictDatas =this.queryParams.platformNo
        }
        if(this.queryParams.custNo!=null && this.queryParams.custNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.custNo
        }
        if(this.queryParams.partnerNo!=null && this.queryParams.partnerNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.partnerNo
        }
        if(this.queryParams.fundNo!=null && this.queryParams.fundNo!=""){
          this.sysDictRefParam.selectDictDatas = this.sysDictRefParam.selectDictDatas +"_"+this.queryParams.fundNo
        }

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
            this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            // this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }
    },
      getProductNoValue(val) {
      const flag = this.lateByte(this.queryParams.productNo) > this.lateByte(val.toString())

      this.queryParams.productNo = this.productNoParam.toString()

       if (val == null || val === '' ||flag) {
        this.productNoSelect = null
        // this.queryParams.productNo = null
        this.productNoParam = null
        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue =''
        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      } else {

        this.sysDictRefParam.dictType = ''
        this.sysDictRefParam.pDictType = 'product_no'
        this.sysDictRefParam.pDictValue = this.queryParams.productNo

        getSelectSysDictRefList(this.sysDictRefParam).then(response => {
          this.platformNoSelect= response.data.platform_no;
            this.custNoSelect= response.data.cust_no
            this.fundNoSelect= response.data.fund_no
            this.partnerNoSelect= response.data.partner_no
            this.productNoSelect= response.data.product_no
        })
      }

    },

      //end

    /**获取数据 */
    getList() {
      this.loading = true;
      this.queryParams.platformNo = this.platformNoParam.toString();
      this.queryParams.custNo = this.custNoParam.toString();
      this.queryParams.partnerNo = this.partnerNoParam.toString();
      this.queryParams.fundNo = this.fundNoParam.toString();
      this.queryParams.productNo = this.productNoParam.toString();
      const params=clone(this.queryParams,true);
      params.moreSearch=params.moreSearch&&JSON.stringify(params.moreSearch)
      cakeData(params).then(response=>{
        this.cakeEchartData = response.data
        this.cakeEchaerts();
        this.loading = false;
      })

    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        platformNo: null,
        custNo: null,
        partnerNo: null,
        fundNo: null,
        productNo: null,
        loanMonth: null,
        reconMonth: null,
        loanAmount: null,
        vintageDay: null,
        vintageType: null,
        vintageRate: null,
        status: '0',
        remark: null,
        createTime: null,
        updateTime: null
      }
      
      this.resetForm('form')
    },
     /** 搜索按钮操作 */
    handleQuery() {
      this.getList()
      // }
    },
    /** 重置按钮操作 */
    resetQuery() {

          //  start 重置逻辑更新
      this.resetForm('queryForm')
      this.queryParams.moreSearch=undefined;
      this.platformNoParam = ''
      this.custNoParam = ''
      this.partnerNoParam = ''
      this.fundNoParam = ''
      this.productNoParam = ''
      this.handleQuery()
      this.initSelectData()
      //  end 重置逻辑更新

    },

   // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    lateByte(sTargetStr) {
      var sTmpStr, sTmpChar;
      var nOriginLen = 0;
      var nStrLength = 0;

      sTmpStr = new String(sTargetStr);
      nOriginLen = sTmpStr.length;

      for (var i = 0; i < nOriginLen; i++) {
        sTmpChar = sTmpStr.charAt(i);

        if (escape(sTmpChar).length > 4) {
          nStrLength += 2;
        } else if (sTmpChar != '/r') {
          nStrLength++;
        }
      }
      return nStrLength;
    },

    //  end 新增方法


    //获取当前月份
    nowtime() {

      let nowDate = new Date();

      let date = {

        // 获取当前年份

        year: nowDate.getFullYear(),

        //获取当前月份


        month: (nowDate.getMonth() + 1 < 10 ? "0" + (nowDate.getMonth() + 1) : nowDate.getMonth()+1)-1,

        //获取当前日期

        date: nowDate.getDate(),
      };

        //拼接

      this.queryParams.reconMonth = date.year + "-" + date.month;
    },
    cakeEchaerts(){
             var myChart = this.$echarts.init(document.getElementById('cakeCharts'));
             var option;
          if (this.cakeEchartData.length == 0 ) {  //暂无数据
              option = {
                  title: {
                      text: '暂无数据',
                      x: 'center',
                      y: 'center',
                      textStyle: {
                        fontSize: 20,
                        fontWeight: 'normal',
                      }
                    }
              }
          } else{
              option={
                              title:{
                                  text:"余额分布",
                                  left:'center'

                              },
                                toolbox: {
                                show: true,
                                feature: {
                                      dataView: { readOnly: false },
                                      restore: {},
                                      saveAsImage: {},
                                }
                              },

                              tooltip: {
                                trigger: 'item',
                                formatter: "{b} : {c}({d}%)"
                              },
                                legend: {
                                      orient: 'vertical',
                                        left: 'left'
                                  },
                              series:[
                                  {
                                      name:'余额',
                                      type: 'pie',
                                      avoidLabelOverlap: false,
                                      radius: '60%',
                                      label: {
                                        normal:{
                                          show: true,
                                          // position: 'center'
                                        }
                                      },
                                      // emphasis: {
                                      //     label: {
                                      //     show: true,
                                      //     fontSize: '40',
                                      //     fontWeight: 'bold'
                                      //     }
                                      // },
                                    labelLine: {//设置延长线的长度
                                        normal: {
                                            length: 5,//设置延长线的长度
                                            length2: 10,//设置第二段延长线的长度
                                        }
                                    },
                                      data:this.cakeEchartData
                                  }
                              ]

                          };

                            myChart.clear()

                      }
                      myChart.setOption(option);
               }

  },
};
</script>
