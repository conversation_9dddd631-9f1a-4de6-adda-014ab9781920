export default {
  formColumns: [
    {
      label: "项目类型组合",
      prop: "projectPortfolioName",
      type: "input",
      placeholder: "请输入项目类型组合",
    },
    {
      label: "关联项目类型",
      prop: "projectTypeName",
      type: "input",
      placeholder: "请输入关联项目类型",
    },
  ],
  columns: Object.freeze([
    {
      label: "项目类型组合",
      prop: "projectPortfolioName",
    },
    { label: "编码", prop: "projectPortfolioCode" },
    { label: "关联项目类型", key: "projectTypeNameList" },
    { label: "说明", prop: "remark" },
    { label: "操作", key: "operate" },
  ]),

  formColumnsDialog: Object.freeze([
    {
      slotName: "projectPortfolio",
      type: "slot",
      span: 22,
    },
    {
      label: "编码:",
      prop: "projectPortfolioCode",
      type: "divText",
      span: 22,
    },
    {
      slotName: "projectTypeId",
      type: "slot",
      span: 22,
    },
    {
      label: "备注说明:",
      prop: "remark",
      type: "textarea",

      style: { width: "600px" },
      span: 22,
    },
  ]),
  formColumnsDialogView: Object.freeze([
    {
      label: "项目类型组合名称:",
      prop: "projectPortfolioName",
      type: "divText",
      span: 22,
    },
    {
      label: "编码:",
      prop: "projectPortfolioCode",
      type: "divText",
      span: 22,
    },
    {
      slotName: "projectTypeId",
      type: "slot",
      span: 22,
    },

    {
      label: "备注说明:",
      prop: "remark",
      type: "divText",
      span: 22,
    },
  ]),
  rules: Object.freeze({
    projectPortfolio: [
      { required: true, message: "项目类型组合名称", trigger: "change" },
    ],
    projectTypeIdList: [
      { required: true, message: "请选择关联项目类型", trigger: "change" },
    ],
  }),
};
