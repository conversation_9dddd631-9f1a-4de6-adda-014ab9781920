<template>
  <div>
    <el-dialog
      append-to-body
      :close-on-click-modal="false"
      title="查看详情"
      :visible.sync="innerValue"
      width="650px"
      @open="handleOpen"
    >
      <el-scrollbar>
        <div style="max-height: 80vh">
          <MyForm
            v-model="myForm"
            :columns="formColumnsDialogView"
            formType="false"
            labelWidth="145px"
            ref="form"
          >
            <template #projectTypeId>
              <el-form-item label="关联项目类型:">
                <el-tag
                  type="info"
                  v-for="(item, index) in myForm.projectTypeNameList"
                  :key="index"
                  class="mr-1"
                >
                  {{ item }}
                </el-tag>
              </el-form-item>
            </template>
          </MyForm>
        </div>
      </el-scrollbar>
      <span slot="footer" class="dialog-footer">
        <div class="flex justify-center">
          <el-button @click="innerValue = false" class="ml-3">关闭</el-button>
        </div>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { clone } from "xe-utils";
import vModelMixin from "@/mixin/v-model";
import config from "./config";
export default {
  mixins: [vModelMixin],
  props: {
    form: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  data() {
    return {
      ...config,
      myForm: {},
    };
  },
  watch: {},
  mounted() {
    this.init();
  },
  methods: {
    async init() {},

    handleOpen() {
      this.getMyForm();
    },
    async getMyForm() {
      this.myForm = clone(this.form, true);
    },
  },
};
</script>
