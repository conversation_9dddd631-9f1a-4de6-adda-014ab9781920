<template>
  <div>
    <el-descriptions
      class="margin-top"
      :column="2"
      direction="vertical"
      :colon="false"
      v-for="(item, index) in form"
      :key="index"
    >
      <el-descriptions-item label="批次号">{{
        item.outsourcedProjectNumber
      }}</el-descriptions-item>
      <el-descriptions-item label="案件状态">
        <el-tag type="warning" v-show="caseStatusObj[item.caseStatus]">{{
          caseStatusObj[item.caseStatus]
        }}</el-tag>
      </el-descriptions-item>
    </el-descriptions>
  </div>
</template>

<script>
import XEUtils from "xe-utils";
import { getCaseInformation } from "@/api/badSystem/assetManagement";

export default {
  name: "CaseInformation",
  components: {},
  data() {
    return {
      caseStatusObj: Object.freeze({
        1: "未分配",
        2: "已分配",
        3: "已撤案",
        4: "已找回",
        5: "对账中",
        6: "对账完成",
        7: "服务费用已结清",
        8: "作废",
      }),
      form: {},
    };
  },

  mounted() {
    this.init();
  },
  methods: {
    async init() {
      const { rows } = await getCaseInformation({
        outsourcedProjectId: this.$route.query.outsourcedProjectId,
        caseStatus:2
      });
      this.form = rows;
    },
  },
};
</script>
