<template>
  <div style="padding: 20px 0" id="tallyVoucherAdd">
    <div v-if="!nextType">
      <div class="header">
        <div class="item">
          <span> <i>*</i>公司 </span>
          <el-select
            v-model="params.companyNo"
            size="mini"
            filterable
            placeholder="请选择公司"
            @change="companyChenge($event)"
          >
            <el-option
              v-for="item in projects"
              :key="item.companyId"
              :label="item.name"
              :value="item.companyId"
            ></el-option>
          </el-select>
        </div>
        <div v-show="modelShow" class="item">
          <span> <i>*</i>流程模板 </span>
          <el-select
            v-model="params.flowModelId"
            size="mini"
            placeholder="请选择流程模板"
            filterable
            @change="updatemodel($event)"
          >
            <el-option
              v-for="item in roleTemplList"
              :key="item.id"
              :label="item.templateName"
              :value="item.parentId"
            ></el-option>
          </el-select>
        </div>
        <div v-show="modelShow" class="item">
          <span> <i>*</i>账套 </span>
          <el-select
            v-model="params.accountSetsId"
            size="mini"
            placeholder="请选择账套"
            @change="updateAccountSets()"
          >
            <el-option
              v-for="item in accountSetsList"
              :key="item.id"
              :label="item.companyName"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
        <div v-show="modelShow" class="item" style="display: flex">
          <span>备注说明</span>
          <el-input
            type="textarea"
            style="width: 500px"
            :rows="3"
            placeholder="备注说明，限500字"
            v-model="params.remark"
          ></el-input>
        </div>
        <div v-if="changeEditType == 'true'">
          <el-divider></el-divider>
          <p style="padding-left: 214px">管理员已开启编辑需审核功能</p>
          <p style="padding-left: 214px">
            如果记账凭证规则发生修改，必须由财务责任人、业务责任人审核确认后才能生效
            <span style="color: #409eff">规则说明</span>
            <el-tooltip placement="top">
              <div slot="content">
                管理员已开启编辑需审核功能 <br />
                所有编辑操作必须设置了财务、业务责任人后才能提交 <br />
                财务责任人提交编辑修改后，需要业务责任人审核后，本次编辑才能生效
                <br />
                业务责任人提交编辑修改后，需要财务责任人审核后，本次编辑才能生效
                <br />
                审核可以被驳回，驳回后本次编辑无效 <br />
                审核中的规则，将锁定无法被编辑 <br />
                审核通过后新规则即时生效 <br />
                可指定任任何属于财务角色的人员，作为财务责任人。如果您是财务角色，但还不是财务责任人，您提交修改后将自动成为本规则的财务责任人
                <br />
                可指定任任何属于财务角色的人员，作为业务责任人。如果您是业务角色，但还不是业务责任人，您提交修改后将自动成为本规则的业务责任人
                <br />
              </div>
              <i class="el-icon-warning-outline"></i>
            </el-tooltip>
          </p>
          <div class="item">
            <span> <i>*</i>财务责任人 </span>
            <el-select
              v-model="params.salesmanList"
              size="mini"
              placeholder="请选择"
              filterable
              multiple=""
            >
              <el-option
                v-for="item in userList.caiwu"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <p
              v-if="
                caiwuType &&
                !params.salesmanList.includes(userId) &&
                !roleList.includes('caiwuAdmin')
              "
              style="margin-left: 210px; margin-bottom: 0"
            >
              当前您不是本记账凭证规则的财务责任人
            </p>
            <p
              v-if="
                caiwuType &&
                !params.salesmanList.includes(userId) &&
                !roleList.includes('caiwuAdmin')
              "
              style="margin-left: 210px; margin-bottom: 0"
            >
              提交修改后，您将自动成为该记账凭证规则的财务责任人
            </p>
          </div>
          <div class="item">
            <span> <i>*</i>业务责任人 </span>
            <el-select
              v-model="params.financialStaffList"
              size="mini"
              placeholder="请选择"
              filterable
              multiple=""
            >
              <el-option
                v-for="item in userList.yewu"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
            <p
              v-if="
                yewuType &&
                !params.financialStaffList.includes(userId) &&
                !roleList.includes('yewuAdmin')
              "
              style="margin-left: 210px; margin-bottom: 0"
            >
              当前您不是本记账凭证规则的业务责任人
            </p>
            <p
              v-if="
                yewuType &&
                !params.financialStaffList.includes(userId) &&
                !roleList.includes('yewuAdmin')
              "
              style="margin-left: 210px; margin-bottom: 0"
            >
              提交修改后，您将自动成为该记账凭证规则的业务责任人
            </p>
          </div>
        </div>
      </div>
      <div
        v-show="modelShow && params.accountSetsId && params.flowModelId"
        class="solid2"
      ></div>
      <div
        class="btns"
        v-if="modelShow && params.accountSetsId && params.flowModelId"
      >
        <el-button @click="addTabs(1)" size="mini" type="primary"
          >+添加凭证规则</el-button
        >
        <el-button @click="addTabs(2)" size="mini" type="primary"
          >+ 添加凭证规则（在收款方账套下 ）</el-button
        >
      </div>
      <div
        style="padding: 20px"
        v-if="modelShow && params.accountSetsId && params.flowModelId"
      >
        <el-tabs
          v-model="tabsValue"
          type="card"
          editable
          @edit="handleTabsEdit"
        >
          <el-tab-pane
            :key="item.name"
            v-for="(item, index) in params.oaVoucherRulesViceVos"
            :label="item.name"
            :name="item.name"
          >
            <div class="content">
              <div style="margin-bottom: 12px; color: #333">
                <p v-if="item.rulerType == 1" style="color: #999">
                  在OA流程的收款方账套下生成凭证的规则
                </p>
                收/付 款方字段
                <el-popover placement="top-start" width="300" trigger="hover">
                  <div>
                    <p>
                      选择模板中的一个收款方元素，其输入内容将用于判断所属账套
                    </p>
                    <p>
                      例如将此流程模板中一个收款人单选控件设为[收款方字段]，当用户发起此流程，并在该单选控件选择了收款人A。收款人A对应的账套，即会成为本条记账凭证所属的账套
                    </p>
                    <p>注意！必须先在[收款人配置]中将收款人关联到账套</p>
                  </div>
                  <i
                    slot="reference"
                    class="el-icon-warning-outline"
                    style="cursor: pointer"
                  ></i>
                </el-popover>
                <el-select
                  style="margin-left: 9px"
                  placeholder="请选择"
                  clearable=""
                  @change="changeCollAccount($event, index)"
                  v-model="item.collAccountingField"
                >
                  <el-option
                    v-for="item in flowList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>
              <div style="margin: 16px 0">
                字：
                <el-select
                  v-model="item.finanicalWord"
                  size="mini"
                  style="width: 300px; border: none"
                >
                  <el-option
                    v-for="item in finicalWordList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </div>
              <div style="margin: 16px 0">
                是否根据动态表单生成科目：
                <el-select
                  v-model="item.isEnableDynamicForm"
                  size="mini"
                  style="width: 300px; border: none"
                >
                  <el-option label="否" value="0"></el-option>
                  <el-option label="是" value="1"></el-option>
                </el-select>
              </div>
              <span>
                摘要：
                <el-popover placement="top-start" width="300" trigger="hover">
                  <div>
                    <p>摘要由[固定名称]与[动态名称]自定义组合生成</p>
                    <p>[固定名称]是固定的文字</p>
                    <p>
                      [动态名称]是此流程模板中的一个元素，将其输入内容作为摘要内容。例如将此流程模板中一个文本输入框元素设为[动态名称模块]，当用户发起此流程，并在文本输入框输入A，A即成为摘要内容
                    </p>
                  </div>
                  <i
                    slot="reference"
                    class="el-icon-warning-outline"
                    style="cursor: pointer"
                  ></i>
                </el-popover>
              </span>
              <div class="word_box">
                <div
                  style="display: flex; align-items: center"
                  v-for="(v, i) in item.summaryList"
                  :key="i"
                >
                  <div class="word_box_item">
                    <el-input
                      style="width: 220px"
                      v-if="v.type === 'input'"
                      v-model="v.value"
                      size="mini"
                      clearable
                      placeholder="请输入"
                    ></el-input>
                    <el-select
                      style="width: 220px"
                      v-if="v.type === 'select'"
                      v-model="v.value"
                      @change="changeSum($event, index, i)"
                      size="mini"
                      clearable
                      placeholder="请选择"
                    >
                      <el-option
                        :label="option.label"
                        :value="option.value"
                        v-for="option in v.options"
                        :key="option.value"
                      ></el-option>
                    </el-select>
                    <i class="el-icon-delete" @click="delItem(index, i)"></i>
                  </div>
                  <i
                    v-if="
                      item.summaryList.length > 1 &&
                      i != item.summaryList.length - 1
                    "
                    class="el-icon-plus"
                  ></i>
                </div>
              </div>
              <div style="margin-top: 16px">
                <el-button
                  @click="handleSummaryClick(index, 1)"
                  size="mini"
                  type="primary"
                  >+添加固定名称模块</el-button
                >
                <el-button
                  @click="handleSummaryClick(index, 2)"
                  size="mini"
                  type="primary"
                  >+添加动态名称模块</el-button
                >
              </div>
              <p style="margin-top: 16px">科目规则：</p>
              <div style="margin-bottom: 16px">
                <el-button
                  type="primary"
                  size="mini"
                  @click="addSubject(item, index, 0)"
                  >+增加借方科目</el-button
                >
                <el-button
                  type="primary"
                  size="mini"
                  @click="addSubject(item, index, 1)"
                  >+增加贷方科目</el-button
                >
              </div>
              <div style="width: 100%; overflow: auto">
                <div
                  class="table_data"
                  :style="{ width: item.tableWidth + 'px' }"
                >
                  <div class="head">
                    <div class="title2" style="width: 120px"></div>
                    <div
                      class="title2"
                      style="width: 500px"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      {{ v.subjectType == 0 ? "借方" : "贷方" }}
                      <i
                        class="el-icon-delete"
                        v-if="v.subDelete == 'true'"
                        @click="delSub(index, i)"
                      ></i>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">关联动态表单</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <el-radio-group v-model="v.isAssociationBatch">
                        <el-radio label="0">是</el-radio>
                        <el-radio label="1">否</el-radio>
                      </el-radio-group>
                      <div
                        style="margin-top: 9px"
                        v-if="v.isAssociationBatch == 0"
                      >
                        动态表单字段
                        <el-select
                          v-model="v.associationBatchField"
                          size="mini"
                          @change="changeAss($event, index, i)"
                          style="width: 300px; border: none"
                          placeholder="请选择动态表单字段"
                        >
                          <el-option
                            v-for="item in flowList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                      </div>
                      <div
                        style="margin-top: 9px"
                        v-if="
                          v.associationBatchField && v.isAssociationBatch == 0
                        "
                      >
                        动态表单金额
                        <el-select
                          v-model="v.batchAmountField"
                          size="mini"
                          style="width: 300px; border: none"
                          placeholder="请选择动态表单金额"
                        >
                          <el-option
                            v-for="item in v.newList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                      </div>
                      <div
                        style="margin-top: 9px"
                        v-if="
                          v.associationBatchField && v.isAssociationBatch == 0
                        "
                      >
                        动态表单科目
                        <el-select
                          v-model="v.batchSubjectField"
                          size="mini"
                          style="width: 300px; border: none"
                          placeholder="请选择动态表单科目"
                        >
                          <el-option
                            v-for="item in v.newList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                      </div>
                      <div
                        style="margin-top: 9px"
                        v-if="
                          v.associationBatchField && v.isAssociationBatch == 0
                        "
                      >
                        动态表单摘要
                        <el-select
                          v-model="v.batchAbstractField"
                          size="mini"
                          style="width: 300px; border: none"
                          placeholder="请选择动态表单摘要"
                        >
                          <el-option
                            v-for="item in v.newList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                          ></el-option>
                        </el-select>
                      </div>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">记账金额字段</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <el-select
                        v-model="v.accountingField"
                        size="mini"
                        style="width: 300px; border: none"
                        @change="changeAccounting($event, index, i)"
                        placeholder="请选择记账金额字段"
                      >
                        <el-option
                          v-for="item in flowList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div class="head" v-if="item.rulerType == 1">
                    <div class="title2" style="width: 120px">科目类型</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <el-select
                        v-if="v.subjectType == 0"
                        v-model="v.accountSetsSubjectType"
                        size="mini"
                        style="width: 300px; border: none"
                        placeholder="请选择科目类型"
                      >
                        <el-option label="资产" value="资产"></el-option>
                        <el-option label="负债" value="负债"></el-option>
                      </el-select>
                      <el-select
                        v-else
                        v-model="v.accountSetsSubjectType"
                        size="mini"
                        style="width: 300px; border: none"
                        placeholder="请选择科目类型"
                      >
                        <el-option label="资产" value="资产"></el-option>
                        <el-option label="负债" value="负债"></el-option>
                        <el-option label="损益" value="损益"></el-option>
                      </el-select>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">一级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <el-select
                        v-if="item.rulerType == 0"
                        v-model="v.firstSubjectId"
                        size="mini"
                        filterable
                        style="width: 300px; border: none"
                        placeholder="请选择科目名称"
                        @change="updatesubject1($event, index, i)"
                      >
                        <el-option
                          v-for="item in firstBorrowSubjectList"
                          :key="item.id"
                          :label="item.name"
                          :value="item.id"
                        ></el-option>
                      </el-select>
                      <el-input
                        v-else
                        v-model="v.firstSubjectName"
                        style="width: 300px; border: none"
                        placeholder="请输入科目名称"
                        size="mini"
                      ></el-input>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">二级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <div style="text-align: left">
                        <el-radio-group v-model="v.isSubjectSecond">
                          <el-radio label="1">无</el-radio>
                          <el-radio label="0">有</el-radio>
                        </el-radio-group>
                      </div>

                      <div v-if="v.isSubjectSecond == 0">
                        <div style="text-align: left; margin-top: 12px">
                          取值方式：
                          <el-select
                            v-model="v.secondValueMode"
                            style="width: 120px"
                          >
                            <el-option value="0" label="固定名称"></el-option>
                            <el-option value="1" label="动态名称"></el-option>
                          </el-select>
                        </div>
                        <div
                          style="margin-top: 12px; display: flex"
                          v-if="v.secondValueMode == 1"
                        >
                          <div
                            style="display: flex; align-items: center"
                            v-for="(x, c) in v.twoSummaryList"
                            :key="c"
                          >
                            <div class="word_box_item" style="width: 170px">
                              <el-select
                                v-if="
                                  x.type == 'select' &&
                                  (item.isEnableDynamicForm == 0 ||
                                    v.isAssociationBatch == 1)
                                "
                                v-model="x.value"
                                size="mini"
                                filterable
                                @change="changeSel($event, index, i, c, 'two')"
                                style="width: 120px; border: none"
                                placeholder="请选择科目名称"
                              >
                                <el-option
                                  v-for="item in flowList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                />
                              </el-select>
                              <el-select
                                v-if="
                                  x.type == 'select' &&
                                  item.isEnableDynamicForm == 1 &&
                                  v.isAssociationBatch == 0
                                "
                                v-model="x.value"
                                size="mini"
                                filterable
                                @change="changeSel($event, index, i, c, 'two')"
                                style="width: 120px; border: none"
                                placeholder="请选择科目名称"
                              >
                                <el-option
                                  v-for="item in v.newList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                />
                              </el-select>
                              <el-input
                                v-if="x.type == 'input'"
                                v-model="x.value"
                                size="mini"
                                filterable
                                style="width: 120px; border: none"
                                placeholder="请输入"
                              ></el-input>
                              <i
                                class="el-icon-delete"
                                @click="deltwoItem(index, i, c, 'two')"
                              ></i>
                            </div>
                            <i
                              v-if="
                                v.twoSummaryList.length > 1 &&
                                c != v.twoSummaryList.length - 1
                              "
                              class="el-icon-plus"
                            ></i>
                          </div>
                        </div>
                        <div
                          v-if="v.secondValueMode == 1"
                          style="text-align: left; margin-top: 12px"
                        >
                          <el-button
                            @click="addTwoSum(index, i, 1, 'two')"
                            size="mini"
                            type="primary"
                            >+添加固定名称模块</el-button
                          >
                          <el-button
                            @click="addTwoSum(index, i, 2, 'two')"
                            size="mini"
                            type="primary"
                            >+添加动态名称模块</el-button
                          >
                        </div>
                        <div v-else style="text-align: left; margin-top: 12px">
                          <el-input
                            v-model="v.secondSubjectName"
                            size="mini"
                            filterable
                            style="width: 250px; border: none"
                            placeholder="请输入"
                          ></el-input>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">三级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <div
                        style="text-align: left"
                        v-if="v.isSubjectSecond == 0"
                      >
                        <el-radio-group v-model="v.isSubjectThird">
                          <el-radio label="1">无</el-radio>
                          <el-radio label="0">有</el-radio>
                        </el-radio-group>
                      </div>

                      <div
                        v-if="v.isSubjectThird == 0 && v.isSubjectSecond == 0"
                      >
                        <div style="text-align: left; margin-top: 12px">
                          取值方式：
                          <el-select
                            v-model="v.thirdValueMode"
                            style="width: 120px"
                          >
                            <el-option value="0" label="固定名称"></el-option>
                            <el-option value="1" label="动态名称"></el-option>
                          </el-select>
                        </div>
                        <div
                          style="margin-top: 12px; display: flex"
                          v-if="v.thirdValueMode == 1"
                        >
                          <div
                            style="display: flex; align-items: center"
                            v-for="(x, c) in v.threeSummaryList"
                            :key="c"
                          >
                            <div class="word_box_item" style="width: 170px">
                              <el-select
                                v-if="x.type == 'select'"
                                v-model="x.value"
                                size="mini"
                                filterable
                                @change="
                                  changeSel($event, index, i, c, 'three')
                                "
                                style="width: 120px; border: none"
                                placeholder="请选择科目名称"
                              >
                                <el-option
                                  v-for="item in flowList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                />
                              </el-select>
                              <el-input
                                v-if="x.type == 'input'"
                                v-model="x.value"
                                size="mini"
                                filterable
                                style="width: 120px; border: none"
                                placeholder="请输入"
                              ></el-input>
                              <i
                                class="el-icon-delete"
                                @click="deltwoItem(index, i, c, 'three')"
                              ></i>
                            </div>
                            <i
                              v-if="
                                v.threeSummaryList.length > 1 &&
                                c != v.threeSummaryList.length - 1
                              "
                              class="el-icon-plus"
                            ></i>
                          </div>
                        </div>
                        <div
                          v-if="v.thirdValueMode == 1"
                          style="text-align: left; margin-top: 12px"
                        >
                          <el-button
                            @click="addTwoSum(index, i, 1, 'three')"
                            size="mini"
                            type="primary"
                            >+添加固定名称模块</el-button
                          >
                          <el-button
                            @click="addTwoSum(index, i, 2, 'three')"
                            size="mini"
                            type="primary"
                            >+添加动态名称模块</el-button
                          >
                        </div>
                        <div v-else style="text-align: left; margin-top: 12px">
                          <el-input
                            v-model="v.thirdSubjectName"
                            size="mini"
                            filterable
                            style="width: 250px; border: none"
                            placeholder="请输入"
                          ></el-input>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">四级科目</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      <div
                        style="text-align: left"
                        v-if="v.isSubjectThird == 0"
                      >
                        <el-radio-group v-model="v.isSubjectFourth">
                          <el-radio label="1">无</el-radio>
                          <el-radio label="0">有</el-radio>
                        </el-radio-group>
                      </div>

                      <div
                        v-if="v.isSubjectFourth == 0 && v.isSubjectThird == 0"
                      >
                        <div style="text-align: left; margin-top: 12px">
                          取值方式：
                          <el-select
                            v-model="v.fourthValueMode"
                            style="width: 120px"
                          >
                            <el-option value="0" label="固定名称"></el-option>
                            <el-option value="1" label="动态名称"></el-option>
                          </el-select>
                        </div>
                        <div
                          style="margin-top: 12px; display: flex"
                          v-if="v.fourthValueMode == 1"
                        >
                          <div
                            style="display: flex; align-items: center"
                            v-for="(x, c) in v.fourSummaryList"
                            :key="c"
                          >
                            <div class="word_box_item" style="width: 170px">
                              <el-select
                                v-if="x.type == 'select'"
                                v-model="x.value"
                                size="mini"
                                filterable
                                @change="changeSel($event, index, i, c, 'four')"
                                style="width: 120px; border: none"
                                placeholder="请选择科目名称"
                              >
                                <el-option
                                  v-for="item in flowList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                />
                              </el-select>
                              <el-input
                                v-if="x.type == 'input'"
                                v-model="x.value"
                                size="mini"
                                filterable
                                style="width: 120px; border: none"
                                placeholder="请输入"
                              ></el-input>
                              <i
                                class="el-icon-delete"
                                @click="deltwoItem(index, i, c, 'four')"
                              ></i>
                            </div>
                            <i
                              v-if="
                                v.fourSummaryList.length > 1 &&
                                c != v.fourSummaryList.length - 1
                              "
                              class="el-icon-plus"
                            ></i>
                          </div>
                        </div>
                        <div
                          v-if="v.fourthValueMode == 1"
                          style="text-align: left; margin-top: 12px"
                        >
                          <el-button
                            @click="addTwoSum(index, i, 1, 'four')"
                            size="mini"
                            type="primary"
                            >+添加固定名称模块</el-button
                          >
                          <el-button
                            @click="addTwoSum(index, i, 2, 'four')"
                            size="mini"
                            type="primary"
                            >+添加动态名称模块</el-button
                          >
                        </div>
                        <div v-else style="text-align: left; margin-top: 12px">
                          <el-input
                            v-model="v.fourthSubjectName"
                            size="mini"
                            filterable
                            style="width: 250px; border: none"
                            placeholder="请输入"
                          ></el-input>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div class="head">
                    <div class="title2" style="width: 120px">科目汇总</div>
                    <div
                      class="title2"
                      style="width: 500px; background: none; text-align: left"
                      v-for="(v, i) in item.oaVoucherRulesSubjects"
                      :key="i"
                    >
                      {{ v.subjectCollect }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <div v-else>
      <editData
        :personData="personData"
        @shangyibu="handleCancel"
        @nextSubmit="nextSubmit"
        :oldData="oldData"
        :newData="params"
        :projects="projects"
        :accountSetsList="accountSetsList"
      />
    </div>

    <div class="btn-container">
      <el-button
        @click="del"
        type="warning"
        v-if="$route.query.pid && !nextType"
        >删除记账凭证</el-button
      >
      <el-button @click="handleCancel" v-if="!nextType">取消</el-button>
      <el-button
        type="primary"
        @click="handleSave"
        v-if="
          changeEditType == 'false' || (changeEditType == 'true' && !projectId)
        "
        >保存</el-button
      >
      <el-button
        type="primary"
        @click="next"
        v-if="changeEditType == 'true' && !nextType && projectId"
        >下一步</el-button
      >
    </div>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="editSubmitType"
      width="500px"
      :show-close="false"
    >
      <p style="font-weight: bold; text-align: center">编辑申请已提交!</p>
      <p style="text-align: center">
        以下人员将在OA系统待办中收到待审核通知，审核通过后编辑内容立即生效。请及时沟通以尽快完成审核
      </p>
      <p style="text-align: center">
        <span style="font-weight: bold">{{ personData.zrr }}</span
        >：{{ personData.zrrList }}
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :close-on-click-modal="false"
      :visible.sync="delSubmitType"
      width="500px"
      :show-close="false"
    >
      <p style="font-weight: bold; text-align: center">删除申请已提交!</p>
      <p style="text-align: center">
        以下人员将在OA系统待办中收到待审核通知，审核通过后删除立即生效。请及时沟通以尽快完成审核
      </p>
      <p style="text-align: center">
        <span style="font-weight: bold">{{ personData.zrr }}</span
        >：{{ personData.zrrList }}
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close">关 闭</el-button>
      </span>
    </el-dialog>
    <delItem
      v-if="delItemType"
      :personData="personData"
      @close="delItemType = false"
      @submit="delSubmit"
    />
  </div>
</template>
    <script>
import editData from "./editData.vue";
import delItem from "./delItem.vue";
import {
  addData,
  getRulesById,
  listSubjectSelect,
  getAccountSetsList,
  addRulesMain,
  deleteByMainId,
  getuser,
  getAllFirstSubject,
  addNewEditInfo,
  getCheckTableField,
} from "@/api/oa/voucharRules";
import { getColectionList } from "@/api/oa/trader";
import {
  allCompanyList,
  getDataByRoleAndClassIdFinance,
  getTemplate,
  getTemplateId,
} from "@/api/oa/processTemplate";
import { queryFormFeild } from "@/api/form/form";
export default {
  name: "TallyVoucherAdd",
  components: {
    editData,
    delItem,
  },
  data() {
    return {
      delSubmitType: false,
      delItemType: false,
      editSubmitType: false,
      nextType: false,
      oldData: null,
      params: {
        id: "",
        companyNo: "",
        flowModelId: "",
        flowModelName: "",
        salesmanList: [],
        financialStaffList: [],
        accountSetsId: "",
        remark: "",
        isVoucher: "",
        oaVoucherRulesViceVos: [
          {
            tableWidth: 1120,
            id: "",
            rulesMainId: "",
            abstractJson: "",
            finanicalWord: "记",
            isEnableDynamicForm: "0",
            name: "记账凭证规则1",
            rulerType: 0,
            summaryList: [
              {
                type: "input",
                value: "",
              },
            ],
            collAccountingField: "",
            collAccountingFieldName: "",
            oaVoucherRulesSubjects: [
              {
                isAssociationBatch: "1",
                associationBatchField: "",
                batchAbstractField: "",
                batchSubjectField: "",
                batchAmountField: "",
                subDelete: "false",
                twoSummaryList: [],
                threeSummaryList: [],
                fourSummaryList: [],
                accountSetsSubjectType: "",
                id: "",
                rulesViceId: "",
                accountingField: "",
                accountingFieldName: "",
                subjectType: 0,
                firstSubjectId: "",
                firstSubjectName: "",
                isSubjectSecond: "1",
                secondValueMode: "0",
                secondSubjectId: "",
                secondSubjectName: "",
                secondSubjectJson: "",
                isSubjectThird: "1",
                thirdValueMode: "0",
                thirdSubjectId: "",
                thirdSubjectName: "",
                thirdSubjectJson: "",
                isSubjectFourth: "1",
                fourthValueMode: "0",
                fourthSubjectId: "",
                fourthSubjectName: "",
                fourthSubjectJson: "",
                subjectCollect: "",
                newList: [],
              },
              {
                isAssociationBatch: "1",
                associationBatchField: "",
                batchAbstractField: "",
                batchSubjectField: "",
                batchAmountField: "",
                subDelete: "false",
                accountSetsSubjectType: "",
                twoSummaryList: [],
                threeSummaryList: [],
                fourSummaryList: [],
                id: "",
                rulesViceId: "",
                accountingField: "",
                subjectType: 1,
                firstSubjectId: "",
                firstSubjectName: "",
                isSubjectSecond: "1",
                secondValueMode: "0",
                secondSubjectId: "",
                secondSubjectName: "",
                secondSubjectJson: "",
                isSubjectThird: "1",
                thirdValueMode: "0",
                thirdSubjectId: "",
                thirdSubjectName: "",
                thirdSubjectJson: "",
                isSubjectFourth: "1",
                fourthValueMode: "0",
                fourthSubjectId: "",
                fourthSubjectName: "",
                fourthSubjectJson: "",
                subjectCollect: "",
                newList: [],
              },
            ],
          },
        ],
      },
      debitRadio: 1,
      a: "",
      jfList: [{}],

      tabsValue: "记账凭证规则1",
      //收款人集合
      collectionList: [],
      //摘要

      //收款方摘要
      collSummaryList: [
        {
          type: "input",
          value: "",
        },
      ],
      //字List
      finicalWordList: [
        {
          label: "记",
          value: "记",
        },
        {
          label: "收",
          value: "收",
        },
        {
          label: "付",
          value: "付",
        },
        {
          label: "转",
          value: "转",
        },
      ],
      //账套
      accountSetsList: [],
      //科目类型
      subjectTypeList: [],
      //一级科目
      firstBorrowSubjectList: [],
      firstLoanSubjectList: [],
      //二级科目
      secondBorrowSubjectList: [],
      secondLoanSubjectList: [],
      //三级科目
      thirdBorrowSubjectList: [],
      thirdLoanSubjectList: [],
      //四级科目
      fourthBorrowSubjectList: [],
      fourthLoanSubjectList: [],

      modelShow: false,
      accFieldShow: false,
      subjectTypeShow: false,

      projectId: "",
      //公司下拉框数据
      projects: [],
      //流程模板数据
      roleTemplList: [],
      queryParams: {
        id: "",
        associationId: "",
        remark: "",
        companyNo: "",
        flowModelId: "",
        accountSetsId: "",
        flowModelName: "",
        collAccountingField: "",
        accountingField: "",
        finanicalWord: "记",
        isEnableDynamicForm: "0",
        abstractJson: "",
        isVoucher: "N",
        collectionField: null,
      },
      lastformId: "",
      subjectNameList: [
        {
          value: 1,
          label: "第一个科目名称",
        },
        {
          value: 2,
          label: "第二个科目名称",
        },
      ],
      takeList: [
        {
          value: 1,
          name: "固定名称",
        },
        {
          value: 2,
          name: "动态名称",
        },
      ],
      flowList: [],
      tableIsShow2: {
        tableList7: false,
        tableList8: false,
      },

      editType: false,
      changeEditType: "",
      userList: {},
      yewuType: false,
      caiwuType: false,
      roleList: [],
      userId: "",
      personData: {},
    };
  },
  created() {
    this.userId = Number(sessionStorage.getItem("userId"));
    if (sessionStorage.getItem("roleList")) {
      this.roleList = JSON.parse(sessionStorage.getItem("roleList"));
    }

    if (this.$route.query.edit) {
      this.editType = true;
    }
    this.changeEditType = this.$route.query.changeEditType;
    this.getDatasById();
  },
  watch: {
    params: {
      handler(newval, oldval) {
        console.log(newval);

        this.params.oaVoucherRulesViceVos.forEach((item) => {
          item.oaVoucherRulesSubjects.forEach((i) => {
            i.twoWord = "";
            i.threeWord = "";
            i.fourWord = "";
            if (i.twoSummaryList && i.twoSummaryList.length > 0) {
              i.twoSummaryList.forEach((v) => {
                if (v.type == "input") {
                  i.twoWord += v.value;
                } else {
                  i.twoWord += "[" + v.label + "]";
                }
              });
            } else {
              i.twoWord = i.secondSubjectName || "";
            }
            if (i.threeSummaryList && i.threeSummaryList.length > 0) {
              i.threeSummaryList.forEach((v) => {
                if (v.type == "input") {
                  i.threeWord += v.value;
                } else {
                  i.threeWord += "[" + v.label + "]";
                }
              });
            } else {
              i.threeWord = i.thirdSubjectName || "";
            }
            if (i.fourSummaryList && i.fourSummaryList.length > 0) {
              i.fourSummaryList.forEach((v) => {
                if (v.type == "input") {
                  i.fourWord += v.value;
                } else {
                  i.fourWord += "[" + v.label + "]";
                }
              });
            } else {
              i.fourWord = i.fourthSubjectName || "";
            }
            setTimeout(() => {
              i.subjectCollect =
                i.firstSubjectName +
                "-" +
                i.twoWord +
                "-" +
                i.threeWord +
                "-" +
                i.fourWord;
            }, 500);
          });
        });
      },
      deep: true,
    },
  },
  methods: {
    changeAss(e, index, i) {
      console.log(e, index, i);
      let data = this.roleTemplList.find(item=>{
        return item.parentId==this.params.flowModelId
      })
      getCheckTableField({ formId: data.formId, checkTableCode: e }).then(
        (res) => {
          this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
            i
          ].newList = res;
        }
      );
    },
    delSubmit(e) {
      let data = {
        ...this.params,
        editInfo: e,
        editType: 2,
        oaVoucherRulesMainId: this.params.id,
      };
      addNewEditInfo(data).then((res) => {
        if (res.code == 200) {
          this.delSubmitType = true;
        }
      });
    },
    nextSubmit(e) {
      e.oaVoucherRulesViceVos.forEach((item) => {
        this.$set(item, "abstractJson", JSON.stringify(item.summaryList));
        // item.abstractJson = JSON.stringify(item.summaryList);
        item.oaVoucherRulesSubjects.forEach((i) => {
          i.secondSubjectJson = JSON.stringify(i.twoSummaryList);
          if (!i.secondSubjectName || i.secondValueMode == 1) {
            i.secondSubjectName = i.twoWord;
          }
          i.thirdSubjectJson = JSON.stringify(i.threeSummaryList);
          if (!i.thirdSubjectName || i.thirdValueMode == 1) {
            i.thirdSubjectName = i.threeWord;
          }
          i.fourthSubjectJson = JSON.stringify(i.fourSummaryList);
          if (!i.fourthSubjectName || i.fourthValueMode == 1) {
            i.fourthSubjectName = i.fourWord;
          }
        });
      });
      addNewEditInfo(e).then((res) => {
        if (res.code == 200) {
          this.editSubmitType = true;
        }
      });
    },
    next() {
      if (!this.params.companyNo) {
        this.$message.warning("请选择公司");
        return;
      }
      if (!this.params.flowModelId) {
        this.$message.warning("请选择流程模板");
        return;
      }
      if (!this.params.accountSetsId) {
        this.$message.warning("请选择账套");
        return;
      }
      if (
        this.$route.query.changeEditType == "true" &&
        this.params.salesmanList.length == 0
      ) {
        this.$message.warning("请选择财务负责人");
        return;
      }
      if (
        this.$route.query.changeEditType == "true" &&
        this.params.financialStaffList.length == 0
      ) {
        this.$message.warning("请选择业务负责人");
        return;
      }
      if (
        this.params.oaVoucherRulesViceVos.length == 0 ||
        !this.params.oaVoucherRulesViceVos
      ) {
        this.$message.warning("请填写完整表单");

        return;
      }
      this.personData = {
        tjsfr: this.yewuType
          ? "业务责任人"
          : this.caiwuType
          ? "财务责任人"
          : this.roleList.includes("caiwuAdmin")
          ? "财务管理员"
          : this.roleList.includes("yewuAdmin")
          ? "业务管理员"
          : "",
      };
      if (this.yewuType) {
        let list = [];
        this.userList.caiwu.forEach((item) => {
          this.params.salesmanList.forEach((i) => {
            if (item.value == i) {
              list.push(item);
            }
          });
        });
        this.personData.zrr = "财务责任人";
        this.personData.zrrList = list.map((item) => item.label);
      }
      if (this.caiwuType) {
        let list = [];
        this.userList.yewu.forEach((item) => {
          this.params.financialStaffList.forEach((i) => {
            if (item.value == i) {
              list.push(item);
            }
          });
        });
        this.personData.zrr = "业务责任人";
        this.personData.zrrList = list.map((item) => item.label);
      }
      this.nextType = true;
    },
    changeCollAccount(e, index) {
      let data = this.flowList.find((item) => {
        return item.value == e;
      });
      this.params.oaVoucherRulesViceVos[index].collAccountingFieldName =
        data.label;
    },
    changeAccounting(e, index, i) {
      let data = this.flowList.find((item) => {
        return item.value == e;
      });
      this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
        i
      ].accountingFieldName = data.label;
    },
    del() {
      if (this.$route.query.changeEditType == "true") {
        this.personData = {
          tjsfr: this.yewuType
            ? "业务责任人"
            : this.caiwuType
            ? "财务责任人"
            : this.roleList.includes("caiwuAdmin")
            ? "财务管理员"
            : this.roleList.includes("yewuAdmin")
            ? "业务管理员"
            : "",
        };
        if (this.yewuType) {
          let list = [];
          this.userList.caiwu.forEach((item) => {
            this.params.salesmanList.forEach((i) => {
              if (item.value == i) {
                list.push(item);
              }
            });
          });
          this.personData.zrr = "财务责任人";
          this.personData.zrrList = list.map((item) => item.label);
        }
        if (this.caiwuType) {
          let list = [];
          this.userList.yewu.forEach((item) => {
            this.params.financialStaffList.forEach((i) => {
              if (item.value == i) {
                list.push(item);
              }
            });
          });
          this.personData.zrr = "业务责任人";
          this.personData.zrrList = list.map((item) => item.label);
        }
        this.delItemType = true;
      } else {
        this.$confirm(
          "是否确认删除此记账凭证, 删除后将不再按照此规则生成记账凭证，请谨慎删除！",
          "提示",
          {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          }
        )
          .then(() => {
            deleteByMainId(this.$route.query.pid).then((res) => {
              if (res.code == 200) {
                this.$message({
                  type: "success",
                  message: "删除成功!",
                });
                let route = {
                  fullPath: "/oa/tallyVoucher",
                  name: "TallyVoucher",
                  path: "/oa/tallyVoucher",
                };
                this.closeSelectedTag(route);
                setTimeout(() => {
                  this.$router.push({ path: "/oa/tallyVoucher" });
                }, 500);
              }
            });
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "已取消删除",
            });
          });
      }
    },
    close() {
      this.editSubmitType = false;
      this.delSubmitType = false;
      let route = {
        fullPath: "/oa/tallyVoucher",
        name: "TallyVoucher",
        path: "/oa/tallyVoucher",
      };
      this.closeSelectedTag(route);
      setTimeout(() => {
        this.$router.push({ path: "/oa/tallyVoucher" });
      }, 500);
    },
    //点击跳转编辑表单
    closeSelectedTag(view) {
      console.log(view);
      this.$tab.closePage(view).then(({ visitedViews }) => {});
    },
    changeSum(e, index, i) {
      let data = this.flowList.find((item) => {
        return item.value == e;
      });
      this.params.oaVoucherRulesViceVos[index].summaryList[i].label =
        data.label;
    },
    changeSel(e, index, i, c, type) {
      console.log(e, index, i, c, type);
      let data = this.flowList.find((item) => {
        return item.value == e;
      });
      if (type == "two") {
        this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
          i
        ].twoSummaryList[c].label = data.label;
      } else if (type == "three") {
        this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
          i
        ].threeSummaryList[c].label = data.label;
      } else {
        this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
          i
        ].fourSummaryList[c].label = data.label;
      }
    },
    delSub(index, i) {
      this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects.splice(
        i,
        1
      );
      this.params.oaVoucherRulesViceVos[index].tableWidth -= 480;
    },
    addSubject(v, i, c) {
      console.log(v, i, c);
      let obj = {
        isAssociationBatch: "1",
        associationBatchField: "",
        batchAbstractField: "",
        batchSubjectField: "",
        batchAmountField: "",
        subDelete: "true",
        twoSummaryList: [],
        threeSummaryList: [],
        fourSummaryList: [],
        id: "",
        rulesViceId: "",
        accountingField: "",
        subjectType: c,
        accountSetsSubjectType: "",
        firstSubjectId: "",
        firstSubjectName: "",
        isSubjectSecond: "1",
        secondValueMode: "0",
        secondSubjectId: "",
        secondSubjectName: "",
        secondSubjectJson: "",
        isSubjectThird: "1",
        thirdValueMode: "0",
        thirdSubjectId: "",
        thirdSubjectName: "",
        thirdSubjectJson: "",
        isSubjectFourth: "1",
        fourthValueMode: "0",
        fourthSubjectId: "",
        fourthSubjectName: "",
        fourthSubjectJson: "",
        subjectCollect: "",
        newList: [],
      };
      let d = "";
      this.params.oaVoucherRulesViceVos[i].oaVoucherRulesSubjects.forEach(
        (item, index) => {
          if (item.subjectType == c) {
            d = index;
          }
        }
      );
      console.log(d);
      setTimeout(() => {
        this.params.oaVoucherRulesViceVos[i].oaVoucherRulesSubjects.splice(
          d + 1,
          0,
          obj
        );
        this.params.oaVoucherRulesViceVos[i].tableWidth += 480;
      }, 100);
    },
    addTwoSum(index, i, type, c) {
      if (c == "two") {
        this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
          i
        ].twoSummaryList.push({
          type: type == 1 ? "input" : "select",
          value: "",
          label: "",
        });
      } else if (c == "three") {
        this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
          i
        ].threeSummaryList.push({
          type: type == 1 ? "input" : "select",
          value: "",
          label: "",
        });
      } else {
        this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
          i
        ].fourSummaryList.push({
          type: type == 1 ? "input" : "select",
          value: "",
          label: "",
        });
      }
    },
    deltwoItem(index, i, c, v) {
      if (v == "two") {
        this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
          i
        ].twoSummaryList.splice(c, 1);
      } else if (v == "three") {
        this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
          i
        ].threeSummaryList.splice(c, 1);
      } else {
        this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
          i
        ].fourSummaryList.splice(c, 1);
      }
    },
    addTabs(e) {
      let type1 = [];
      let type2 = [];
      this.params.oaVoucherRulesViceVos.map((item) => {
        if (item.rulerType == 0) {
          type1.push(item);
        } else {
          type2.push(item);
        }
      });
      console.log(type1, type2);

      if (e == 1) {
        this.params.oaVoucherRulesViceVos.push({
          name: "记账凭证规则" + (type1.length + 1),
          ...{
            tableWidth: 1120,
            id: "",
            rulesMainId: "",
            abstractJson: "",
            finanicalWord: "记",
            isEnableDynamicForm: "0",
            rulerType: 0,
            summaryList: [
              {
                type: "input",
                value: "",
              },
            ],
            collAccountingField: "",
            oaVoucherRulesSubjects: [
              {
                isAssociationBatch: "1",
                associationBatchField: "",
                batchAbstractField: "",
                batchSubjectField: "",
                batchAmountField: "",
                twoSummaryList: [],
                threeSummaryList: [],
                fourSummaryList: [],
                id: "",
                rulesViceId: "",
                accountingField: "",
                accountSetsSubjectType: "",

                subjectType: 0,
                firstSubjectId: "",
                firstSubjectName: "",
                isSubjectSecond: "1",
                secondValueMode: "0",
                secondSubjectId: "",
                secondSubjectName: "",
                secondSubjectJson: "",
                isSubjectThird: "1",
                thirdValueMode: "0",
                thirdSubjectId: "",
                thirdSubjectName: "",
                thirdSubjectJson: "",
                isSubjectFourth: "1",
                fourthValueMode: "0",
                fourthSubjectId: "",
                fourthSubjectName: "",
                fourthSubjectJson: "",
                subjectCollect: "",
                newList: [],
              },
              {
                isAssociationBatch: "1",
                associationBatchField: "",
                batchAbstractField: "",
                batchSubjectField: "",
                batchAmountField: "",
                twoSummaryList: [],
                threeSummaryList: [],
                fourSummaryList: [],
                id: "",
                rulesViceId: "",
                accountingField: "",
                accountSetsSubjectType: "",
                subjectType: 1,
                firstSubjectId: "",
                firstSubjectName: "",
                isSubjectSecond: "1",
                secondValueMode: "0",
                secondSubjectId: "",
                secondSubjectName: "",
                secondSubjectJson: "",
                isSubjectThird: "1",
                thirdValueMode: "0",
                thirdSubjectId: "",
                thirdSubjectName: "",
                thirdSubjectJson: "",
                isSubjectFourth: "1",
                fourthValueMode: "0",
                fourthSubjectId: "",
                fourthSubjectName: "",
                fourthSubjectJson: "",
                subjectCollect: "",
                newList: [],
              },
            ],
          },
        });
      } else {
        this.params.oaVoucherRulesViceVos.push({
          name: "记账凭证规则（在收款方账套下）" + (type2.length + 1),
          ...{
            tableWidth: 1120,
            id: "",
            rulesMainId: "",
            abstractJson: "",
            rulerType: 1,
            finanicalWord: "记",
            isEnableDynamicForm: "0",
            summaryList: [
              {
                type: "input",
                value: "",
              },
            ],
            collAccountingField: "",
            oaVoucherRulesSubjects: [
              {
                isAssociationBatch: "1",
                associationBatchField: "",
                batchAbstractField: "",
                batchSubjectField: "",
                batchAmountField: "",
                twoSummaryList: [],
                threeSummaryList: [],
                fourSummaryList: [],
                id: "",
                rulesViceId: "",
                accountSetsSubjectType: "",
                accountingField: "",

                subjectType: 0,
                firstSubjectId: "",
                firstSubjectName: "",
                isSubjectSecond: "1",
                secondValueMode: "0",
                secondSubjectId: "",
                secondSubjectName: "",
                secondSubjectJson: "",
                isSubjectThird: "1",
                thirdValueMode: "0",
                thirdSubjectId: "",
                thirdSubjectName: "",
                thirdSubjectJson: "",
                isSubjectFourth: "1",
                fourthValueMode: "0",
                fourthSubjectId: "",
                fourthSubjectName: "",
                fourthSubjectJson: "",
                subjectCollect: "",
                newList: [],
              },
              {
                isAssociationBatch: "1",
                associationBatchField: "",
                batchAbstractField: "",
                batchSubjectField: "",
                batchAmountField: "",
                twoSummaryList: [],
                threeSummaryList: [],
                fourSummaryList: [],
                id: "",
                rulesViceId: "",
                accountingField: "",
                subjectType: 1,
                firstSubjectId: "",
                firstSubjectName: "",
                accountSetsSubjectType: "",
                isSubjectSecond: "1",
                secondValueMode: "0",
                secondSubjectId: "",
                secondSubjectName: "",
                secondSubjectJson: "",
                isSubjectThird: "1",
                thirdValueMode: "0",
                thirdSubjectId: "",
                thirdSubjectName: "",
                thirdSubjectJson: "",
                isSubjectFourth: "1",
                fourthValueMode: "0",
                fourthSubjectId: "",
                fourthSubjectName: "",
                fourthSubjectJson: "",
                subjectCollect: "",
                newList: [],
              },
            ],
          },
        });
      }
    },
    handleTabsEdit(e) {
      console.log(e);
      this.params.oaVoucherRulesViceVos.map((item, index) => {
        if (item.name == e) {
          this.params.oaVoucherRulesViceVos.splice(index, 1);
        }
      });
      let arr = [...this.params.oaVoucherRulesViceVos];
      let type1 = [];
      let type2 = [];
      arr.map((item) => {
        if (item.rulerType == 0) {
          type1.push(item);
        } else {
          type2.push(item);
        }
      });
      arr.forEach((item) => {
        type1.forEach((v, i) => {
          if (item.name == v.name) {
            item.name = "记账凭证规则" + (i + 1);
          }
        });
        type2.forEach((v, i) => {
          if (item.name == v.name) {
            item.name = "记账凭证规则（在收款方账套下）" + (i + 1);
          }
        });
      });
      setTimeout(() => {
        this.params.oaVoucherRulesViceVos = [...arr];
      }, 100);
    },
    delItemC(i) {
      this.collSummaryList.splice(i, 1);
    },
    delItem(index, i) {
      this.params.oaVoucherRulesViceVos[index].summaryList.splice(i, 1);
    },
    handleSummaryClick(index, e) {
      if (e == 1) {
        const temp = {
          type: "input",
          value: "",
        };
        this.params.oaVoucherRulesViceVos[index].summaryList.push(temp);
        return;
      }
      if (e == 2) {
        const temp = {
          type: "select",
          value: "",
          label: "",
          options: [],
        };
        temp.options = this.flowList;
        this.params.oaVoucherRulesViceVos[index].summaryList.push(temp);
        return;
      }
    },

    handleSummaryDel() {
      if (this.summaryList.length > 1) {
        this.summaryList.pop();
      }
    },
    //修改有无

    collHandleSummaryClick(name) {
      if (name === "固定文本") {
        const temp = {
          type: "input",
          value: "",
        };
        this.collSummaryList.push(temp);
        return;
      }
      if (name === "模板动态元素") {
        const temp = {
          type: "select",
          value: "",
          options: [],
        };
        /*--------模拟请求后台 -----------------*/
        temp.options = this.flowList;
        // temp.options = [
        //   {
        //     label: "区域1",
        //     value: "北京"
        //   }
        // ];
        this.collSummaryList.push(temp);

        return;
      }
    },

    collHandleSummaryDel() {
      if (this.collSummaryList.length > 1) {
        this.collSummaryList.pop();
      }
    },
    updateAccountSets() {
      //账套发生改变 根据账套id查询 科目
      this.subjectTypeShow = true;
      // this.getSubjectTypeData(this.queryParams.accountSetsId);

      this.getSubjectData(this.params.accountSetsId);
    },

    // getSubjectTypeData(accountSetsId) {
    //   getSubjectTypes(accountSetsId).then(response => {
    //     this.subjectTypeList = response;
    //   });
    // },
    companyChenge(event) {
      console.log(event, this.projects);
      this.modelShow = true;
      const list = this.projects.filter((item) => item.companyId === event);

      var data = {
        classificationId: list[0].id,
      };
      //根据公司查询模板和账套
      getDataByRoleAndClassIdFinance(data).then((response) => {
        this.roleTemplList = response.rows;
      });
      //查询账套
      getAccountSetsList(list[0].companyId).then((response) => {
        this.accountSetsList = response;
      });
    },
    async getDatasById() {
      this.projectId = this.$route.query.pid;

      getColectionList().then((response) => {
        this.collectionList = response.rows;
      });

      if (this.projectId != "") {
        await getRulesById(this.projectId).then((response) => {
          allCompanyList().then((response2) => {
            this.projects = response2;
            const list = this.projects.filter(
              (item) => item.companyId === Number(response.data.companyNo)
            );
            getDataByRoleAndClassIdFinance({
              classificationId: list[0].id,
            }).then((response3) => {
              this.roleTemplList = response3.rows;
              console.log(this.roleTemplList, "-=-");
              this.params.associationId = response.data.associationId;
              this.params.salesmanList = response.data.salesmanList;
              this.params.financialStaffList = response.data.financialStaffList;
              getuser().then((res) => {
                this.userList = res;
                let yewu = this.userList.yewu.find((item) => {
                  return item.value == this.userId;
                });
                if (yewu) {
                  this.yewuType = true;
                  if (this.params.financialStaffList.includes(yewu.value)) {
                    return;
                  }
                  this.params.financialStaffList.push(yewu.value);
                  return;
                }
                let caiwu = this.userList.caiwu.find((item) => {
                  return item.value == this.userId;
                });
                if (caiwu) {
                  this.caiwuType = true;
                  if (this.params.salesmanList.includes(caiwu.value)) {
                    return;
                  }
                  this.params.salesmanList.push(caiwu.value);
                }
              });
              this.params.id = response.data.id;
              this.params.companyNo = Number(response.data.companyNo);
              this.params.remark = response.data.remark;
              this.params.accountSetsId = response.data.accountSetsId;
              this.params.oaVoucherRulesRecordsOldDataId =
                response.data.oaVoucherRulesRecordsOldDataId;
              this.params.subjectType = response.data.subjectType;
              this.params.finanicalWord = response.data.finanicalWord;
              this.params.isEnableDynamicForm =
                response.data.isEnableDynamicForm;
              this.params.oaVoucherRulesViceVos =
                response.data.oaVoucherRulesViceVos;
              this.params.oaVoucherRulesViceVos.map((item) => {
                this.$set(
                  item,
                  "summaryList",
                  JSON.parse(item.abstractJson) || []
                );
                this.$set(
                  item,
                  "tableWidth",
                  item.oaVoucherRulesSubjects.length * 480
                );
                item.oaVoucherRulesSubjects.forEach((i) => {
                  this.$set(i, "newList", []);
                  if (i.isAssociationBatch == 0) {
                    console.log(this.roleTemplList, "==");
                    let data = this.roleTemplList.find((item) => {
                      return item.parentId == response.data.flowModelId;
                    });
                    console.log(data, "=-");
                    getCheckTableField({
                      formId: data.formId,
                      checkTableCode: i.associationBatchField,
                    }).then((res) => {
                      i.newList = res;
                    });
                  }

                  this.$set(
                    i,
                    "twoSummaryList",
                    JSON.parse(i.secondSubjectJson) || []
                  );
                  this.$set(
                    i,
                    "threeSummaryList",
                    JSON.parse(i.thirdSubjectJson) || []
                  );
                  this.$set(
                    i,
                    "fourSummaryList",
                    JSON.parse(i.fourthSubjectJson) || []
                  );
                  i.twoWord = "";
                  i.threeWord = "";
                  i.fourWord = "";
                  if (i.twoSummaryList.length > 0) {
                    i.twoSummaryList.forEach((v) => {
                      if (v.type == "input") {
                        i.twoWord += v.value;
                      } else {
                        i.twoWord += "[" + v.label + "]";
                      }
                    });
                  } else {
                    i.twoWord = i.secondSubjectName || "";
                  }
                  if (i.threeSummaryList.length > 0) {
                    i.threeSummaryList.forEach((v) => {
                      if (v.type == "input") {
                        i.threeWord += v.value;
                      } else {
                        i.threeWord += "[" + v.label + "]";
                      }
                    });
                  } else {
                    i.threeWord = i.thirdSubjectName || "";
                  }
                  if (i.fourSummaryList.length > 0) {
                    i.fourSummaryList.forEach((v) => {
                      if (v.type == "input") {
                        i.fourWord += v.value;
                      } else {
                        i.fourWord += "[" + v.label + "]";
                      }
                    });
                  } else {
                    i.fourWord = i.fourthSubjectName || "";
                  }
                  setTimeout(() => {
                    i.subjectCollect =
                      i.firstSubjectName +
                      "-" +
                      i.twoWord +
                      "-" +
                      i.threeWord +
                      "-" +
                      i.fourWord;
                  }, 500);
                });
              });
              this.params.collSubjectType = response.data.collSubjectType;
              this.params.collFinanicalWord = response.data.collFinanicalWord;
              this.params.flowModelId = response.data.flowModelId;
              this.params.flowModelName = response.data.flowModelName;
              this.params.accountingField = response.data.accountingField;
              this.params.isVoucher = response.data.isVoucher;
              this.params.collectionField = response.data.collectionField;
              this.modelShow = true;
              this.accFieldShow = true;
              this.subjectTypeShow = true;

              this.oldData = JSON.parse(JSON.stringify(this.params));
              //查询账套
              getAccountSetsList(this.params.companyNo).then((response) => {
                this.accountSetsList = response;
              });
              //根据模板id查询详情
              getTemplateId(this.params.flowModelId).then((response) => {
                //查询记账金额字段
                var dataForm = {
                  formId: response.data.oaProcessTemplate.formId,
                };

                queryFormFeild(dataForm).then((response) => {
                  this.flowList = response;
                });
              });
              //获取一级科目
              if (this.params.accountSetsId) {
                this.getSubjectData(this.params.accountSetsId);
              }
            });
          });
        });

        // var data = {
        //   classificationId: this.queryParams.companyNo
        // };

        //根据账套查询类型
        // if (this.queryParams.accountSetsId) {
        //   this.getSubjectTypeData(this.queryParams.accountSetsId);
        // }
      } else {
        //查询公司
        getuser().then((res) => {
          this.userList = res;
          let yewu = this.userList.yewu.find((item) => {
            return item.value == this.userId;
          });
          if (yewu) {
            this.yewuType = true;
            this.params.financialStaffList.push(yewu.value);
            return;
          }
          let caiwu = this.userList.caiwu.find((item) => {
            return item.value == this.userId;
          });
          if (caiwu) {
            this.caiwuType = true;
            this.params.salesmanList.push(caiwu.value);
          }
        });
        allCompanyList().then((response) => {
          this.projects = response;
        });
      }

      // noForbiddenList().then(response => {
      //   this.modelList = response.rows;
      // });
    },

    async getSubjectData(accountSetsId) {
      //一级科目借条件
      var data = {
        // balanceDirection: "借",
        status: "1",
        // type: subjectType,
        parentId: "0",
        level: "1",
        accountSetsId: accountSetsId,
      };
      console.log("进入");
      await getAllFirstSubject().then((response) => {
        this.firstBorrowSubjectList = response;
        this.firstLoanSubjectList = response;
      });
      // //一级科目贷条件
      // var data = {
      //   balanceDirection: "贷",
      //   status: "1",
      //   type: subjectType,
      //   accountSetsId: accountSetsId,
      //   parentId: "0",
      //   level: "1",
      //   accountSetsId: accountSetsId
      // };
      // await listSubjectSelect(data).then(response => {
      //   this.firstLoanSubjectList = response;
      // });
    },

    handleRadioInput1(i) {
      console.log(i);
      const { debitRadio, creditRadio } =
        this.payFormList[i].table.tableList2[0];
      if (debitRadio === 1 && creditRadio === 1) {
        this.payFormList[i].tableIsShow.tableList3 = false;
        this.payFormList[i].tableList4 = false;
        this.payFormList[i].table.tableList2 = [
          { ...this.info, title: "第二科目" },
        ];
        this.payFormList[i].table.tableList3 = [
          { ...this.info, title: "第三科目" },
        ];
        this.payFormList[i].table.tableList4 = [
          { ...this.info, title: "第四科目" },
        ];
      }
      if (debitRadio !== 1 || creditRadio !== 1) {
        this.payFormList[i].tableIsShow.tableList3 = true;
      }
      if (debitRadio === 1) {
        this.payFormList[i].table.tableList2 = [
          {
            ...this.payFormList[i].table.tableList2[0],
            debit: "",
            debitRadio: 1,
            debitTake: 1,
            debitAccountName: "",
            debitEntryName: "",
          },
        ];
      }
      if (creditRadio === 1) {
        this.payFormList[i].table.tableList2 = [
          {
            ...this.payFormList[i].table.tableList2[0],
            credit: "",
            creditRadio: 1,
            creditTake: 1,
            creditAccountName: "",
            creditEntryName: "",
          },
        ];
      }
    },
    handleRadioInput2(i) {
      const { debitRadio, creditRadio } =
        this.payFormList[i].table.tableList3[0];
      if (debitRadio === 1 && creditRadio === 1) {
        this.payFormList[i].tableIsShow.tableList4 = false;
        this.payFormList[i].table.tableList3 = [
          { ...this.info, title: "第三科目" },
        ];
        this.payFormList[i].table.tableList4 = [
          { ...this.info, title: "第四科目" },
        ];
      }
      if (debitRadio !== 1 || creditRadio !== 1) {
        this.payFormList[i].tableIsShow.tableList4 = true;
      }
      if (debitRadio === 1) {
        this.payFormList[i].table.tableList3 = [
          {
            ...this.payFormList[i].table.tableList3[0],
            debit: "",
            debitRadio: 1,
            debitTake: 1,
            debitAccountName: "",
            debitEntryName: "",
          },
        ];
      }
      if (creditRadio === 1) {
        this.payFormList[i].table.tableList3 = [
          {
            ...this.payFormList[i].table.tableList3[0],
            credit: "",
            creditRadio: 1,
            creditTake: 1,
            creditAccountName: "",
            creditEntryName: "",
          },
        ];
      }
    },
    handleRadioInput3(i) {
      const { debitRadio, creditRadio } =
        this.payFormList[i].table.tableList4[0];
      if (debitRadio === 1) {
        this.payFormList[i].table.tableList4 = [
          {
            ...this.payFormList[i].table.tableList4[0],
            debit: "",
            debitRadio: 1,
            debitTake: 1,
            debitAccountName: "",
            debitEntryName: "",
          },
        ];
      }
      if (creditRadio === 1) {
        this.payFormList[i].table.tableList4 = [
          {
            ...this.payFormList[i].table.tableList4[0],
            credit: "",
            creditRadio: 1,
            creditTake: 1,
            creditAccountName: "",
            creditEntryName: "",
          },
        ];
      }
    },

    handleRadioInput4() {
      const { debitRadio, creditRadio } = this.tables.tableList6[0];
      if (debitRadio === 1 && creditRadio === 1) {
        this.tableIsShow2.tableList7 = false;
        this.tableIsShow2.tableList8 = false;
        this.tables.tableList6 = [{ ...this.info2, title: "第二科目" }];
        this.tables.tableList7 = [{ ...this.info2, title: "第三科目" }];
        this.tables.tableList8 = [{ ...this.info2, title: "第四科目" }];
      }
      if (debitRadio !== 1 || creditRadio !== 1) {
        this.tableIsShow2.tableList7 = true;
      }
      if (debitRadio === 1) {
        this.tables.tableList6 = [
          {
            ...this.tables.tableList6[0],
            debit: "",
            debitRadio: 1,
            debitTake: 1,
            debitAccountName: "",
            debitEntryName: "",
          },
        ];
      }
      if (creditRadio === 1) {
        this.tables.tableList6 = [
          {
            ...this.tables.tableList6[0],
            credit: "",
            creditRadio: 1,
            creditTake: 1,
            creditAccountName: "",
            creditEntryName: "",
          },
        ];
      }
    },
    pushItem() {
      this.payFormList.push({
        tableIsShow: {
          tableList3: false,
          tableList4: false,
        },
        table: {
          tableList1: [
            {
              title: "第一科目",
              debit: "",
              debitId: "",
              credit: "",
              creditId: "",
            },
          ],
          tableList2: [
            {
              title: "第二科目",
              secondBorrowId: "",
              debit: "",
              debitRadio: 1,
              debitTake: 1,
              debitAccountName: "",
              debitEntryName: "",
              secondLoanId: "",
              credit: "",
              creditRadio: 1,
              creditTake: 1,
              creditAccountName: "",
              creditEntryName: "",
            },
          ],
          tableList3: [
            {
              title: "第三科目",
              thirdBorrowId: "",
              debit: "",
              credit: "",
              debitRadio: 1,
              thirdLoanId: "",
              creditRadio: 1,
              creditTake: 1,
              debitTake: 1,
              debitAccountName: "",
              debitEntryName: "",
              creditAccountName: "",
              creditEntryName: "",
            },
          ],
          tableList4: [
            {
              title: "第四科目",
              debit: "",
              credit: "",
              debitRadio: 1,
              creditRadio: 1,
              creditTake: 1,
              debitTake: 1,
              fourthLoanId: "",
              fourthBorrowId: "",
              debitAccountName: "",
              debitEntryName: "",
              creditAccountName: "",
              creditEntryName: "",
            },
          ],
        },
        accountingField: "",
        finanicalWord: "",
        isEnableDynamicForm: "0",
        summaryList: [
          {
            type: "input",
            value: "",
          },
        ],
      });
    },
    handleRadioInput5() {
      const { debitRadio, creditRadio } = this.tables.tableList7[0];
      if (debitRadio === 1 && creditRadio === 1) {
        this.tableIsShow2.tableList8 = false;
        this.tables.tableList7 = [{ ...this.info, title: "第三科目" }];
        this.tables.tableList8 = [{ ...this.info, title: "第四科目" }];
      }
      if (debitRadio !== 1 || creditRadio !== 1) {
        this.tableIsShow2.tableList8 = true;
      }
      if (debitRadio === 1) {
        this.tables.tableList7 = [
          {
            ...this.tables.tableList7[0],
            debit: "",
            debitRadio: 1,
            debitTake: 1,
            debitAccountName: "",
            debitEntryName: "",
          },
        ];
      }
      if (creditRadio === 1) {
        this.tables.tableList7 = [
          {
            ...this.tables.tableList7[0],
            credit: "",
            creditRadio: 1,
            creditTake: 1,
            creditAccountName: "",
            creditEntryName: "",
          },
        ];
      }
    },
    handleRadioInput6() {
      const { debitRadio, creditRadio } = this.tables.tableList8[0];
      if (debitRadio === 1) {
        this.tables.tableList8 = [
          {
            ...this.tables.tableList8[0],
            debit: "",
            debitRadio: 1,
            debitTake: 1,
            debitAccountName: "",
            debitEntryName: "",
          },
        ];
      }
      if (creditRadio === 1) {
        this.tables.tableList8 = [
          {
            ...this.tables.tableList8[0],
            credit: "",
            creditRadio: 1,
            creditTake: 1,
            creditAccountName: "",
            creditEntryName: "",
          },
        ];
      }
    },

    handleCancel() {
      this.$router.push({ path: "/oa/tallyVoucher" });
    },
    handleSave() {
      if (!this.params.companyNo) {
        this.$message.warning("请选择公司");
        return;
      }
      if (!this.params.flowModelId) {
        this.$message.warning("请选择流程模板");
        return;
      }
      if (!this.params.accountSetsId) {
        this.$message.warning("请选择账套");
        return;
      }
      if (
        this.$route.query.changeEditType == "true" &&
        this.params.salesmanList.length == 0
      ) {
        this.$message.warning("请选择财务负责人");
        return;
      }
      if (
        this.$route.query.changeEditType == "true" &&
        this.params.financialStaffList.length == 0
      ) {
        this.$message.warning("请选择业务负责人");
        return;
      }
      if (
        this.params.oaVoucherRulesViceVos.length == 0 ||
        !this.params.oaVoucherRulesViceVos
      ) {
        this.$message.warning("请填写完整表单");

        return;
      }

      var falg = false;
      this.params.oaVoucherRulesViceVos.forEach((item) => {
        this.$set(item, "abstractJson", JSON.stringify(item.summaryList));
        // item.abstractJson = JSON.stringify(item.summaryList);
        item.oaVoucherRulesSubjects.forEach((i) => {
          i.secondSubjectJson = JSON.stringify(i.twoSummaryList);
          if (!i.secondSubjectName || i.secondValueMode == 1) {
            i.secondSubjectName = i.twoWord;
          }
          i.thirdSubjectJson = JSON.stringify(i.threeSummaryList);
          if (!i.thirdSubjectName || i.thirdValueMode == 1) {
            i.thirdSubjectName = i.threeWord;
          }
          i.fourthSubjectJson = JSON.stringify(i.fourSummaryList);
          if (!i.fourthSubjectName || i.fourthValueMode == 1) {
            i.fourthSubjectName = i.fourWord;
          }
          if (!i.firstSubjectName || !i.accountingField) {
            falg = true;
          }
          if (
            i.isAssociationBatch == 0 &&
            (!i.associationBatchField ||
              !i.batchAmountField ||
              !i.batchSubjectField ||
              !i.batchAbstractField)
          ) {
            falg = true;
          }
        });
      });
      console.log(this.params);
      setTimeout(() => {
        if (falg == true) {
          this.$message.warning("请填写完整表单");
          return;
        }
        console.log(this.oldData, this.params, "--");
        if (this.$route.query.changeEditType == "false") {
          addRulesMain(this.params).then((res) => {
            this.$message.success("操作成功！");
            this.$store.state.editTemplate = !this.$store.state.editTemplate;

            setTimeout(() => {
              this.$router.push({ path: "/oa/tallyVoucher" });
            }, 150);
          });
        } else {
          this.params.editType = !this.projectId ? "0" : "1";
          addNewEditInfo(this.params).then((res) => {
            this.$message.success("操作成功！");
            this.$store.state.editTemplate = !this.$store.state.editTemplate;

            setTimeout(() => {
              this.$router.push({ path: "/oa/tallyVoucher" });
            }, 150);
          });
        }
      }, 100);
    },
    updatesubject1(e, index, i) {
      console.log(e, index, i);
      const list = this.firstBorrowSubjectList.filter((item) => item.id === e);
      this.params.oaVoucherRulesViceVos[index].oaVoucherRulesSubjects[
        i
      ].firstSubjectName = list[0].name;
      //获取下方二级目录
      return;
      var data = {
        // type: this.queryParams.subjectType,
        accountSetsId: this.queryParams.accountSetsId,
        // balanceDirection: "借",
        status: "1",
        parentId: this.payFormList[i].table.tableList1[0].debitId,
        level: "2",
      };
      listSubjectSelect(data).then((response) => {
        this.secondBorrowSubjectList = response;
      });
    },
    updatesubject2(event, i) {
      const list = this.firstLoanSubjectList.filter(
        (item) => item.id === event
      );
      this.payFormList[i].table.tableList1[0].credit = list[0].name;

      //获取下方二级目录
      var data = {
        type: this.queryParams.subjectType,
        accountSetsId: this.queryParams.accountSetsId,
        balanceDirection: "贷",
        status: "1",
        parentId: this.payFormList[i].table.tableList1[0].creditId,
        level: "2",
      };
      listSubjectSelect(data).then((response) => {
        this.secondLoanSubjectList = response;
      });
    },
    //获取三级科目和
    updatesubject3(i) {
      if (
        this.payFormList[i].table.tableList2[0].secondBorrowId != "" &&
        undefined != this.payFormList[i].table.tableList2[0].secondBorrowId
      ) {
        //获取下方三级目录
        var data = {
          // type: this.queryParams.subjectType,
          accountSetsId: this.queryParams.accountSetsId,
          // balanceDirection: "借",
          status: "1",
          parentId: this.payFormList[i].table.tableList2[0].secondBorrowId,
          level: "3",
        };
        listSubjectSelect(data).then((response) => {
          this.thirdBorrowSubjectList = response;
        });
      }
    },
    updatesubject4(i) {
      if (
        this.payFormList[i].table.tableList2[0].secondLoanId != "" &&
        undefined != this.payFormList[i].table.tableList2[0].secondLoanId
      ) {
        //获取下方三级目录
        var data = {
          // type: this.queryParams.subjectType,
          accountSetsId: this.queryParams.accountSetsId,
          // balanceDirection: "贷",
          status: "1",
          parentId: this.payFormList[i].table.tableList2[0].secondLoanId,
          level: "3",
        };
        listSubjectSelect(data).then((response) => {
          this.thirdLoanSubjectList = response;
        });
      }
    },

    //获取四级级科目和
    updatesubject5(i) {
      if (
        this.payFormList[i].table.tableList3[0].thirdBorrowId != "" &&
        undefined != this.payFormList[i].table.tableList3[0].thirdBorrowId
      ) {
        //获取下方四级目录
        var data = {
          // type: this.queryParams.subjectType,
          accountSetsId: this.queryParams.accountSetsId,
          // balanceDirection: "借",
          status: "1",
          parentId: this.payFormList[i].table.tableList3[0].thirdBorrowId,
          level: "4",
        };
        listSubjectSelect(data).then((response) => {
          this.fourthBorrowSubjectList = response;
        });
      }
    },
    updatesubject6(i) {
      if (
        this.payFormList[i].table.tableList3[0].thirdLoanId != "" &&
        undefined != this.payFormList[i].table.tableList3[0].thirdLoanId
      ) {
        //获取下方四级目录
        var data = {
          // type: this.queryParams.subjectType,
          accountSetsId: this.queryParams.accountSetsId,
          // balanceDirection: "贷",
          status: "1",
          parentId: this.payFormList[i].table.tableList3[0].thirdLoanId,
          level: "4",
        };
        listSubjectSelect(data).then((response) => {
          this.fourthLoanSubjectList = response;
        });
      }
    },

    updatemodel(event) {
      console.log(event);
      this.roleTemplList.forEach((item) => {
        if (item.parentId == event) {
          this.params.flowModelName = item.templateName;
          this.lastformId = item.formId;
          queryFormFeild({ formId: item.formId }).then((response) => {
            this.flowList = response;
          });
          this.accFieldShow = true;
        }
      });
    },
    clearSubjectId1(event) {
      this.tables.tableList2[0].secondBorrowId = "";
    },
    clearSubjectId2(event) {
      this.tables.tableList2[0].secondLoanId = "";
    },
    clearSubjectId3(event) {
      this.tables.tableList3[0].thirdBorrowId = "";
    },
    clearSubjectId4(event) {
      this.tables.tableList3[0].thirdLoanId = "";
    },
    clearSubjectId5(event) {
      this.tables.tableList4[0].fourthBorrowId = "";
    },
    clearSubjectId6(event) {
      this.tables.tableList4[0].fourthLoanId = "";
    },
    updatekemudebit2(event, i) {
      const list = this.flowList.filter((item) => item.value === event);
      this.payFormList[i].table.tableList2[0].debitAccountName = list[0].label;
    },
    updatekemucredit2(event, i) {
      const list = this.flowList.filter((item) => item.value === event);
      this.payFormList[i].table.tableList2[0].creditAccountName = list[0].label;
    },

    updatekemudebit3(event, i) {
      const list = this.flowList.filter((item) => item.value === event);
      this.payFormList[i].table.tableList3[0].debitAccountName = list[0].label;
    },
    updatekemucredit3(event, i) {
      const list = this.flowList.filter((item) => item.value === event);
      this.payFormList[i].table.tableList3[0].creditAccountName = list[0].label;
    },

    updatekemudebit4(event, i) {
      const list = this.flowList.filter((item) => item.value === event);
      this.payFormList[i].table.tableList4[0].debitAccountName = list[0].label;
    },
    updatekemucredit4(event, i) {
      const list = this.flowList.filter((item) => item.value === event);
      this.payFormList[i].table.tableList4[0].creditAccountName = list[0].label;
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) !==
          -1
        );
      };
    },
    /**输入框 借方二级科目可搜索*/
    querySearchBorrow2(queryString, cb) {
      var restaurants = this.secondBorrowSubjectList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    selectBorrow2(item) {
      console.log(item.id);
      this.tables.tableList2[0].secondBorrowId = item.id;
      console.log(this.tables.tableList2[0].secondBorrowId);
    },
    querySearchLoan2(queryString, cb) {
      var restaurants = this.secondLoanSubjectList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    selectLoan2(item) {
      console.log(item.id);
      this.tables.tableList2[0].secondLoanId = item.id;
      console.log(this.tables.tableList2[0].secondLoanId);
    },

    /**输入框 借方三级科目可搜索*/
    querySearchBorrow3(queryString, cb) {
      var restaurants = this.thirdBorrowSubjectList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    selectBorrow3(item) {
      console.log(item.id);
      this.tables.tableList3[0].secondBorrowId = item.id;
      console.log(this.tables.tableList3[0].secondBorrowId);
    },
    querySearchLoan3(queryString, cb) {
      var restaurants = this.thirdLoanSubjectList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    selectLoan3(item) {
      console.log(item.id);
      this.tables.tableList3[0].secondLoanId = item.id;
      console.log(this.tables.tableList3[0].secondLoanId);
    },

    /**输入框 借方四级科目可搜索*/
    querySearchBorrow4(queryString, cb) {
      var restaurants = this.fourthBorrowSubjectList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    selectBorrow4(item) {
      console.log(item.id);
      this.tables.tableList4[0].fourthBorrowId = item.id;
      console.log(this.tables.tableList4[0].fourthBorrowId);
    },
    querySearchLoan4(queryString, cb) {
      var restaurants = this.fourthLoanSubjectList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    selectLoan4(item) {
      console.log(item.id);
      this.tables.tableList4[0].fourthLoanId = item.id;
      console.log(this.tables.tableList4[0].fourthLoanId);
    },
  },
};
</script>
  <style lang="less" scoped>
.table_data {
  overflow: auto;
  border: 1px solid #ccc;
  border-bottom: none;
  box-sizing: border-box;
  .head {
    display: flex;

    .title2 {
      padding: 12px;
      border-right: 1px solid #ccc;
      border-bottom: 1px solid #ccc;
      box-sizing: border-box;
      text-align: center;
      background: #f2f2f2;
      overflow: auto;
    }
  }
}
.word_box {
  width: 100%;
  height: 50px;
  border: 1px solid #cccccc;
  display: flex;
  align-items: center;
  padding-left: 16px;
}
.word_box_item {
  width: 270px;
  height: 40px;
  background: #e8f4ff;
  display: flex;
  align-items: center;
  padding-left: 12px;
}
.btns {
  padding: 20px;
}
.solid2 {
  width: 100%;
  height: 10px;
  background: #f2f2f2;
}
.el-icon-delete {
  margin-left: 5px;
  font-size: 16px;
  cursor: pointer;
}
.paybox,
.shoukbox {
  .pay_item {
    margin-bottom: 12px;
    padding-bottom: 12px;
    .solid {
      width: 98%;
      height: 2px;
      background: #f2f2f2;
      margin: 0 auto;
      margin-top: 12px;
      margin-bottom: 12px;
    }
    .subject {
      width: 98%;
      margin: 0 auto;
      .title2 {
        font-size: 14px;
        font-weight: bold;
      }
      .title3 {
        color: #999;
        margin-bottom: 0px;
      }
    }
    border: 1px solid #e0e7ed;
    .item_title {
      font-size: 16px;
      font-weight: bold;
      background: #f8f8f9;
      height: 40px;
      line-height: 40px;
      padding-left: 12px;
      border-bottom: 1px solid #e0e7ed;
      margin-bottom: 12px;
    }
  }
  .item {
    margin-bottom: 12px;
    /deep/ .el-input__inner {
      width: 250px !important;
      height: 32px !important;
    }
    span {
      display: inline-block;
      width: 200px;
      text-align: right;
      margin-right: 12px;
      i {
        color: red;
        margin-right: 5px;
      }
    }
  }
}
.shoukbox {
}
.header {
  .item {
    margin-bottom: 12px;
    /deep/ .el-input__inner {
      width: 250px !important;
    }
    span {
      display: inline-block;
      width: 200px;
      text-align: right;
      margin-right: 12px;
      i {
        color: red;
        margin-right: 5px;
      }
    }
  }
}
.el-form-item {
  margin-bottom: 0;
}
.btn-container {
  margin: 10px 0;
  width: 100%;
  text-align: center;
}
</style>
