<template>
  <div class="app-container">
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      v-show="showSearch"
      label-width="98px"
    >
      <el-form-item label="加班日期">
        <el-date-picker
          v-model="dateRange"
          value-format="yyyy-MM-dd"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="handleQuery"
          :picker-options="pickerOptions"
        >
        </el-date-picker>
      </el-form-item>
      <el-form-item label="人员姓名" prop="nickName">
        <el-input
          v-model="queryParams.nickName"
          placeholder="请输入人员姓名"
          clearable
          size="small"
          style="width: 240px"
          @keyup.enter.native="handleQuery"
          @clear="handleQuery"
        />
      </el-form-item>
      <el-form-item label="审核状态" prop="state">
        <el-select
          v-model="queryParams.state"
          placeholder="请选择审核状态"
          clearable
          @clear="handleQuery"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in dict.type.check_work_approve_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="生效状态" prop="effective">
        <el-select
          v-model="queryParams.effective"
          placeholder="请选择生效状态"
          clearable
          @clear="handleQuery"
          @change="handleQuery"
        >
          <el-option
            v-for="dict in effectiveList"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <el-divider></el-divider>
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" size="mini" @click="handleAdd"
          >加班申请</el-button
        >
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="multipleSelection.length != 1"
          @click="handleUpdateOpear"
          >修改</el-button
        >
      </el-col> -->
      <!-- <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleDelete"
          :disabled="!Boolean(multipleSelection.length)"
          >删除</el-button
        >
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-check"
          size="mini"
          plain
          @click="submit"
          :disabled="multipleSelection.length != 1"
          >提交</el-button
        >
      </el-col> -->
      <right-toolbar
        :showSearch.sync="showSearch"
        @queryTable="getList"
      ></right-toolbar>
    </el-row>
    <el-table
      :row-style="{ height: '35px' }"
      v-loading="loading"
      :data="configList"
      @selection-change="handleSelectionChange"
      row-key="id"
      ref="multipleTable"
    >
      <el-table-column
        type="selection"
        width="55"
        align="center"
        fixed="left"
        reserve-selection
      />
      <el-table-column
        type="index"
        label="序号"
        width="50"
        :index="columnIndex"
      />
      <el-table-column label="加班单号" align="center" width="130">
        <template #default="{ row }">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdateOpearView(row)"
            >{{ row.workCode }}</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="人员姓名" align="center" width="120">
        <template #default="{ row }">
          <el-button size="mini" type="text" @click="getUserData(row)">{{
            row.nickName
          }}</el-button>
        </template>
      </el-table-column>
      <el-table-column label="加班日期" align="center" width="280">
        <template #default="{ row }">
          <div
            v-for="(item, index) in row.workOvertimeSlaveList"
            :key="index"
            style="height: 46px"
            class="flex flex-col justify-start"
          >
            <div>
              {{ `${item.startTime} ${item.startTimePeriod}` }} 至
              {{ `${item.endTime} ${item.endTimePeriod}` }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="加班时长(天)"
        align="center"
        prop="times"
        width="140"
      >
        <template #default="{ row }">
          <div
            v-for="(item, index) in row.workOvertimeSlaveList"
            :key="index"
            style="height: 46px"
            class="flex flex-col justify-start"
          >
            <div>{{ item.times }}</div>
            <div
              v-show="row.workOvertimeSlaveList.length > 1"
              class="relative bottom-2"
            >
              (合计{{ row.totalTimes }})
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="申请时间"
        align="center"
        prop="applicationTime"
        width="160"
      />
      <el-table-column
        label="工作内容"
        align="center"
        prop="content"
        minWidth="200"
      />
      <el-table-column label="审核状态" align="center" width="120">
        <template #default="{ row }">
          <div size="mini" type="text">
            {{ dict.label.check_work_approve_status[row.state] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="生效状态" align="center" width="120">
        <template #default="{ row }">
          <div>
            {{ effectiveObj[row.effective] }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" fixed="right" width="180px">
        <template #default="{ row }">
          <el-button
            size="mini"
            type="text"
            @click="handleUpdateOpear(row)"
            v-if="['1', '4'].includes(row.state)"
            >修改</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="submit(row)"
            v-if="['1', '4'].includes(row.state)"
            >提交</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="handleDelete(row)"
            v-if="['1', '4'].includes(row.state)"
            >删除</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="viewProcessId(row)"
            v-if="['2', '3'].includes(row.state)"
            >查看流程</el-button
          >
          <el-button
            size="mini"
            type="text"
            @click="openVoidDialog(row)"
            v-if="
              ['2', '3'].includes(row.state) &&
              row.createBy == $store.getters.name &&
              row.effective == 0
            "
            >作废加班申请</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
    <DetailDialog
      :form="form"
      :formTitle="formTitle"
      v-model="open"
      @on-save-success="getList"
      @on-submit-success="submitUpdata"
    />
    <SelectCompany
      v-if="selectCompanyType"
      @close="closeCompany"
      @submit="submitCompany"
    />
    <DetailDialogVoid
      :form="form"
      type="overTime"
      v-model="openVoid"
      @onSubmit="getList"
    >
      <div>您确认要作废该条加班申请流程吗？</div>
      <div>
        确认后将发送给人事部门确认，人事部门同意后会通知到已审批该申请的所有审核人，您的加班申请将作废，不会统计到系统中
      </div></DetailDialogVoid
    >
    <UserDetail
      v-model="userDetailType"
      @close="userDetailType = false"
      :id="userId"
    />
  </div>
</template>

<script>
import {
  overtimeList,
  delOvertime,
  getWorkOvertimeFlow,
  addOvertime,
} from "@/api/checkWork/overTime";
import config from "./components/config";
import DetailDialog from "./components/DetailDialog.vue";
import DetailDialogVoid from "../components/DetailDialogVoid.vue";
import SelectCompany from "@/components/SelectCompany/index.vue";
import tableSelect from "@/mixin/table-select";
export default {
  name: "OverTime",
  components: { DetailDialog, SelectCompany, DetailDialogVoid },
  mixins: [tableSelect],
  dicts: ["check_work_approve_status"],
  data() {
    return {
      ...config,
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: true,
      // 参数表格数据
      configList: [],
      // 查询参数
      total: 0,
      dateRange: [],
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        state: "",
        effective: "",
        nickName:undefined
      },
      open: false,
      form: {},
      formTitle: "",
      multipleSelection: [],
      multipleSelectionAdd: null,
      selectCompanyType: false,
      openVoid: false,
      userDetailType: false,
      userId: "",
    };
  },
  watch: {},

  created() {
    this.getDefaultTime();
    this.init();
  },
  methods: {
    init() {
      this.getList();
    },
    getDefaultTime() {
      if (this.$route.query.time) {
        let dateStr = this.$route.query.time;
        let [year, month] = dateStr.split("-").map(Number); // 将字符串分割并转为数字
        // 获取该月的第一天
        let firstDay = new Date(year, month - 1, 1); // 月份从0开始，因此需要减1
        // 获取该月的最后一天
        let lastDay = new Date(year, month, 0); // 设置为下个月的第0天，即上一个月的最后一天

        // 格式化为 'YYYY-MM-DD' 格式
        let formatDate = (date) => {
          let year = date.getFullYear();
          let month = String(date.getMonth() + 1).padStart(2, "0"); // 月份需要加 1
          let day = String(date.getDate()).padStart(2, "0"); // 日需要补充零
          return `${year}-${month}-${day}`;
        };
        // 返回格式化后的结果
        this.dateRange = [formatDate(firstDay), formatDate(lastDay)];
      }
    },
    /** 查询参数列表 */
    async getList() {
      this.loading = true;
      const { rows, total } = await overtimeList(
        this.addDateRangeCustom(this.queryParams, this.dateRange, [
          "startTime",
          "endTime",
        ])
      );
      rows.forEach((item) => {
        item.totalTimes =
          item.workOvertimeSlaveList
            ?.map((item) => item.times)
            .reduce(
              (accumulator, currentValue) => accumulator + currentValue,
              0
            ) + "天";
      });
      this.configList = rows;
      this.total = total;
      this.$refs.multipleTable.clearSelection();
      this.loading = false;
    },
    columnIndex(index) {
      return (
        index + 1 + (this.queryParams.pageNum - 1) * this.queryParams.pageSize
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.dateRange = [];
      this.handleQuery();
    },
    getUserData(data) {
      this.userId = data.userId;
      this.userDetailType = true;
    },
    handleAdd() {
      this.formTitle = "添加加班申请";
      this.form = {};
      this.open = true;
    },
    updateSubmitCheck(value) {
      const state = value?.state || this.multipleSelection[0].state;
      if (["2", "3"].includes(state)) {
        this.$message.warning("请选择审核状态为未提交或不通过的数据");
        return true;
      } else {
        return false;
      }
    },
    openVoidDialog(row) {
      this.form = { ...row };
      this.openVoid = true;
    },
    handleUpdateOpear(row) {
      // if (this.updateSubmitCheck()) return;
      this.formTitle = "修改加班申请";
      this.form = { ...row };
      this.open = true;
    },
    handleUpdateOpearView(row) {
      this.formTitle = "查看加班申请";
      this.form = { ...row };
      this.open = true;
    },
    deleteCheck() {
      const noAllow = ["2", "3"];
      const allowBorrowing = this.multipleSelection.some((item) =>
        noAllow.includes(item.state)
      );
      if (allowBorrowing) {
        this.$message.warning("请选择审核状态为未提交或不通过的数据");
        return true;
      } else {
        return false;
      }
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      //  if(this.deleteCheck())return;
      const ids = [row.id];
      const idNames = row.nickName;
      this.$modal
        .confirm('是否确认删除人员姓名为"' + idNames + '"的数据项？')
        .then(function () {
          return delOvertime(ids);
        })
        .then(async () => {
          await this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    },
    async submit(row) {
      const { data } = await addOvertime(row);
      this.submitUpdata(data);
    },
    async submitUpdata(value) {
      // if(this.updateSubmitCheck(value))return;
      const processId = value?.processId;
      if (processId) {
        this.$router.push({
          path: "/oaWork/processFormView",
          query: {
            oid: processId,
            myActiviteType: true,
          },
        });
        return;
      }
      try {
        this.multipleSelectionAdd = value;
        this.selectCompanyType = true;
      } catch (error) {}
    },
    submitCompany(e) {
      getWorkOvertimeFlow({ companyId: e }).then((res) => {
        if (res.code == 200) {
          this.selectCompanyType = false;
          const data = this.multipleSelectionAdd;
          sessionStorage.setItem(
            "oa-checkWorkOverTimeForm",
            JSON.stringify(data)
          );
          this.$router.push({
            path: "/oaWork/updateProcessForm",
            query: {
              templateId: res.templateId,
              classificationId: res.classificationId,
              companyId: res.companyId,
              checkWorkOverTime: true,
            },
          });
        }
      });
    },
    closeCompany() {
      this.multipleSelectionAdd = null;
      this.selectCompanyType = false;
      this.getList();
    },
    viewProcessId(value) {
      this.$router.push({
        path: "/oaWork/processFormView",
        query: {
          oid: value.processId,
          businessId: value.processId,
        },
      });
    },
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
    },
  },
};
</script>
<style lang="less" scoped>
::v-deep .el-table__body-wrapper,
::v-deep .el-table__fixed-body-wrapper {
  .el-table__cell {
    vertical-align: top;
    padding-bottom: 0px;
    .cell {
      padding-top: 10px;
      white-space: pre-line;
    }
  }
}
</style>