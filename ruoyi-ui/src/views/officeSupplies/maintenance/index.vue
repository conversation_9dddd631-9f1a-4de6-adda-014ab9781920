<template>
  <div style="height: 100%">
    <div class="search">
      <div class="item">
        <span>物品名称</span>
        <el-input v-model="params.itemName" clearable="" placeholder="请输入物品名称" style="width: 200px"></el-input>
      </div>

      <div class="item">
        <span>所属公司</span>
        <el-select placeholder="请选择所属公司" clearable="" style="width: 200px" v-model="params.companyId" filterable>
          <el-option v-for="item in projects" :key="item.id" :label="item.companyShortName" :value="item.id">
          </el-option>
        </el-select>
      </div>
      <div class="item">
        <span>启用状态</span>
        <el-select placeholder="请选择启用状态" clearable="" style="width: 200px" v-model="params.status" filterable>
          <el-option label="正常" value="0"> </el-option>
          <el-option label="停用" value="1"> </el-option>
        </el-select>
      </div>
      <el-button size="mini" type="primary" icon="el-icon-search" @click="search">搜 索</el-button>
      <el-button size="mini" icon="el-icon-refresh" @click="reset">重 置</el-button>
    </div>
    <div class="solid"></div>
    <div class="content">
      <div class="left">
        <el-input v-model="filterText" placeholder="请输入类别名称" style="width: 210px"></el-input>
        <el-tree class="filter-tree" :data="leftTreeList" :props="defaultProps" :default-expand-all="false"
          :filter-node-method="filterNode" @node-click="handleNodeClick" ref="tree" node-key="id"
          :highlight-current="true">
        </el-tree>
      </div>
      <div class="right">
        <div class="header_btn" style="display: flex; align-items: center">
          <el-button style="border-color: #aed8ff; background: #e8f4ff; color: #3fa1ff" icon="el-icon-plus"
            type="primary" size="mini" @click="newData" v-hasPermi="['maintenance:addEdit']">新建</el-button>
          <el-button type="success" plain icon="el-icon-edit" size="mini" @click="exportExcel"
            v-hasPermi="['maintenance:export']">导出列表</el-button>
          <el-button v-hasPermi="['maintenance:changeGroy']" type="primary" plain icon="el-icon-edit"
            :disabled="multipleSelection.length == 0" size="mini" @click="dialogVisible = true">物品整理</el-button>
          <el-button style="
              border-color: #aed8ff;
              background: #e8f4ff;
              color: #3fa1ff;
              position: absolute;
              right: 0;
            " type="primary" size="mini" @click="reset">返回至主目录</el-button>
        </div>
        <el-table :data="tableData" style="width: 100%; margin-top: 16px; margin-left: 4px">
          <el-table-column align="center" prop="date" width="55">
            <template slot="header" slot-scope="scope">
              <img v-if="multipleSelection.length == 0" @click="selectionChange(scope.row, 'allact')" class="selsct"
                :src="require('@/assets/images/omo_none.png')" alt="" />
              <img v-show="tableData.length > 0 && allType" @click="selectionChange(scope.row, 'alldel')" class="selsct"
                :src="require('@/assets/images/omo_act.png')" alt="" />
              <img v-show="multipleSelection.length > 0 && !allType" @click="selectionChange(scope.row, 'allact')"
                class="selsct" :src="require('@/assets/images/omo_show.png')" alt="" />
            </template>
            <template slot-scope="scope">
              <img v-show="!scope.row.acttype" @click="selectionChange(scope.row, 'act')" class="selsct"
                :src="require('@/assets/images/omo_none.png')" alt="" />
              <img v-show="scope.row.acttype" @click="selectionChange(scope.row, 'del')" class="selsct"
                :src="require('@/assets/images/omo_act.png')" alt="" />
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip="" label="序号" width="50">
            <template slot-scope="scope">
              {{ scope.$index + 1 }}
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip="" prop="itemName" label="物品名称" width="180">
            <template #default="{ row }">
              <div style="color: rgb(63, 161, 255); cursor: pointer" @click="toDetail(row)">
                {{ row.itemName }}
              </div>
            </template>
          </el-table-column>

          <el-table-column align="center" show-overflow-tooltip="" prop="categoryName" label="所属类别" />

          <el-table-column align="center" show-overflow-tooltip="" prop="companyShortName" label="所属公司" />
          <el-table-column align="center" label="启用状态" width="120">
            <template #default="{ row }">
              <el-switch v-model="row.status" active-value="0" inactive-value="1" @change="handleStatusChange(row)" />
            </template>
          </el-table-column>
          <el-table-column align="center" show-overflow-tooltip="" prop="amount" label="数量" />
          <el-table-column align="center" show-overflow-tooltip="" prop="measureUnit" label="计量单位" />
          <el-table-column align="center" show-overflow-tooltip="" prop="updateTime" label="最新修改时间" />
          <el-table-column align="center" show-overflow-tooltip="" prop="createTime" label="创建时间" />
          <el-table-column align="center" fixed="right" width="170" label="操作">
            <template #default="{ row }">
              <el-button @click="toDetail(row)" type="text" size="small">查看详情</el-button>
              <el-dropdown>
                <el-button type="text" size="small">>> 更多</el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click.native="edit(row)" v-hasPermi="['maintenance:addEdit']">
                    <span style="color: #3fa1ff">修改</span>
                  </el-dropdown-item>
                  <el-dropdown-item @click.native="showChangeRecords(row)">
                    <span style="color: #3fa1ff">查看修改记录</span>
                  </el-dropdown-item>
                  <el-dropdown-item v-hasPermi="['maintenance:del']" @click.native="del(row)">
                    <span style="color: red">删除</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </template>
          </el-table-column>
        </el-table>
        <pagination v-show="total > 0" :total="total" :page.sync="params.pageNum" :page-sizes="[10, 20, 50, 100]"
          :limit.sync="params.pageSize" @pagination="getList" />
      </div>
    </div>
    <ChangeRecordsDialog :itemData="itemData" v-if="changeRecordsVisible" @close="changeRecordsVisible = false" />
    <el-dialog title="物品整理" :visible.sync="dialogVisible" width="650px" :before-close="handleClose">
      <el-tree class="filter-tree" :data="leftTreeList" :props="defaultProps" :default-expand-all="false"
        :filter-node-method="filterNode" @node-click="handleNodeClick2" ref="tree" node-key="id"
        :highlight-current="true">
      </el-tree>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitChange" :disabled="!categoryId">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Cookies from "js-cookie";

import {
  supplyMainList,
  categoryTree,
  getAuthCompany,
  supplySettle,
  supplyMainDel,
  changeStatusSupply
} from "@/api/officeSupplies/officeSupplies";

import ChangeRecordsDialog from "./components/ChangeRecordsDialog";

export default {
  name: "Maintenance",
  components: {
    ChangeRecordsDialog,
  },
  data() {
    return {
      dialogVisible: false,
      changeRecordsVisible: false,
      defaultProps2: {
        children: "children",
        label: "label",
      },
      deTreeList: [],

      disabledView: false,
      editData: null,
      treeSelect: [],
      addItemType: false,
      defaultProps: {
        children: "children",
        label: "categoryName",
      },
      projects: [],
      leftTreeList: [],
      multipleSelection: [],
      allType: false,
      selectItemType: false,
      total: 0,
      params: {
        itemName: "",
        categoryId: "",
        companyId: "",
        status: "",
        pageNum: 1,
        pageSize: 10,
      },
      tableData: [],
      selectList: [],

      filterText: "",
      itemData: null,
      categoryId: "",
    };
  },
  watch: {
    multipleSelection(newval, oldval) {
      if (newval.length == 0) {
        this.allType = false;
      }
    },
    tableData(newval, oldval) {
      var flag = newval.every((item) => {
        return item.acttype;
      });
      this.allType = flag ? true : false;
    },
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  created() { },

  activated() {
    this.$refs.tree.setCurrentKey(this.params.id);
  },
  mounted() {
    this.getTreeList();
    this.getList();
  },
  methods: {
    handleStatusChange(row) {
      console.log(row.status);

      changeStatusSupply({
        id: row.id,
        status: row.status,
        offSupplyHistory: {
          oldJson: JSON.stringify(row),
          newJson: JSON.stringify({ ...row, status: row.status == 1 ? 0 : 1 }),
          updateRemark: '',
        },
      }).then(res => {
        this.$message.success('状态更新成功');
        this.getList(); // 刷新列表保持数据同步
      }).catch(() => {
        // 操作失败时恢复原状态
        row.status = row.status == 1 ? 0 : 1;
      });
    },
    submitChange() {
      supplySettle({
        ids: this.multipleSelection.map((item) => item.id),
        categoryId: this.categoryId,
      }).then((res) => {
        this.$message.success("操作成功");
        this.multipleSelection = [];
        this.dialogVisible = false;
        this.getList();
      });
    },
    handleClose() {
      this.dialogVisible = false;
    },
    toDetail(value) {
      this.$router.push({
        path: "/officeSuppliesOther/maintenanceDetail",
        query: { id: value.id },
      });
    },
    exportExcel() {
      this.download(
        "/offSupplyMain/supplyMain/export",
        {
          ...this.params,
        },
        `办公用品类别_${new Date().getTime()}.xlsx`
      );
    },

    newData() {
      this.$router.push({
        path: "/officeSuppliesOther/addMaintenance",
        query: {
          categoryId: this.params.categoryId,
          categoryName: this.params.categoryName,
        }
      });
    },
    addItem(e) {
      console.log(e, 1);
      if (e.id) {
        const params = { ...e };
        delete params.createBy;
        updateLicenseCatalogueCatalogue(params).then((res) => {
          if (res.code == 200) {
            this.$message.success("修改成功");
            this.addItemType = false;
            this.getList();
            this.getTreeList();
          }
        });
      } else {
        addLicenseCatalogueCatalogue({ ...e }).then((res) => {
          if (res.code == 200) {
            this.$message.success("新建成功");
            this.addItemType = false;
            this.getList();
            this.getTreeList();
          }
        });
      }
    },
    edit(value) {
      sessionStorage.setItem("updateMaintenance",'修改物品'); // 将对象转换为字符串并存储到 sessionStorage 中
      this.$router.push({
        path: "/officeSuppliesOther/addMaintenance",
        query: { id: value.id }

      });
    },
    del(value) {
      const ids = value.sysCode;
      this.$confirm("此操作将永久删除该物品, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          supplyMainDel(ids).then((res) => {
            if (res.code == 200) {
              this.$message.success("操作成功");

              this.getList();
              this.multipleSelection = [];
            }
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },

    search() {
      this.params.pageNum = 1;
      this.getList();
    },
    reset() {
      this.params = {
        itemName: "",
        categoryId: "",
        companyId: "",
        status: "",

        pageNum: 1,
        pageSize: 10,
      };
      this.getList();
    },

    dehandleNodeClick(data) {
      console.log(data);
      this.params.pertainDeptName = data.label;
      this.params.deptId = data.id;
      this.$refs.selectUpResId.blur();
    },
    handleNodeClick(data) {
      console.log(data);
      this.params.categoryId = data.id;
      this.params.categoryName = data.categoryName;

      this.getList();
    },
    handleNodeClick2(data) {
      console.log(data);
      this.categoryId = data.id;
    },
    getTreeList() {
      categoryTree().then((res) => {
        this.leftTreeList = res.data;
      });
      getAuthCompany().then((res) => {
        // this.treeSelect = res.data.dept;
        this.projects = res.rows
      });
    },
    getList() {
      let params = {
        ...this.params, qieryFlag: '0'
      };
      supplyMainList({ ...params }).then((res) => {
        if (res.code == 200) {
          this.tableData = res.rows;
          this.tableData.forEach((item, index) => {
            item.acttype = false;
            item.xh = (this.params.pageNum - 1) * 10 + index + 1;
          });
          this.total = res.total;
          if (this.multipleSelection.length > 0) {
            this.getArrEqual(this.tableData, this.multipleSelection);
          }
        }
      });
    },
    getArrEqual(arr1, arr2) {
      for (let i = 0; i < arr2.length; i++) {
        for (let j = 0; j < arr1.length; j++) {
          if (arr1[j].id === arr2[i].id) {
            arr1[j].acttype = true;
          }
        }
      }
    },
    selectionChange(v, type) {
      var list = [...this.tableData];
      switch (type) {
        case "act":
          list.map((val, idx) => {
            if (val.id == v.id) {
              val.acttype = true;
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          this.multipleSelection.push(v);
          break;
        case "del":
          list.map((val, idx) => {
            if (val.id == v.id) {
              delete val.acttype;
            }
          });
          this.multipleSelection.map((val, idx) => {
            if (val.id == v.id) {
              this.multipleSelection.splice(idx, 1);
            }
          });
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "alldel":
          list.map((val, idx) => {
            delete val.acttype;
          });
          let arr = [...this.multipleSelection];
          for (let i = 0; i < arr.length; i++) {
            for (let j = 0; j < list.length; j++) {
              if (arr[i].id == list[j].id) {
                arr.splice(i, 1);
              }
            }
          }
          this.multipleSelection = [...arr];
          this.tableData = [];
          this.tableData = JSON.parse(JSON.stringify(list));
          break;
        case "allact":
          var flag = list.every((item) => {
            return item.acttype;
          });
          if (flag) {
            this.allType = true;
            list.map((val, idx) => {
              delete val.acttype;
            });
            let arr = [...this.multipleSelection];
            for (let i = 0; i < arr.length; i++) {
              for (let j = 0; j < list.length; j++) {
                if (arr[i].id == list[j].id) {
                  arr.splice(i, 1);
                }
              }
            }
            this.multipleSelection = [...arr];
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          } else {
            let datalist = [...list, ...this.multipleSelection];
            let obj = {};
            let peon = datalist.reduce((cur, next) => {
              obj[next.id] ? "" : (obj[next.id] = true && cur.push(next));
              return cur;
            }, []); //设置cur默认类型为数组，并且初始值为空的数组

            this.multipleSelection = [...peon];
            list.map((val, idx) => {
              val.acttype = true;
              // this.multipleSelection.push(val);
            });
            this.tableData = [];
            this.tableData = JSON.parse(JSON.stringify(list));
          }

          break;
      }
      console.log(this.multipleSelection, "---");
    },

    filterNode(value, data) {
      if (!value) return true;
      return data.categoryName.indexOf(value) !== -1;
    },
    showChangeRecords(row) {
      this.itemData = row;
      this.changeRecordsVisible = true;
    },
  },
};
</script>

<style lang="less" scoped>
.content {
  display: flex;
  width: 100%;
  padding: 16px;

  .left {
    width: 280px;
    height: 650px;
    overflow-y: auto;
    border: 1px solid #ccc;
    flex-shrink: 0;
    padding: 16px;
    box-sizing: border-box;
  }

  .right {
    width: calc(100% - 280px);
    padding-left: 12px;

    .el-button {
      height: 32px;
    }
  }
}

.search {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  flex-wrap: wrap;

  .item {
    display: flex;
    align-items: center;
    margin-right: 16px;

    span {
      margin-right: 9px;
    }
  }
}

.el-button {
  height: 36px;
  margin-left: 4px;
  margin-right: 12px;
}

.solid {
  width: 100%;
  height: 1px;
  background: #f2f2f2;
  margin-top: 12px;
}

.selsct {
  width: 14px;
  cursor: pointer;
}
</style>