<template>
  <div class="m-auto">
    <el-button
      @click="cancel"
      v-show="(!Object.keys(formPropProcess).length)&&showClose"
      class="m-4"
      >关 闭</el-button
    >
    <el-image
      ref="previewImg"
      v-show="false"
      :src="photoUrl"
      :preview-src-list="imagePreviewUrls"
    />
    <MyForm
      v-model="myForm"
      :columns="formColumns"
      formType="false"
      labelWidth="120px"
      ref="form"
      class="p-10"
    >
      <template #organizationalUser>
        <el-form-item
          label="会议组织人:"
          prop="organizationalUser"
          style="margin-bottom: 20px"
        >
          <div
            style="
              border: 1px solid rgba(215, 215, 215, 1);
              bacnground-color: rgba(255, 255, 255, 1);
              border-radius: 5px;
            "
            class="px-2"
          >
            {{ myForm.organizationalUserName }}
          </div>
        </el-form-item>
      </template>
      <template #organizationalDept>
        <el-form-item
          label="组织部门:"
          prop="organizationalDept"
          style="margin-bottom: 20px"
        >
          <div class="flex">
            <div
              v-for="(item, index) in myForm.depts"
              :key="index"
              style="
                border: 1px solid rgba(215, 215, 215, 1);
                bacnground-color: rgba(255, 255, 255, 1);
                border-radius: 5px;
              "
              class="px-2 mr-2"
            >
              {{ item.deptName }}({{ item.companyShortName }})
            </div>
          </div>
        </el-form-item>
      </template>
      <template #attendUser>
        <el-form-item
          label="参会人员:"
          prop="attendUser"
          style="margin-bottom: 20px"
        >
          <div
            v-for="(item, index) in myForm.attendUserList"
            :key="index"
            style="
              border: 1px solid rgba(215, 215, 215, 1);
              bacnground-color: rgba(255, 255, 255, 1);
              border-radius: 5px;
              display: inline-block;
            "
            class="px-2 mr-2"
          >
            {{ item.nickName }}
          </div>
        </el-form-item>
      </template>
      <template #fileIds>
        <el-form-item
          prop="fileIds"
          label="会议材料:"
          style="margin-bottom: 20px"
        >
          <div v-for="(item, index) in myForm.files" :key="index">
            {{ item.fileName }}
            <el-button type="text" @click="handleDownload(item)" class="ml-2"
              >下载</el-button
            >
            <el-button
              v-show="
                item.fileName.endsWith('.pdf') ||
                item.fileName.endsWith('.jpg') ||
                item.fileName.endsWith('.png') ||
                item.fileName.endsWith('.gif') ||
                item.fileName.endsWith('.jpeg')
              "
              type="text"
              @click="handlePreview(item)"
              >查看</el-button
            >
          </div>
        </el-form-item>
      </template>
    </MyForm>
    <!-- <InBody>
      <div
        class="flex justify-center fixed bottom-0 bg-white z-10 w-full pb-2"
        style="left: 130px"
      >

        <el-button @click="cancel" v-show="!(Object.keys(formPropProcess).length)">取 消</el-button>
      </div>
    </InBody> -->
  </div>

  <!-- <CommonDetail type="view" :myForm="myForm"  class="w-2/3 m-auto"/> -->
</template>

<script>
import { meetingDetail } from "@/api/meeting/staging";
// import CommonDetail from "@/views/meeting/components/commonDetail.vue";
import { clone } from "xe-utils";

import privew from "@/mixin/privew";
export default {
  name: "MeetingDetails",
  mixins: [privew],
  // components: { CommonDetail },
  props: {
    showClose:{
      type:Boolean,
      default:true
    },
    formPropProcess: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      attendWay: Object.freeze({
        1: "录像",
        2: "录音",
        3: "不需要录音/录像",
      }),
      notificationMap: Object.freeze({
        1: "系统代办通知",
        2: "企业微信通知",
      }),
      formColumns: Object.freeze([
        {
          label: "会议主题:",
          prop: "meetingTheme",
          type: "divText",
          span: 10,
        },
        {
          label: "会议编号:",
          prop: "meetingCode",
          type: "divText",
          span: 14,
        },
        {
          label: "会议时间:",
          prop: "meetingTime",
          type: "divText",
          span: 10,
        },
        {
          label: "会议时长:",
          prop: "meetingDuration",
          type: "divText",
          span: 14,
        },
        {
          type: "slot",
          slotName: "organizationalUser",
        },
        {
          type: "slot",
          slotName: "organizationalDept",
          span: 24,
        },
        {
          label: "会议室:",
          prop: "meetingRoomName",
          type: "divText",
        },
        {
          label: "参会方式:",
          prop: "attendWayLabel",
          type: "divText",
          span: 24,
        },
        {
          type: "slot",
          slotName: "attendUser",
          span: 24,
        },
        {
          label: "外部参会人员:",
          prop: "externalAttendUser",
          type: "divText",
          span: 24,
        },
        {
          label: "会议提醒方式:",
          prop: "meetingReminderWayLabel",
          type: "divText",
          span: 24,
        },
        {
          label: "会议提醒时间:",
          prop: "isInstantlyReminderLabel",
          type: "divText",
          span: 24,
        },
        {
          type: "slot",
          span: 24,
          slotName: "fileIds",
        },
        {
          label: "会议内容:",
          prop: "meetingContent",
          type: "divText",
          span: 24,
        },
        {
          label: "备注:",
          prop: "remark",
          type: "divText",

          span: 24,
        },
      ]),
      myForm: {},
    };
  },
  watch: {},
  computed: {},
  mounted() {
    this.getFormProp();
  },
  methods: {
    async getFormProp() {
      let tempData = {};
      if (Object.keys(this.formPropProcess).length) {
        tempData = clone(this.formPropProcess, true);
      } else {
        const { data } = await meetingDetail(this.$route.params.id);
        tempData = data;
      }
      tempData.meetingTime = `${tempData.meetingStartTime} 至 ${tempData.meetingEndTime}`;
      tempData.meetingDuration = `${tempData.meetingDuration} 分钟`;
      tempData.attendWayLabel = this.attendWay[tempData.attendWay];
      tempData.meetingReminderWayLabel = tempData.meetingReminderWayIds
        .map((item) => this.notificationMap[item])
        .join();
      if (
        tempData.isInstantlyReminder == "true" ||
        tempData.isInstantlyReminder == true
      ) {
        tempData.isInstantlyReminderLabel = "会议提交后立刻提醒";
      } else {
        tempData.isInstantlyReminderLabel = `会议开始前${tempData.meetingReminderTimeHour}小时${tempData.meetingReminderTimeMinute}分钟进行提醒`;
      }
      this.myForm = tempData;
    },
    cancel() {
      const obj = { path: this.$route.path };
      this.$tab.closePage(obj);
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="less" scoped>
</style>

