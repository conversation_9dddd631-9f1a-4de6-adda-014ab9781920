import request from "@/utils/request";

export function getMonthLogMainList(params) {
  return request({
    url: "/month/log/main/getMonthLogMainList",
    method: "get",
    params,
  });
}
export function getMonthLogMainByUserId(params) {
  return request({
    url: "/month/log/main/getMonthLogMainByUserId",
    method: "get",
    params,
  });
}

export function logmMin(data) {
  return request({
    url: '/month/log/main',
    method: 'put',
    data: data
  })
}
export function logNotify(data) {
  return request({
    url: '/month/log/notify',
    method: 'post',
    data: data
  })
}