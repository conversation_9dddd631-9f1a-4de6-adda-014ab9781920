import request from '@/utils/request'

// 查询【请填写功能名称】列表
export function listDeploy(data) {
  return request({
    url: '/oasystem/deploy/list',
    method: 'post',
    data
  })
}
export function getReceiptAndPaymentInfo(id) {
  return request({
    url: '/oasystem/deploy/getReceiptAndPaymentInfo/' + id,
    method: 'get',

  })
}
export function getCwProjectFeeInfo(id) {
  return request({
    url: '/oasystem/deploy/getCwProjectFeeInfo/' + id,
    method: 'get',

  })
}
export function getItemInfo() {
  return request({
    url: '/oasystem/deploy/getItemInfo',
    method: 'get',

  })
}
export function querySelectList(params) {
  return request({
    url: '/oasystem/deploy/querySelectList',
    method: 'get',
    params
  })
}
export function getDataByTemplName(params) {
  return request({
    url: '/oaSystem/processTemplate/getDataByTemplName',
    method: 'get',
    params
  })
}
export function getDataIsReference(params) {
  return request({
    url: '/oasystem/deploy/getDataIsReference',
    method: 'get',
    params
  })
}
export function getCompanyIsReference(params) {
  return request({
    url: '/system/company/getCompanyIsReference',
    method: 'get',
    params
  })
}




export function queryData(data) {
  return request({
    url: '/oasystem/deploy/isRepeat',
    method: 'post',
    data
  })
}
export function updateprincipal(data) {
  return request({
    url: '/xmgl/project/updateprincipal',
    method: 'post',
    data: data
  })
}
// 查询【请填写功能名称】详细
export function getDeploy(id) {
  return request({
    url: '/oasystem/deploy/' + id,
    method: 'get'
  })
}
export function deleteTemporarily(query) {
  return request({
    url: '/xmgl/project/deleteTemporarily',
    method: 'get',
    params: query
  })
}
export function queryTemporarily(params) {
  return request({
    url: '/system/relation/queryTemporarily',
    method: 'get',
    params
  })
}
export function getXmglProcessFlow(params) {
  return request({
    url: '/system/relation/getXmglProcessFlow',
    method: 'get',
    params
  })
}
export function queryProcessCompany(params) {
  return request({
    url: '/system/relation/queryProcessCompany',
    method: 'get',
    params
  })
}
export function isRepeat(params) {
  return request({
    url: '/xmgl/project/isRepeat',
    method: 'get',
    params
  })
}
export function relationAdd(data) {
  return request({
    url: '/system/relation/add',
    method: 'post',
    data: data
  })
}
export function lxProjectAdd(data) {
  return request({
    url: '/xmgl/project',
    method: 'post',
    data: data
  })
}
export function postponeAudit(data) {
  return request({
    url: '/xmgl/project/postponeAudit',
    method: 'post',
    data: data
  })
}
export function terminateProject(data) {
  return request({
    url: '/xmgl/project/terminateProject',
    method: 'post',
    data: data
  })
}
export function updateProjectAndUser(data) {
  return request({
    url: '/xmgl/project/updateProjectAndUser',
    method: 'post',
    data: data
  })
}
export function claimProject(data) {
  return request({
    url: '/xmgl/project/claimProject',
    method: 'post',
    data: data
  })
}
export function dynamicList(data) {
  return request({
    url: '/system/dynamic/list',
    method: 'post',
    data: data
  })
}
// 新增【请填写功能名称】
export function addDeploy(data) {
  return request({
    url: '/oasystem/deploy',
    method: 'post',
    data: data
  })
}

// 修改【请填写功能名称】
export function updateDeploy(data) {
  return request({
    url: '/oasystem/deploy',
    method: 'put',
    data: data
  })
}

export function addNewEditInfo(data) {
  return request({
    url: '/oasystem/deploy/addNewEditInfo',
    method: 'post',
    data
  })
}

export function startProd(data) {
  return request({
    url: '/businessData/record/startProd',
    method: 'post',
    data
  })
}
export function getParamDataOfCheckConfig(params) {
  return request({
    url: "/businessData/record/getParamDataOfCheckConfig",
    method: "get",
    params,
  });
}
export function getParamDataOfAchievementEnter(params) {
  return request({
    url: "/businessData/record/getParamDataOfAchievementEnter",
    method: "get",
    params,
  });
}
export function abandonedProd(data) {
  return request({
    url: '/businessData/record/abandonedProd',
    method: 'post',
    data
  })
}
export function collecPayAbandonedProd(data) {
  return request({
    url: '/oasystem/trader/abandonedProd',
    method: 'post',
    data
  })
}

export function getAssignDeptUserList(data) {
  return request({
    url: '/system/user/getAssignDeptUserList',
    method: 'post',
    data
  })
}
export function overdProd(data) {
  return request({
    url: '/businessData/record/overdProd',
    method: 'post',
    data
  })
}
// 删除【请填写功能名称】
export function delDeploy(id) {
  return request({
    url: '/oasystem/deploy/' + id,
    method: 'delete'
  })
}
// 用户状态修改
export function changeenableStatus(id, isEnable) {
  const data = {
    id,
    isEnable
  }
  return request({
    url: '/oasystem/deploy/changeStatus',
    method: 'put',
    data: data
  })
}
export function getListByType(query) {
  return request({
    url: '/oasystem/deploy/getListByType',
    method: 'get',
    params: query
  })
}

