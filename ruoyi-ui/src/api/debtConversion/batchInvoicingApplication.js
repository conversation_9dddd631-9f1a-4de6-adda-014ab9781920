import request from "@/utils/request";
// 查询用户列表
export function applicationFileList(params) {
  return request({
    url: "/invoicing/application/file/list",
    method: "get",
    params,
  });
}

// 查询用户详细
export function deleteFileList(id) {
  return request({
    url: "/invoicing/application/file/" + id,
    method: "delete",
  });
}

// 新增用户
export function applicationFileImportDataCheck(data) {
  return request({
    url: "/invoicing/application/file/importDataCheck",
    method: "post",
    data,
  });
}
export function applicationFileImportData(data) {
  return request({
    url: "/invoicing/application/file/importData",
    method: "post",
    data,
  });
}

export function applicationList(params) {
  return request({
    url: "/invoicing/application/list",
    method: "get",
    params,
  });
}

export function applicationImplement(params) {
  return request({
    url: "/invoicing/application/implement",
    method: "get",
    params,
  });
}

export function invoicingBusinessBatch(data) {
  return request({
    url: "/invoicing/business/invoicingBusinessBatch",
    method: "post",
    data,
  });
}
