import request from "@/utils/request";

export function listPlan(params) {
  return request({
    url: "/annual/plan/list",
    method: "get",
    params,
  });
}
export function exportPlan(params) {
  return request({
    url: "/annual/plan/export",
    method: "get",
    params,
  });
}

export function annualPlan(data) {
  return request({
    url: "/annual/plan",
    method: "post",
    data,
  });
}
export function annualPlanUpdate(data) {
  return request({
    url: "/annual/plan",
    method: "put",
    data,
  });
}
export function getAnnualPlanVo(params) {
  return request({
    url: "/annual/plan/getAnnualPlanVo",
    method: "get",
    params,
  });
}

export function delAnnualPlan(configId) {
  return request({
    url: "/annual/plan/" + configId,
    method: "delete",
  });
}
export function getAnnualPlanFlow(params) {
  return request({
    url: "/informationFlowController/getAnnualPlanFlow",
    method: "get",
    params,
  });
}
export function selectAnnualPlanProcessList(params) {
  return request({
    url: "/annual/plan/selectAnnualPlanProcessList",
    method: "get",
    params,
  });
}
export function insertAnnualPlanProcess(data) {
  return request({
    url: "/annual/plan/insertAnnualPlanProcess",
    method: "post",
    data,
  });
}
export function unpassAnnualPlanProcess(data) {
  return request({
    url: "/annual/plan/unpassAnnualPlanProcess",
    method: "put",
    data,
  });
}
export function passAnnualPlanProcess(data) {
  return request({
    url: "/annual/plan/passAnnualPlanProcess",
    method: "put",
    data,
  });
}
