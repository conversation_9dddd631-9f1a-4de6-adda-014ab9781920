<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysInfoEditRecordMapper">

    <resultMap type="SysInfoEditRecord" id="SysInfoEditRecordResult">
        <result property="id"    column="id"    />
        <result property="logQueryId"    column="log_query_id"    />
        <result property="oldInfo"    column="old_info"    />
        <result property="newInfo"    column="new_info"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysInfoEditRecordVo">
        select id, log_query_id, old_info, new_info, create_by, create_time, update_by, update_time from sys_info_edit_record
    </sql>

    <select id="selectSysInfoEditRecordList" parameterType="SysInfoEditRecord" resultMap="SysInfoEditRecordResult">
        <include refid="selectSysInfoEditRecordVo"/>
        <where>
            <if test="logQueryId != null "> and log_query_id = #{logQueryId}</if>
            <if test="oldInfo != null  and oldInfo != ''"> and old_info = #{oldInfo}</if>
            <if test="newInfo != null  and newInfo != ''"> and new_info = #{newInfo}</if>
        </where>
    </select>

    <select id="selectSysInfoEditRecordById" parameterType="Long" resultMap="SysInfoEditRecordResult">
        <include refid="selectSysInfoEditRecordVo"/>
        where id = #{id}
    </select>

    <select id="getEditRecord" parameterType="Long" resultMap="SysInfoEditRecordResult">
        <include refid="selectSysInfoEditRecordVo"/>
        where log_query_id = #{logQueryId}
    </select>

    <insert id="insertSysInfoEditRecord" parameterType="SysInfoEditRecord" useGeneratedKeys="true" keyProperty="id">
        insert into sys_info_edit_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="logQueryId != null">log_query_id,</if>
            <if test="oldInfo != null">old_info,</if>
            <if test="newInfo != null">new_info,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="logQueryId != null">#{logQueryId},</if>
            <if test="oldInfo != null">#{oldInfo},</if>
            <if test="newInfo != null">#{newInfo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysInfoEditRecord" parameterType="SysInfoEditRecord">
        update sys_info_edit_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="logQueryId != null">log_query_id = #{logQueryId},</if>
            <if test="oldInfo != null">old_info = #{oldInfo},</if>
            <if test="newInfo != null">new_info = #{newInfo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysInfoEditRecordById" parameterType="Long">
        delete from sys_info_edit_record where id = #{id}
    </delete>

    <delete id="deleteSysInfoEditRecordByIds" parameterType="String">
        delete from sys_info_edit_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
