package com.ruoyi.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.ruoyi.common.constant.HttpStatus;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.exception.file.InvalidExtensionException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PageUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.domain.vo.*;
import com.ruoyi.system.mapper.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.ruoyi.system.service.IGdxxWorkOrderService;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.oss.OSSUtil;
import com.ruoyi.common.core.domain.AjaxResult;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class GdxxWorkOrderServiceImpl implements IGdxxWorkOrderService {

    public static final SimpleDateFormat FORMAT = new SimpleDateFormat("yyyyy-MM-dd");
    private static final Logger log = LoggerFactory.getLogger(GdxxWorkOrderServiceImpl.class);
    @Autowired
    private GdxxWorkOrderMapper workOrderMapper;
    @Autowired
    private GdxxWorkOrderPersonnelMapper personnelMapper;
    @Autowired
    private GdxxWorkOrderFileMapper fileMapper;
    @Autowired
    private GdxxWorkOrderDynamicMapper dynamicMapper;
    @Autowired
    private GdxxWorkOrderHistoryMapper historyMapper;
    @Autowired
    private SysUserMapper sysUserMapper;
    @Autowired
    private SysDeptMapper sysDeptMapper;

    @Override
    public GdxxWorkOrderDetailVO getWorkOrderDetail(Long id) {
        log.info("获取工单详情, id: {}", id);
        GdxxWorkOrder workOrder = workOrderMapper.selectGdxxWorkOrderById(id);
        if (workOrder == null) {
            log.warn("工单不存在, id: {}", id);
            return null;
        }
        GdxxWorkOrderDetailVO vo = new GdxxWorkOrderDetailVO();
        BeanUtils.copyProperties(workOrder, vo);

        // 查询人员表
        GdxxWorkOrderPersonnel personnelQuery = new GdxxWorkOrderPersonnel();
        personnelQuery.setSourceFirstCategory("ORDER_TABLE_ZHUBIAO");
        personnelQuery.setSourceTableId(String.valueOf(id));
        personnelQuery.setStatus("0");
        List<GdxxWorkOrderPersonnel> personnelList = personnelMapper.selectGdxxWorkOrderPersonnelList(personnelQuery);
        vo.setPersonnelList(personnelList);

        // 查询文件表
        GdxxWorkOrderFile fileQuery = new GdxxWorkOrderFile();
        fileQuery.setSourceCategory("ORDER_TABLE_ZHUBIAO");
        fileQuery.setSourceTableId(String.valueOf(id));
        fileQuery.setStatus("0");
        List<GdxxWorkOrderFile> fileList = fileMapper.selectGdxxWorkOrderFileList(fileQuery);
        vo.setFileList(fileList);
        // 执行名称赋值
        handleNameInfo(vo, personnelList);
        return vo;
    }


    @Override
    public GdxxWorkOrderHistoryDetailVO getWorkOrderHistoryDetail(Long id) {
        log.info("获取历史工单详情, id: {}", id);
        GdxxWorkOrderHistory workOrder = historyMapper.selectGdxxWorkOrderHistoryById(id);
        if (workOrder == null) {
            log.warn("历史工单不存在, id: {}", id);
            return null;
        }
        GdxxWorkOrderHistoryDetailVO vo = new GdxxWorkOrderHistoryDetailVO();
        BeanUtils.copyProperties(workOrder, vo);

        // 查询人员表
        GdxxWorkOrderPersonnel personnelQuery = new GdxxWorkOrderPersonnel();
        personnelQuery.setSourceFirstCategory("ORDER_TABLE_LISHI");
        personnelQuery.setSourceTableId(String.valueOf(id));
        personnelQuery.setStatus("0");
        List<GdxxWorkOrderPersonnel> personnelList = personnelMapper.selectGdxxWorkOrderPersonnelList(personnelQuery);
        vo.setPersonnelList(personnelList);

        // 查询文件表
        GdxxWorkOrderFile fileQuery = new GdxxWorkOrderFile();
        fileQuery.setSourceCategory("ORDER_TABLE_LISHI");
        fileQuery.setSourceTableId(String.valueOf(id));
        fileQuery.setStatus("0");
        List<GdxxWorkOrderFile> fileList = fileMapper.selectGdxxWorkOrderFileList(fileQuery);
        vo.setFileList(fileList);
        // 执行名称赋值
        handleNameInfoHistory(vo, personnelList);
        return vo;
    }


    @Override
    public List<GdxxWorkOrderDetailVO> getWorkOrderDetailList(GdxxWorkOrder query) {
        List<GdxxWorkOrder> workOrders = workOrderMapper.selectGdxxWorkOrderList(query);

        List<GdxxWorkOrderDetailVO> voList = new ArrayList<>();

        for (GdxxWorkOrder workOrder : workOrders) {
            GdxxWorkOrderDetailVO vo = new GdxxWorkOrderDetailVO();
            BeanUtils.copyProperties(workOrder, vo);

            // 查询人员表
            GdxxWorkOrderPersonnel personnelQuery = new GdxxWorkOrderPersonnel();
                personnelQuery.setSourceFirstCategory("ORDER_TABLE_ZHUBIAO");
            personnelQuery.setSourceTableId(String.valueOf(workOrder.getId()));
            personnelQuery.setStatus("0");
            List<GdxxWorkOrderPersonnel> personnelList = personnelMapper.selectGdxxWorkOrderPersonnelList(personnelQuery);
            vo.setPersonnelList(personnelList);

            // 查询文件表
            GdxxWorkOrderFile fileQuery = new GdxxWorkOrderFile();
            fileQuery.setSourceCategory("ORDER_TABLE_ZHUBIAO");
            fileQuery.setSourceTableId(String.valueOf(workOrder.getId()));
            fileQuery.setStatus("0");
            List<GdxxWorkOrderFile> fileList = fileMapper.selectGdxxWorkOrderFileList(fileQuery);
            vo.setFileList(fileList);
            // 执行名称赋值
            handleNameInfo(vo, personnelList);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public List<GdxxWorkOrderDetailVO> getWorkOrderDetailList(Set<Long> query,boolean pageFlag) {
        if(pageFlag){
        PageUtils.startPage();
        }
        List<GdxxWorkOrder> workOrders = workOrderMapper.selectGdxxWorkOrderListByIds(query);
        if(pageFlag){
            PageUtils.clearPage();
        }

        List<GdxxWorkOrderDetailVO> voList = new ArrayList<>();

        for (GdxxWorkOrder workOrder : workOrders) {
            GdxxWorkOrderDetailVO vo = new GdxxWorkOrderDetailVO();
            BeanUtils.copyProperties(workOrder, vo);

            // 查询人员表
            GdxxWorkOrderPersonnel personnelQuery = new GdxxWorkOrderPersonnel();
            personnelQuery.setSourceFirstCategory("ORDER_TABLE_ZHUBIAO");
            personnelQuery.setSourceTableId(String.valueOf(workOrder.getId()));
            personnelQuery.setStatus("0");
            List<GdxxWorkOrderPersonnel> personnelList = personnelMapper.selectGdxxWorkOrderPersonnelList(personnelQuery);
            vo.setPersonnelList(personnelList);

            // 查询文件表
            GdxxWorkOrderFile fileQuery = new GdxxWorkOrderFile();
            fileQuery.setSourceCategory("ORDER_TABLE_ZHUBIAO");
            fileQuery.setSourceTableId(String.valueOf(workOrder.getId()));
            fileQuery.setStatus("0");
            List<GdxxWorkOrderFile> fileList = fileMapper.selectGdxxWorkOrderFileList(fileQuery);
            vo.setFileList(fileList);
            // 执行名称赋值
            handleNameInfo(vo, personnelList);
            voList.add(vo);
        }
        return voList;
    }

    @Override
    @Transactional
    public AjaxResult createWorkOrder(GdxxWorkOrderCreateVO vo, MultipartFile[] files) {
        log.info("开始创建工单, 工单信息: {}", JSON.toJSONString(vo));
        try {
            // 获取当前登录用户ID
            Long userId = SecurityUtils.getUserId();
            String username = SecurityUtils.getUsername();
            Date now = new Date();

            if (files != null && files.length > 0) {
                log.info("创建工单上传文件数量: {}, 文件大小: {} bytes", files.length,
                    Arrays.stream(files).mapToLong(MultipartFile::getSize).sum());
            }

            // 创建新工单
            Long newWorkOrderId = handleNewProjectInfo(vo, userId, now, username, files);
            log.info("创建工单主表成功, newWorkOrderId: {}", newWorkOrderId);

            if (!vo.getWorkOrderStatus().equals("ORDER_STATUS_CAOGAO")) {
                // 创建历史工单
                Long historyWorkOrderId = handleHistoryProjectInfo(vo, userId, now, username, newWorkOrderId, files);
                log.info("创建工单历史表成功, historyWorkOrderId: {}", historyWorkOrderId);

                // 发布信息
                handleDynamicInfo(newWorkOrderId, historyWorkOrderId, now, username);
                log.info("工单状态非草稿状态进行状态发布");
            }
            return AjaxResult.success("创建工单成功", newWorkOrderId);
        } catch (Exception e) {
            log.error("创建工单失败", e);
            return AjaxResult.error("创建工单失败：" + e.getMessage());
        }
    }


    @Override
    @Transactional
    public AjaxResult updateWorkOrder(GdxxWorkOrderUpdateVO vo, MultipartFile[] files) {
        log.info("开始修改工单, 工单信息: {}", JSON.toJSONString(vo));
        // 获取当前登录用户信息
        String username = SecurityUtils.getUsername();
        Date now = new Date();
        GdxxWorkOrder gdxxWorkOrder = workOrderMapper.selectGdxxWorkOrderById(vo.getId());

        try {
            if (files != null && files.length > 0) {
                log.info("修改工单上传文件数量: {}, 文件大小: {} bytes", files.length,
                    Arrays.stream(files).mapToLong(MultipartFile::getSize).sum());
            }

            if (gdxxWorkOrder.getWorkOrderStatus().equals("ORDER_STATUS_CAOGAO") && vo.getWorkOrderStatus().equals("ORDER_STATUS_CAOGAO")) {
                GdxxWorkOrder updateOrder = new GdxxWorkOrder();
                BeanUtils.copyProperties(vo, updateOrder);
                workOrderMapper.updateGdxxWorkOrder(updateOrder);
                log.info("修改草稿工单主表信息完成");

                int updateNum = personnelMapper.updateByFirStTypeAndFirstId(vo.getId(), "ORDER_TABLE_ZHUBIAO");
                log.info("修改人员信息条数,主表ID:{}, updateNum: {}", vo.getId(), updateNum);
                // 1. 处理部门和人员对应关系
                List<Map<String, String>> departmentPersonnelList = JSONArray.parseObject(vo.getDepartmentPersonnelListJson(), List.class);
                if (CollectionUtil.isNotEmpty(departmentPersonnelList)) {
                    for (Map<String, String> map : departmentPersonnelList) {
                        String departmentId = map.get("departmentId");
                        String personnelId = map.get("personnelId");
                        String sourceFirstCategory = map.get("sourceFirstCategory");
                        String sourceSecondCategory = map.get("sourceSecondCategory");

                        if (departmentId != null && personnelId != null && sourceFirstCategory != null) {
                            GdxxWorkOrderPersonnel personnel = new GdxxWorkOrderPersonnel();
                            personnel.setSourceFirstCategory(sourceFirstCategory);
                            personnel.setSourceSecondCategory(sourceSecondCategory);
                            personnel.setSourceTableId(String.valueOf(vo.getId()));
                            personnel.setDepartmentId(departmentId);
                            personnel.setPersonnelId(personnelId);
                            personnel.setStatus("0");
                            personnel.setCreator(username);
                            personnel.setCreationTime(now);
                            personnelMapper.insertGdxxWorkOrderPersonnel(personnel);
                        }
                    }
                }
             // 2. 处理文件上传
                // 删除非ids内的文件
                if (CollectionUtil.isNotEmpty(vo.getFileIds())) {
                    int updateFileNum = fileMapper.updateNotInFileIdsAndFirstType(vo.getId(), vo.getFileIds(), "ORDER_TABLE_ZHUBIAO");
                    log.info("修改文件信息条数,主表ID:{}, updateNum: {}", vo.getId(), updateFileNum);
                }
                if (files != null && CollectionUtil.isNotEmpty(Arrays.asList(files))) {
                    for (MultipartFile file : files) {
                        if (!file.isEmpty()) {
                            String fileName = file.getOriginalFilename();
                            // 上传文件到OSS
                            String gdxxFilePath = FileUploadUtils.uploadOSS("GDXX", file, null);
                            // 保存文件信息到数据库
                            GdxxWorkOrderFile workOrderFile = new GdxxWorkOrderFile();
                            workOrderFile.setSourceCategory("ORDER_TABLE_ZHUBIAO");
                            workOrderFile.setSourceTableId(String.valueOf(vo.getId()));
                            workOrderFile.setFileAddress(gdxxFilePath);
                            workOrderFile.setStatus("0");
                            workOrderFile.setFileName(fileName);
                            workOrderFile.setCreator(username);
                            workOrderFile.setCreationTime(now);
                            fileMapper.insertGdxxWorkOrderFile(workOrderFile);
                        }
                    }
                }
                log.info("修改草稿工单完成, workOrderId: {}", vo.getId());
                return AjaxResult.success("修改草稿工单完成。");
            } else {

                // 1. 判断当前工单是否存在
                GdxxWorkOrder oldWorkOrder = workOrderMapper.selectGdxxWorkOrderById(vo.getId());
                if (oldWorkOrder == null) {
                    return AjaxResult.error("工单不存在");
                }
                // 2. 创建新工单记录
                GdxxWorkOrder newWorkOrder = new GdxxWorkOrder();
                BeanUtils.copyProperties(vo, newWorkOrder);
                if ("ORDER_STATUS_YISHOULI".equals(vo.getWorkOrderStatus())){
                    newWorkOrder.setAcceptanceTime(DateUtils.getDate());
                }
                // 从草稿变成待受理
                if (gdxxWorkOrder.getWorkOrderStatus().equals("ORDER_STATUS_CAOGAO") && "ORDER_STATUS_DAISHOULI".equals(vo.getWorkOrderStatus())){
                    newWorkOrder.setRequirementSubmissionTime(new Date());
                }
                newWorkOrder.setModifier(username);
                newWorkOrder.setModificationTime(now);
                newWorkOrder.setStatus("0");
                newWorkOrder.setId(vo.getId());
                workOrderMapper.updateGdxxWorkOrder(newWorkOrder);
                Long newWorkOrderId = newWorkOrder.getId();


                // 处理人员情况
                int updateNum = personnelMapper.updateByFirStTypeAndFirstId(vo.getId(), "ORDER_TABLE_ZHUBIAO");
                log.info("修改人员信息条数,主表ID:{}, updateNum: {}", vo.getId(), updateNum);
                // 3. 处理部门和人员对应关系
                List<Map<String, String>> departmentPersonnelList = JSONArray.parseObject(vo.getDepartmentPersonnelListJson(), List.class);
                if (CollectionUtil.isNotEmpty(departmentPersonnelList)) {
                    for (Map<String, String> map : departmentPersonnelList) {
                        String departmentId = map.get("departmentId");
                        String personnelId = map.get("personnelId");
                        String sourceFirstCategory = map.get("sourceFirstCategory");
                        String sourceSecondCategory = map.get("sourceSecondCategory");

                        if (departmentId != null && personnelId != null && sourceFirstCategory != null) {
                            GdxxWorkOrderPersonnel personnel = new GdxxWorkOrderPersonnel();
                            personnel.setSourceFirstCategory(sourceFirstCategory);
                            personnel.setSourceSecondCategory(sourceSecondCategory);
                            personnel.setSourceTableId(String.valueOf(newWorkOrderId));
                            personnel.setDepartmentId(departmentId);
                            personnel.setPersonnelId(personnelId);
                            personnel.setStatus("0");
                            personnel.setCreator(username);
                            personnel.setCreationTime(now);
                            personnelMapper.insertGdxxWorkOrderPersonnel(personnel);
                        }
                    }
                }

                // 4. 处理文件上传
                if (CollectionUtil.isNotEmpty(vo.getFileIds())) {
                    int updateFileNum = fileMapper.updateNotInFileIdsAndFirstType(vo.getId(), vo.getFileIds(), "ORDER_TABLE_ZHUBIAO");
                    log.info("修改文件信息条数,主表ID:{}, updateNum: {}", vo.getId(), updateFileNum);
                }
                if (files != null && CollectionUtil.isNotEmpty(Arrays.asList(files))) {
                    for (MultipartFile file : files) {
                        if (!file.isEmpty()) {
                            String fileName = file.getOriginalFilename();
                            // 上传文件到OSS
                            String gdxxFilePath = FileUploadUtils.uploadOSS("GDXX", file, null);
                            // 保存文件信息到数据库
                            GdxxWorkOrderFile workOrderFile = new GdxxWorkOrderFile();
                            workOrderFile.setSourceCategory("ORDER_TABLE_ZHUBIAO");
                            workOrderFile.setSourceTableId(String.valueOf(newWorkOrderId));
                            workOrderFile.setFileAddress(gdxxFilePath);
                            workOrderFile.setStatus("0");
                            workOrderFile.setFileName(fileName);
                            workOrderFile.setCreator(username);
                            workOrderFile.setCreationTime(now);
                            fileMapper.insertGdxxWorkOrderFile(workOrderFile);
                        }
                    }
                }

                // 5. 创建历史记录
                GdxxWorkOrderHistory history = new GdxxWorkOrderHistory();
                BeanUtils.copyProperties(vo, history);
                if ("ORDER_STATUS_YISHOULI".equals(vo.getWorkOrderStatus())){
                    history.setAcceptanceTime(DateUtils.getDate());
                }
                // 从草稿变成待受理
                if (gdxxWorkOrder.getWorkOrderStatus().equals("ORDER_STATUS_CAOGAO") && "ORDER_STATUS_DAISHOULI".equals(vo.getWorkOrderStatus())){
                    history.setRequirementSubmissionTime(new Date());
                }
                history.setWorkOrderMainId(newWorkOrderId);
                history.setModifier(username);
                history.setModificationTime(now);
                history.setStatus("0");
                historyMapper.insertGdxxWorkOrderHistory(history);
                Long historyId = history.getId();

                // 6. 处理历史记录的人员信息
                List<Map<String, String>> departmentHistoryPersonnelList = JSONArray.parseObject(vo.getDepartmentPersonnelListJson(), List.class);
                if (CollectionUtil.isNotEmpty(departmentHistoryPersonnelList)) {
                    for (Map<String, String> map : departmentHistoryPersonnelList) {
                        String departmentId = map.get("departmentId");
                        String personnelId = map.get("personnelId");
                        String sourceFirstCategory = map.get("sourceFirstCategory");
                        String sourceSecondCategory = map.get("sourceSecondCategory");

                        if (departmentId != null && personnelId != null && sourceFirstCategory != null) {
                            GdxxWorkOrderPersonnel personnel = new GdxxWorkOrderPersonnel();
                            personnel.setSourceFirstCategory("ORDER_TABLE_LISHI");
                            personnel.setSourceSecondCategory(sourceSecondCategory);
                            personnel.setSourceTableId(String.valueOf(historyId));
                            personnel.setDepartmentId(departmentId);
                            personnel.setPersonnelId(personnelId);
                            personnel.setStatus("0");
                            personnel.setCreator(username);
                            personnel.setCreationTime(now);
                            personnelMapper.insertGdxxWorkOrderPersonnel(personnel);
                        }
                    }
                }

                // 7. 处理历史记录的文件信息
                //  处理文件上传
                if (CollectionUtil.isNotEmpty(vo.getFileIds())) {
                    List<GdxxWorkOrderFile> file = fileMapper.selectGdxxWorkOrderFileByIds(vo.getFileIds());
                    for (GdxxWorkOrderFile gdxxWorkOrderFile : file) {
                        gdxxWorkOrderFile.setSourceCategory("ORDER_TABLE_LISHI");
                        gdxxWorkOrderFile.setSourceTableId(String.valueOf(historyId));
                        gdxxWorkOrderFile.setStatus("0"); // 因为之前的可能设置为停用，需要重新赋值
                        fileMapper.insertGdxxWorkOrderFile(gdxxWorkOrderFile);
                    }
                }
                if (files != null && CollectionUtil.isNotEmpty(Arrays.asList(files))) {
                    for (MultipartFile file : files) {
                        if (!file.isEmpty()) {
                            String fileName = file.getOriginalFilename();
                            // 上传文件到OSS
                            String gdxxFilePath = FileUploadUtils.uploadOSS("GDXX", file, null);

                            // 保存文件信息到数据库
                            GdxxWorkOrderFile workOrderFile = new GdxxWorkOrderFile();
                            workOrderFile.setSourceCategory("ORDER_TABLE_LISHI");
                            workOrderFile.setSourceTableId(String.valueOf(historyId));
                            workOrderFile.setFileAddress(gdxxFilePath);
                            workOrderFile.setStatus("0");
                            workOrderFile.setFileName(fileName);
                            workOrderFile.setCreator(username);
                            workOrderFile.setCreationTime(now);
                            fileMapper.insertGdxxWorkOrderFile(workOrderFile);
                        }
                    }
                }

                // 8. 添加新的动态信息
                GdxxWorkOrderDynamic newDynamic = new GdxxWorkOrderDynamic();
                newDynamic.setWorkOrderMainId(newWorkOrderId);
                newDynamic.setDynamicType(vo.getDynamicType());
                newDynamic.setDynamicContent("");
                newDynamic.setWordOrderHistoryId(historyId);
                newDynamic.setStatus("0");
                newDynamic.setCreator(username);
                newDynamic.setCreationTime(now);
                dynamicMapper.insertGdxxWorkOrderDynamic(newDynamic);
                log.info("添加动态完成, newDynamic: {}", JSON.toJSONString(newDynamic));
                log.info("修改工单成功, newWorkOrderId: {}", newWorkOrderId);
                return AjaxResult.success("修改工单成功", newWorkOrderId);
            }
        } catch (Exception e) {
            log.error("修改工单失败", e);
            return AjaxResult.error("修改工单失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public AjaxResult updateWorkOrderStatusAndAddDynamic(Long workOrderId, String dynamicType) {
        log.info("开始修改工单状态, workOrderId: {}, dynamicType: {}", workOrderId, dynamicType);
        try {
            // 获取当前登录用户信息
            String username = SecurityUtils.getUsername();
            Date now = new Date();

            // 1. 修改工单状态
            GdxxWorkOrder workOrder = new GdxxWorkOrder();
            workOrder.setId(workOrderId);
            workOrder.setWorkOrderStatus(dynamicType);
            workOrder.setModifier(username);
            workOrder.setModificationTime(now);
            workOrderMapper.updateGdxxWorkOrder(workOrder);

            log.info("修改工单状态成功, workOrderId: {}", workOrderId);
            return AjaxResult.success("修改工单状态成功");
        } catch (Exception e) {
            log.error("修改工单状态失败", e);
            return AjaxResult.error("修改工单状态失败：" + e.getMessage());
        }
    }


    @Override
    public AjaxResult getWorkOrderDynamicList(Long workOrderId) {
        log.info("获取工单动态列表, workOrderId: {}", workOrderId);
        try {
            // 查询动态列表
            GdxxWorkOrderDynamic query = new GdxxWorkOrderDynamic();
            query.setWorkOrderMainId(workOrderId);
            query.setStatus("0");
            List<GdxxWorkOrderDynamic> dynamicList = dynamicMapper.selectGdxxWorkOrderDynamicList(query);

            if (CollectionUtil.isEmpty(dynamicList)) {
                log.info("查询工单动态发布记录为空, workOrderId: {}", workOrderId);
                return AjaxResult.success("查询工单动态发布记录为空。");
            }
            Set<String> collect = dynamicList.stream().map(GdxxWorkOrderDynamic::getCreator).collect(Collectors.toSet());
            List<SysUser> sysUsers = sysUserMapper.selectUserNameAndNickNameByUserNameList(collect);
            Map<String,String> userNameAndNickNameMap = new HashMap<>();
            for (SysUser sysUser : sysUsers) {
                userNameAndNickNameMap.put(sysUser.getUserName(),sysUser.getNickName());
            }
            // 转换为VO列表
            List<GdxxWorkOrderDynamicVO> voList = new ArrayList<>();
            for (GdxxWorkOrderDynamic dynamic : dynamicList) {
                GdxxWorkOrderDynamicVO vo = new GdxxWorkOrderDynamicVO();
                BeanUtils.copyProperties(dynamic, vo);
                vo.setCreatorName(userNameAndNickNameMap.get(vo.getCreator()));

                // 查询关联的文件
                GdxxWorkOrderFile fileQuery = new GdxxWorkOrderFile();
                fileQuery.setSourceCategory("ORDER_TABLE_DONGTAI");
                fileQuery.setSourceTableId(String.valueOf(dynamic.getId()));
                fileQuery.setStatus("0");
                List<GdxxWorkOrderFile> fileList = fileMapper.selectGdxxWorkOrderFileList(fileQuery);
                vo.setFileList(fileList);

                voList.add(vo);
            }

            return AjaxResult.success(voList);
        } catch (Exception e) {
            log.error("查询工单动态失败", e);
            return AjaxResult.error("查询工单动态失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional
    public AjaxResult publishDynamic(GdxxWorkOrderDynamicAddVO vo, MultipartFile[] files) {
        log.info("开始发布工单动态, 动态信息: {}", JSON.toJSONString(vo));
        try {
            // 获取当前登录用户信息
            String username = SecurityUtils.getUsername();
            Date now = new Date();

            if (files != null && files.length > 0) {
                log.info("发布工单动态上传文件数量: {}, 文件大小: {} bytes", files.length,
                    Arrays.stream(files).mapToLong(MultipartFile::getSize).sum());
            }

            // 创建动态记录
            GdxxWorkOrderDynamic dynamic = new GdxxWorkOrderDynamic();
            dynamic.setWorkOrderMainId(vo.getWorkOrderMainId());
            dynamic.setDynamicType(vo.getDynamicType());
            dynamic.setDynamicContent(vo.getDynamicContent());
            dynamic.setStatus("0");
            dynamic.setCreator(username);
            dynamic.setCreationTime(now);

            // 插入动态记录
            dynamicMapper.insertGdxxWorkOrderDynamic(dynamic);
            Long dynamicId = dynamic.getId();

            // 处理文件上传
            if (files != null &&CollectionUtil.isNotEmpty(Arrays.asList(files))) {
                for (MultipartFile file : files) {
                    if (!file.isEmpty()) {
                        String fileName = file.getOriginalFilename();
                        // 上传文件到OSS
                        String gdxxFilePath = FileUploadUtils.uploadOSS("GDXX", file, null);

                        // 保存文件信息到数据库
                        GdxxWorkOrderFile workOrderFile = new GdxxWorkOrderFile();
                        workOrderFile.setSourceCategory("ORDER_TABLE_DONGTAI");
                        workOrderFile.setSourceTableId(String.valueOf(dynamicId));
                        workOrderFile.setFileAddress(gdxxFilePath);
                        workOrderFile.setStatus("0");
                        workOrderFile.setFileName(fileName);
                        workOrderFile.setCreator(username);
                        workOrderFile.setCreationTime(now);
                        fileMapper.insertGdxxWorkOrderFile(workOrderFile);
                    }
                }
            }

            log.info("发布动态成功, dynamicId: {}", dynamicId);
            return AjaxResult.success("发布动态成功", dynamicId);
        } catch (Exception e) {
            log.error("发布动态失败", e);
            return AjaxResult.error("发布动态失败：" + e.getMessage());
        }
    }

    @Override
    public void downloadFile(Long fileId, HttpServletResponse response) {
        log.info("开始下载文件, fileId: {}", fileId);
        try {
            // 查询文件信息
            GdxxWorkOrderFile file = fileMapper.selectGdxxWorkOrderFileById(fileId);
            if (file == null) {
                log.warn("文件不存在, fileId: {}", fileId);
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write("文件不存在");
                return;
            }
            log.info("文件下载成功, fileId: {}, fileName: {}", fileId, file.getFileName());
            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    java.net.URLEncoder.encode(file.getFileName(), "UTF-8"));

            // 从OSS下载文件并写入响应流
            OSSUtil.readFile(file.getFileAddress(), response);
        } catch (Exception e) {
            log.error("下载文件失败", e);
            try {
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write("下载文件失败：" + e.getMessage());
            } catch (Exception ex) {
                log.error("写入响应失败", ex);
            }
        }
    }


    /**
     * 创建动态信息
     *
     * @param newWorkOrderId     新主表id
     * @param historyWorkOrderId 历史表id
     * @param now                现在的时间
     * @param username           用户名
     */
    private void handleDynamicInfo(Long newWorkOrderId, Long historyWorkOrderId, Date now, String username) {
        // 添加动态
        GdxxWorkOrderDynamic gdxxWorkOrderDynamic = new GdxxWorkOrderDynamic();
        gdxxWorkOrderDynamic.setWorkOrderMainId(newWorkOrderId);
        gdxxWorkOrderDynamic.setDynamicType("ORDER_DONGTAI_CHUANGJIANGONGDAN");
        gdxxWorkOrderDynamic.setDynamicContent("");
        gdxxWorkOrderDynamic.setWordOrderHistoryId(historyWorkOrderId);
        gdxxWorkOrderDynamic.setStatus("0");
        gdxxWorkOrderDynamic.setCreator(username);
        gdxxWorkOrderDynamic.setCreationTime(now);
        dynamicMapper.insertGdxxWorkOrderDynamic(gdxxWorkOrderDynamic);
    }


    /**
     * 创建主表信息
     *
     * @param vo       填充数据
     * @param userId   用户ID
     * @param now      现在的时间
     * @param username 用户
     * @return {@link Long }
     * @throws IOException
     * @throws InvalidExtensionException 文件上传误异常类
     */
    private Long handleNewProjectInfo(GdxxWorkOrderCreateVO vo, Long userId, Date now, String username, MultipartFile[] files) throws IOException, InvalidExtensionException {
        // 创建工单主表对象
        GdxxWorkOrder workOrder = new GdxxWorkOrder();
        workOrder.setWorkOrderTitle(vo.getWorkOrderTitle());
        workOrder.setWorkOrderType(vo.getWorkOrderType());
        workOrder.setRequesterId(String.valueOf(userId));
        workOrder.setRequesterDepartmentId(vo.getRequesterDepartmentId());
        workOrder.setRequirementBackground(vo.getRequirementBackground());
        workOrder.setRequirementPurpose(vo.getRequirementPurpose());
        workOrder.setRequirementDescription(vo.getRequirementDescription());
        workOrder.setRequirementSubmissionTime(vo.getWorkOrderStatus().equals("ORDER_STATUS_CAOGAO") ? null : now);
        workOrder.setRequirementPriority(vo.getRequirementPriority());
        workOrder.setExpectedCompletionDate(vo.getExpectedCompletionDate());
        workOrder.setRequirementRemark(vo.getRequirementRemark());
        workOrder.setWorkOrderStatus(vo.getWorkOrderStatus());
        workOrder.setStatus("0"); // 正常状态
        workOrder.setCreator(username);
        workOrder.setCreationTime(now);
        workOrder.setBelongSystem(vo.getBelongSystem());

        // 插入工单主表
        workOrderMapper.insertGdxxWorkOrder(workOrder);
        Long workOrderId = workOrder.getId();

        // 处理人员信息
        List<Map<String, String>> departmentPersonnelList = JSONArray.parseObject(vo.getDepartmentPersonnelListJson(), List.class);
        if (CollectionUtil.isNotEmpty(departmentPersonnelList)) {
            for (Map<String, String> map : departmentPersonnelList) {
                String departmentId = map.get("departmentId");
                String personnelId = map.get("personnelId");
                String sourceFirstCategory = map.get("sourceFirstCategory");
                String sourceSecondCategory = map.get("sourceSecondCategory");

                if (departmentId != null && personnelId != null && sourceFirstCategory != null) {
                    GdxxWorkOrderPersonnel personnel = new GdxxWorkOrderPersonnel();
                    personnel.setSourceFirstCategory(sourceFirstCategory);
                    personnel.setSourceSecondCategory(sourceSecondCategory);
                    personnel.setSourceTableId(String.valueOf(workOrderId));
                    personnel.setDepartmentId(departmentId);
                    personnel.setPersonnelId(personnelId);
                    personnel.setStatus("0");
                    personnel.setCreator(username);
                    personnel.setCreationTime(now);
                    personnelMapper.insertGdxxWorkOrderPersonnel(personnel);
                }
            }
        }

        // 处理文件上传
        if (files != null && files.length > 0 && CollectionUtil.isNotEmpty(Arrays.asList(files))) {
            for (MultipartFile file : files) {
                if (!file.isEmpty()) {
                    String fileName = file.getOriginalFilename();
                    // 上传文件到OSS
                    String gdxxFilePath = FileUploadUtils.uploadOSS("GDXX", file, null);

                    // 保存文件信息到数据库
                    GdxxWorkOrderFile workOrderFile = new GdxxWorkOrderFile();
                    workOrderFile.setSourceCategory("ORDER_TABLE_ZHUBIAO");
                    workOrderFile.setSourceTableId(String.valueOf(workOrderId));
                    workOrderFile.setFileAddress(gdxxFilePath);
                    workOrderFile.setStatus("0");
                    workOrderFile.setFileName(fileName);
                    workOrderFile.setCreator(username);
                    workOrderFile.setCreationTime(now);
                    fileMapper.insertGdxxWorkOrderFile(workOrderFile);
                }
            }
        }
        return workOrderId;
    }


    /**
     * 创建历史信息
     *
     * @param vo             填充的数据
     * @param userId         用户ID
     * @param now            现在的时间
     * @param username       用户名称
     * @param newWorkOrderId 主表ID
     * @return {@link Long }
     * @throws IOException
     * @throws InvalidExtensionException 文件上传误异常类
     */
    private Long handleHistoryProjectInfo(GdxxWorkOrderCreateVO vo, Long userId, Date now, String username, Long newWorkOrderId, MultipartFile[] files) throws IOException, InvalidExtensionException {
        // 创建工单历史表对象
        GdxxWorkOrderHistory workOrder = new GdxxWorkOrderHistory();
        workOrder.setWorkOrderMainId(newWorkOrderId);
        workOrder.setWorkOrderTitle(vo.getWorkOrderTitle());
        workOrder.setWorkOrderType(vo.getWorkOrderType());
        workOrder.setRequesterId(String.valueOf(userId));
        workOrder.setRequesterDepartmentId(vo.getRequesterDepartmentId());
        workOrder.setRequirementBackground(vo.getRequirementBackground());
        workOrder.setRequirementPurpose(vo.getRequirementPurpose());
        workOrder.setRequirementDescription(vo.getRequirementDescription());
        workOrder.setRequirementSubmissionTime(now);
        workOrder.setRequirementPriority(vo.getRequirementPriority());
        workOrder.setExpectedCompletionDate(vo.getExpectedCompletionDate());
        workOrder.setRequirementRemark(vo.getRequirementRemark());
        workOrder.setWorkOrderStatus(vo.getWorkOrderStatus());
        workOrder.setStatus("0"); // 正常状态
        workOrder.setCreator(username);
        workOrder.setCreationTime(now);

        // 插入工单主表
        historyMapper.insertGdxxWorkOrderHistory(workOrder);
        Long workOrderId = workOrder.getId();

        // 处理人员信息
        List<Map<String, String>> departmentPersonnelList = JSONArray.parseObject(vo.getDepartmentPersonnelListJson(), List.class);
        if (CollectionUtil.isNotEmpty(departmentPersonnelList)) {
            for (Map<String, String> map : departmentPersonnelList) {
                String departmentId = map.get("departmentId");
                String personnelId = map.get("personnelId");
                String sourceFirstCategory = map.get("sourceFirstCategory");
                String sourceSecondCategory = map.get("sourceSecondCategory");

                if (departmentId != null && personnelId != null && sourceFirstCategory != null) {
                    GdxxWorkOrderPersonnel personnel = new GdxxWorkOrderPersonnel();
                    personnel.setSourceFirstCategory("ORDER_TABLE_LISHI");
                    personnel.setSourceSecondCategory(sourceSecondCategory);
                    personnel.setSourceTableId(String.valueOf(workOrderId));
                    personnel.setDepartmentId(departmentId);
                    personnel.setPersonnelId(personnelId);
                    personnel.setStatus("0");
                    personnel.setCreator(username);
                    personnel.setCreationTime(now);
                    personnelMapper.insertGdxxWorkOrderPersonnel(personnel);
                }
            }
        }

        // 处理文件上传
        if (files != null && files.length > 0 && CollectionUtil.isNotEmpty(Arrays.asList(files))) {
            for (MultipartFile file : files) {
                if (!file.isEmpty()) {
                    String fileName = file.getOriginalFilename();
                    // 上传文件到OSS
                    String gdxxFilePath = FileUploadUtils.uploadOSS("GDXX", file, null);

                    // 保存文件信息到数据库
                    GdxxWorkOrderFile workOrderFile = new GdxxWorkOrderFile();
                    workOrderFile.setSourceCategory("ORDER_TABLE_LISHI");
                    workOrderFile.setSourceTableId(String.valueOf(workOrderId));
                    workOrderFile.setFileAddress(gdxxFilePath);
                    workOrderFile.setStatus("0");
                    workOrderFile.setFileName(fileName);
                    workOrderFile.setCreator(username);
                    workOrderFile.setCreationTime(now);
                    fileMapper.insertGdxxWorkOrderFile(workOrderFile);
                }
            }
        }
        return workOrderId;
    }

    @Override
    public void exportWorkOrderList(GdxxWorkOrder query, HttpServletResponse response) {
        try {
            // 1. 查询工单列表
            List<GdxxWorkOrderDetailVO> workOrderList = getWorkOrderDetailList(query);

            // 2. 创建Excel工作簿
            ExcelWriter writer = ExcelUtil.getWriter();

            // 3. 设置表头
            writer.addHeaderAlias("workOrderTitle", "工单标题");
            writer.addHeaderAlias("workOrderType", "工单类型");
            writer.addHeaderAlias("requesterName", "申请人");
            writer.addHeaderAlias("requirementBackground", "需求背景");
            writer.addHeaderAlias("requirementPurpose", "需求目的");
            writer.addHeaderAlias("requirementDescription", "需求描述");
            writer.addHeaderAlias("requirementSubmissionTime", "提交时间");
            writer.addHeaderAlias("requirementPriority", "优先级");
            writer.addHeaderAlias("expectedCompletionDate", "预期完成日期");
            writer.addHeaderAlias("requirementRemark", "备注");
            writer.addHeaderAlias("workOrderStatus", "工单状态");
            writer.addHeaderAlias("currentExecutorName", "当前执行人");
            writer.addHeaderAlias("creatorName", "创建人");
            writer.addHeaderAlias("creationTime", "创建时间");

            // 4. 写入数据
            writer.write(workOrderList, true);

            // 5. 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8");
            String fileName = URLEncoder.encode("工单列表", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            // 6. 输出到浏览器
            writer.flush(response.getOutputStream(), true);
            writer.close();
        } catch (Exception e) {
            try {
                response.setContentType("text/html;charset=utf-8");
                response.getWriter().write("导出工单列表失败：" + e.getMessage());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    @Override
    public Set<Long> getWorkOrderIds(GdxxWorkOrder query) {
        // 不包含草稿的当前人所涉及的数据
        return workOrderMapper.getWorkOrderIds(query.getWorkOrderTitle(), query.getRequesterId(), query.getCurrentExecutor(), query.getWorkOrderStatus(), query.getRndProgress(), query.getRequirementPriority()
                , query.getRequirementSubmissionTime(), SecurityUtils.getUserId(), query.getIsMeSubmmitFlag(), query.getIsMeJoinFlag(), query.getIsDoingFlag(), query.getIsDaiShouLiFlag(),query.getCompleteTime()
                , query.getBelongSystemList(),query.getRndProgressList());

    }

    @Override
    public Set<Long> getWorkOrderIdsInCaoGao(GdxxWorkOrder query) {
        // 不包含草稿的当前人所涉及的数据
        return workOrderMapper.getWorkOrderIdsInCaoGao(query.getWorkOrderTitle(), query.getRequesterId(), query.getCurrentExecutor(), query.getWorkOrderStatus(), query.getRndProgress(), query.getRequirementPriority()
                , query.getRequirementSubmissionTime(), SecurityUtils.getUserId(), query.getIsMeSubmmitFlag(), query.getIsMeJoinFlag(), query.getIsDoingFlag(), query.getIsDaiShouLiFlag(),query.getCompleteTime()
                , query.getBelongSystemList(),query.getRndProgressList());

    }

    @Override
    public List<GdxxWorkOrderExport> getExportInfo(GdxxWorkOrderQueryVo gdxxWorkOrderQueryVo) {
        GdxxWorkOrder query = new GdxxWorkOrder();
        com.ruoyi.common.utils.bean.BeanUtils.copyProperties(gdxxWorkOrderQueryVo, query);
        if (gdxxWorkOrderQueryVo != null && StringUtils.isNotEmpty(gdxxWorkOrderQueryVo.getRequirementSubmissionTime())) {
            try {
                query.setRequirementSubmissionTime(FORMAT.parse(gdxxWorkOrderQueryVo.getRequirementSubmissionTime()));
            } catch (ParseException e) {
                log.error("需求时间传输格式有误。", JSON.toJSONString(gdxxWorkOrderQueryVo));
                throw new RuntimeException("需求时间传输格式有误。");
            }
        }
        Set<Long> orderIdsNotCaoGao = getWorkOrderIds(query);
        Set<Long> orderIdsInCaoGao = getWorkOrderIdsInCaoGao(query);
        orderIdsNotCaoGao.addAll(orderIdsInCaoGao);
        if (CollectionUtil.isEmpty(orderIdsNotCaoGao)) {
            return new ArrayList<>();
        }
        List<GdxxWorkOrderDetailVO> workOrderDetailList = getWorkOrderDetailList(orderIdsNotCaoGao,false);
        List<GdxxWorkOrderExport> gdxxWorkOrderExports = new ArrayList<>();
        for (GdxxWorkOrderDetailVO gdxxWorkOrderDetailVO : workOrderDetailList) {
            GdxxWorkOrderExport gdxxWorkOrderExport = new GdxxWorkOrderExport();
            BeanUtils.copyProperties(gdxxWorkOrderDetailVO,gdxxWorkOrderExport);
            List<GdxxWorkOrderPersonnel> personnelList = gdxxWorkOrderDetailVO.getPersonnelList();
            if (CollectionUtil.isNotEmpty(personnelList)){
                for (GdxxWorkOrderPersonnel gdxxWorkOrderPersonnel : personnelList) {
                    // 产品经理
                    if (gdxxWorkOrderPersonnel.getSourceSecondCategory().equals("ORDER_RENYUAN_CHANPINJINGLI")) {
                        List<String> chanPinJingLiName = gdxxWorkOrderExport.getChanPinJingLiName();
                        if (CollectionUtil.isEmpty(chanPinJingLiName)){
                            chanPinJingLiName = new ArrayList<>();
                        }
                        if (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) || StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName())) {
                            chanPinJingLiName.add((StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) ? gdxxWorkOrderPersonnel.getDepartmentName() : "")
                                    + " - " + (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName()) ? gdxxWorkOrderPersonnel.getPersonnelName() : ""));
                        }
                        gdxxWorkOrderExport.setChanPinJingLiName(chanPinJingLiName);
                    }else if (gdxxWorkOrderPersonnel.getSourceSecondCategory().equals("ORDER_RENYUAN_XIANGMUJINGLI")){
                        // 项目经理
                        List<String> xiangMuJingLiName = gdxxWorkOrderExport.getXiangMuJingLiName();
                        if (CollectionUtil.isEmpty(xiangMuJingLiName)){
                            xiangMuJingLiName = new ArrayList<>();
                        }
                        if (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) || StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName())) {
                            xiangMuJingLiName.add((StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) ? gdxxWorkOrderPersonnel.getDepartmentName() : "")
                                    + " - " + (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName()) ? gdxxWorkOrderPersonnel.getPersonnelName() : ""));
                        }
                        gdxxWorkOrderExport.setXiangMuJingLiName(xiangMuJingLiName);
                    }else if (gdxxWorkOrderPersonnel.getSourceSecondCategory().equals("ORDER_RENYUAN_KAIFARENYUAN")){
                        // 开发人员
                        List<String> kaiFaRenYuanName = gdxxWorkOrderExport.getKaiFaRenYuanName();
                        if (CollectionUtil.isEmpty(kaiFaRenYuanName)){
                            kaiFaRenYuanName = new ArrayList<>();
                        }
                        if (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) || StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName())) {
                            kaiFaRenYuanName.add((StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) ? gdxxWorkOrderPersonnel.getDepartmentName() : "")
                                    + " - " + (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName()) ? gdxxWorkOrderPersonnel.getPersonnelName() : ""));
                        }
                        gdxxWorkOrderExport.setKaiFaRenYuanName(kaiFaRenYuanName);
                    }else if (gdxxWorkOrderPersonnel.getSourceSecondCategory().equals("ORDER_RENYUAN_CESHIRENYUAN")){
                        // 测试人员
                        List<String> ceShiRenYuanName = gdxxWorkOrderExport.getCeShiRenYuanName();
                        if (CollectionUtil.isEmpty(ceShiRenYuanName)){
                            ceShiRenYuanName = new ArrayList<>();
                        }
                        if (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) || StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName())){
                            ceShiRenYuanName.add((StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) ? gdxxWorkOrderPersonnel.getDepartmentName() : "")
                                + " - " + (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName()) ? gdxxWorkOrderPersonnel.getPersonnelName() : ""));
                        }
                        gdxxWorkOrderExport.setCeShiRenYuanName(ceShiRenYuanName);
                    }else if (gdxxWorkOrderPersonnel.getSourceSecondCategory().equals("ORDER_RENYUAN_BUMENXUQIUFUZEREN")){
                        // 部门需求人
                        List<String> buMenXuQiuRenName = gdxxWorkOrderExport.getBuMenXuQiuRenName();
                        if (CollectionUtil.isEmpty(buMenXuQiuRenName)){
                            buMenXuQiuRenName = new ArrayList<>();
                        }
                        if (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) || StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName())) {
                            buMenXuQiuRenName.add((StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) ? gdxxWorkOrderPersonnel.getDepartmentName() : "")
                                    + " - " + (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName()) ? gdxxWorkOrderPersonnel.getPersonnelName() : ""));
                        }
                        gdxxWorkOrderExport.setBuMenXuQiuRenName(buMenXuQiuRenName);

                    }else if (gdxxWorkOrderPersonnel.getSourceSecondCategory().equals("ORDER_RENYUAN_ZHIXINGREN")){
                        // 执行人
                        List<String> zhiXingRenName = gdxxWorkOrderExport.getZhiXingRenName();
                        if (CollectionUtil.isEmpty(zhiXingRenName)){
                            zhiXingRenName = new ArrayList<>();
                        }
                        if (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) || StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName())) {
                            zhiXingRenName.add((StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentName()) ? gdxxWorkOrderPersonnel.getDepartmentName() : "")
                                    + " - " + (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelName()) ? gdxxWorkOrderPersonnel.getPersonnelName() : ""));
                        }
                        gdxxWorkOrderExport.setZhiXingRenName(zhiXingRenName);

                    }
                }
            }
            gdxxWorkOrderExport.setChanPinJingLiName(CollectionUtil.isEmpty(gdxxWorkOrderExport.getChanPinJingLiName())?null:gdxxWorkOrderExport.getChanPinJingLiName());
            gdxxWorkOrderExport.setXiangMuJingLiName(CollectionUtil.isEmpty(gdxxWorkOrderExport.getXiangMuJingLiName())?null:gdxxWorkOrderExport.getXiangMuJingLiName());
            gdxxWorkOrderExport.setKaiFaRenYuanName(CollectionUtil.isEmpty(gdxxWorkOrderExport.getKaiFaRenYuanName())?null:gdxxWorkOrderExport.getKaiFaRenYuanName());
            gdxxWorkOrderExport.setCeShiRenYuanName(CollectionUtil.isEmpty(gdxxWorkOrderExport.getCeShiRenYuanName())?null:gdxxWorkOrderExport.getCeShiRenYuanName());
            gdxxWorkOrderExport.setBuMenXuQiuRenName(CollectionUtil.isEmpty(gdxxWorkOrderExport.getBuMenXuQiuRenName())?null:gdxxWorkOrderExport.getBuMenXuQiuRenName());
            gdxxWorkOrderExports.add(gdxxWorkOrderExport);
        }

        return gdxxWorkOrderExports;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String importWorkOrder(List<GdxxWorkOrderImportVO> workOrderList) {
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();

        for (GdxxWorkOrderImportVO workOrder : workOrderList) {
            try {
                // 创建工单
                AjaxResult result = importWorkOrderData(workOrder);
                if ((int) result.get("code") == HttpStatus.SUCCESS) {
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、工单 " + workOrder.getWorkOrderTitle() + " 导入成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、工单 " + workOrder.getWorkOrderTitle() + " 导入失败：" + result.get("msg"));
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、工单 " + workOrder.getWorkOrderTitle() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }

        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 导入工单数据
     *
     * @param workOrder 工单信息
     * @return 导入
     */
    private AjaxResult importWorkOrderData(GdxxWorkOrderImportVO workOrder) {
        GdxxWorkOrder gdxxWorkOrder = new GdxxWorkOrder();
        gdxxWorkOrder.setWorkOrderTitle(workOrder.getWorkOrderTitle());
        gdxxWorkOrder.setWorkOrderType(workOrder.getWorkOrderType());
        gdxxWorkOrder.setRequesterId(workOrder.getRequesterId());
        gdxxWorkOrder.setRequesterDepartmentId(workOrder.getRequesterDepartmentId());
        gdxxWorkOrder.setRequirementBackground(workOrder.getRequirementBackground());
        gdxxWorkOrder.setRequirementPurpose(workOrder.getRequirementPurpose());
        gdxxWorkOrder.setRequirementDescription(workOrder.getRequirementDescription());
        gdxxWorkOrder.setRequirementSubmissionTime(StringUtils.isNotEmpty(workOrder.getRequirementSubmissionTime())?DateUtils.parse(workOrder.getRequirementSubmissionTime(), DateUtils.DateFormat.YYYY_MM_DD_HHMMSS):null);
        gdxxWorkOrder.setAcceptanceTime(StringUtils.isNotEmpty(workOrder.getAcceptanceTime())?workOrder.getAcceptanceTime():null);
        gdxxWorkOrder.setRequirementPriority(workOrder.getRequirementPriority());
        gdxxWorkOrder.setExpectedCompletionDate(workOrder.getExpectedCompletionDate());
        gdxxWorkOrder.setRequirementRemark(workOrder.getRequirementRemark());
        gdxxWorkOrder.setWorkOrderStatus(workOrder.getWorkOrderStatus());
        gdxxWorkOrder.setRndProgress(workOrder.getRndProgress());
        gdxxWorkOrder.setRequirementImplementationSystem(workOrder.getRequirementImplementationSystem());
        gdxxWorkOrder.setSystemFunctionModule(workOrder.getSystemFunctionModule());
        gdxxWorkOrder.setProjectRisk(workOrder.getProjectRisk());
        gdxxWorkOrder.setExternalStakeholderInfo(workOrder.getExternalStakeholderInfo());
        gdxxWorkOrder.setRequirementScheduleStartDate(workOrder.getRequirementScheduleStartDate());
        gdxxWorkOrder.setRequirementScheduleEndDate(workOrder.getRequirementScheduleEndDate());
        gdxxWorkOrder.setDesignScheduleStartDate(workOrder.getDesignScheduleStartDate());
        gdxxWorkOrder.setDesignScheduleEndDate(workOrder.getDesignScheduleEndDate());
        gdxxWorkOrder.setDevelopmentScheduleStartDate(workOrder.getDevelopmentScheduleStartDate());
        gdxxWorkOrder.setDevelopmentScheduleEndDate(workOrder.getDevelopmentScheduleEndDate());
        gdxxWorkOrder.setTestingScheduleStartDate(workOrder.getTestingScheduleStartDate());
        gdxxWorkOrder.setTestingScheduleEndDate(workOrder.getTestingScheduleEndDate());
        gdxxWorkOrder.setAcceptanceTestingScheduleStartDate(workOrder.getAcceptanceTestingScheduleStartDate());
        gdxxWorkOrder.setAcceptanceTestingScheduleEndDate(workOrder.getAcceptanceTestingScheduleEndDate());
        gdxxWorkOrder.setExpectedGoLiveDate(workOrder.getExpectedGoLiveDate());
        gdxxWorkOrder.setStatus("0");
        gdxxWorkOrder.setCreationTime(new Date());
        gdxxWorkOrder.setBelongSystem(workOrder.getBelongSystem());
        int insertMainCount = workOrderMapper.insertGdxxWorkOrder(gdxxWorkOrder); // 主表信息
        log.info("创建主表数据总共 ：{} 条", insertMainCount);

        // 历史表
        GdxxWorkOrderHistory gdxxWorkOrderHistory = new GdxxWorkOrderHistory();
        BeanUtils.copyProperties(gdxxWorkOrder, gdxxWorkOrderHistory);
        gdxxWorkOrderHistory.setId(null);
        gdxxWorkOrderHistory.setWorkOrderMainId(gdxxWorkOrder.getId());
        historyMapper.insertGdxxWorkOrderHistory(gdxxWorkOrderHistory);
        log.info("创建历史表数据总共 ：{} 条", insertMainCount);

        // 需求负责人
        List<Map<String, String>> requirementStakeholders = workOrder.getRequirementStakeholdersList();
        if (CollectionUtil.isNotEmpty(requirementStakeholders)) {
            for (Map<String, String> map : requirementStakeholders) {
                GdxxWorkOrderPersonnel workOrderPersonnel = new GdxxWorkOrderPersonnel();
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_ZHUBIAO");
                workOrderPersonnel.setSourceSecondCategory("ORDER_RENYUAN_BUMENXUQIUFUZEREN");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrder.getId()));
                workOrderPersonnel.setDepartmentId(map.get("deptId"));
                workOrderPersonnel.setPersonnelId(map.get("userId"));
                workOrderPersonnel.setStatus("0");
                workOrderPersonnel.setCreationTime(new Date());
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_LISHI");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrderHistory.getId()));
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
            }
        }

        // 当前执行人
        List<Map<String, String>> currentExecutors = workOrder.getCurrentExecutorList();
        if (CollectionUtil.isNotEmpty(currentExecutors)) {
            for (Map<String, String> map : currentExecutors) {
                GdxxWorkOrderPersonnel workOrderPersonnel = new GdxxWorkOrderPersonnel();
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_ZHUBIAO");
                workOrderPersonnel.setSourceSecondCategory("ORDER_RENYUAN_ZHIXINGREN");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrder.getId()));
                workOrderPersonnel.setDepartmentId(map.get("deptId"));
                workOrderPersonnel.setPersonnelId(map.get("userId"));
                workOrderPersonnel.setStatus("0");
                workOrderPersonnel.setCreationTime(new Date());
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_LISHI");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrderHistory.getId()));
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
            }
        }

        // 产品经理
        List<Map<String, String>> productManagers = workOrder.getProductManagerList();
        if (CollectionUtil.isNotEmpty(productManagers)) {
            for (Map<String, String> map : productManagers) {
                GdxxWorkOrderPersonnel workOrderPersonnel = new GdxxWorkOrderPersonnel();
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_ZHUBIAO");
                workOrderPersonnel.setSourceSecondCategory("ORDER_RENYUAN_CHANPINJINGLI");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrder.getId()));
                workOrderPersonnel.setDepartmentId(map.get("deptId"));
                workOrderPersonnel.setPersonnelId(map.get("userId"));
                workOrderPersonnel.setStatus("0");
                workOrderPersonnel.setCreationTime(new Date());
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_LISHI");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrderHistory.getId()));
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
            }
        }

        // 项目经理
        List<Map<String, String>> projectManagers = workOrder.getProjectManagerList();
        if (CollectionUtil.isNotEmpty(projectManagers)) {
            for (Map<String, String> map : projectManagers) {
                GdxxWorkOrderPersonnel workOrderPersonnel = new GdxxWorkOrderPersonnel();
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_ZHUBIAO");
                workOrderPersonnel.setSourceSecondCategory("ORDER_RENYUAN_XIANGMUJINGLI");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrder.getId()));
                workOrderPersonnel.setDepartmentId(map.get("deptId"));
                workOrderPersonnel.setPersonnelId(map.get("userId"));
                workOrderPersonnel.setStatus("0");
                workOrderPersonnel.setCreationTime(new Date());
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_LISHI");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrderHistory.getId()));
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
            }
        }

        // 开发人员
        List<Map<String, String>> developers = workOrder.getDeveloperList();
        if (CollectionUtil.isNotEmpty(developers)) {
            for (Map<String, String> map : developers) {
                GdxxWorkOrderPersonnel workOrderPersonnel = new GdxxWorkOrderPersonnel();
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_ZHUBIAO");
                workOrderPersonnel.setSourceSecondCategory("ORDER_RENYUAN_KAIFARENYUAN");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrder.getId()));
                workOrderPersonnel.setDepartmentId(map.get("deptId"));
                workOrderPersonnel.setPersonnelId(map.get("userId"));
                workOrderPersonnel.setStatus("0");
                workOrderPersonnel.setCreationTime(new Date());
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_LISHI");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrderHistory.getId()));
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
            }
        }

        // 测试人员
        List<Map<String, String>> testers = workOrder.getTesterList();
        if (CollectionUtil.isNotEmpty(testers)) {
            for (Map<String, String> map : testers) {
                GdxxWorkOrderPersonnel workOrderPersonnel = new GdxxWorkOrderPersonnel();
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_ZHUBIAO");
                workOrderPersonnel.setSourceSecondCategory("ORDER_RENYUAN_CESHIRENYUAN");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrder.getId()));
                workOrderPersonnel.setDepartmentId(map.get("deptId"));
                workOrderPersonnel.setPersonnelId(map.get("userId"));
                workOrderPersonnel.setStatus("0");
                workOrderPersonnel.setCreationTime(new Date());
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
                workOrderPersonnel.setSourceFirstCategory("ORDER_TABLE_LISHI");
                workOrderPersonnel.setSourceTableId(String.valueOf(gdxxWorkOrderHistory.getId()));
                personnelMapper.insertGdxxWorkOrderPersonnel(workOrderPersonnel);
            }
        }


        GdxxWorkOrderDynamic gdxxWorkOrderDynamic = new GdxxWorkOrderDynamic();
        gdxxWorkOrderDynamic.setWorkOrderMainId(gdxxWorkOrder.getId());
        gdxxWorkOrderDynamic.setDynamicType("ORDER_DONGTAI_CHUANGJIANGONGDAN");
        gdxxWorkOrderDynamic.setDynamicContent("");
        gdxxWorkOrderDynamic.setWordOrderHistoryId(gdxxWorkOrderHistory.getId());
        gdxxWorkOrderDynamic.setStatus("0");
        gdxxWorkOrderDynamic.setCreator(SecurityUtils.getLoginUser().getUsername());
        gdxxWorkOrderDynamic.setCreationTime(new Date());
        dynamicMapper.insertGdxxWorkOrderDynamic(gdxxWorkOrderDynamic);
        log.info("创建动态新增工单数据，gdxxWorkOrderDynamic:{}",JSON.toJSONString(gdxxWorkOrderDynamic));

        return AjaxResult.success();
    }

    /**
     * 执行主表名称赋值
     *
     * @param vo            主表VO
     * @param personnelList 人员列表
     */
    private void handleNameInfo(GdxxWorkOrderDetailVO vo, List<GdxxWorkOrderPersonnel> personnelList) {
        Set<String> userIdSet = new HashSet<>();
        Set<String> userNameSet = new HashSet<>();
        Set<Long> departmentIds = new HashSet<>();
        userIdSet.add(vo.getRequesterId());// 需求提出人ID
//        userIdSet.add(vo.getCurrentExecutor());// 当前执行人ID

        departmentIds.add(Long.valueOf(vo.getRequesterDepartmentId())); //需求提出人部门id
        for (GdxxWorkOrderPersonnel gdxxWorkOrderPersonnel : personnelList) {
            if (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelId())) {
                userIdSet.add(String.valueOf(gdxxWorkOrderPersonnel.getPersonnelId()));
            }
            if (gdxxWorkOrderPersonnel.getDepartmentId() != null && StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentId())) {
                departmentIds.add(Long.valueOf(gdxxWorkOrderPersonnel.getDepartmentId()));
            }


        }
        if (CollectionUtil.isNotEmpty(userIdSet)) {
            List<SysUser> userIdAndNickNameMap
                    = sysUserMapper.selectUserIdAndNickNameByUserIdList(userIdSet);
            if (CollectionUtil.isNotEmpty(userIdAndNickNameMap)) {
                Map<String, String> userIdMap = new HashMap<>();
                for (SysUser sysUser : userIdAndNickNameMap) {
                    userIdMap.put(String.valueOf(sysUser.getUserId()), sysUser.getNickName());
                }


                vo.setRequesterName(userIdMap.get(vo.getRequesterId()));
//                vo.setCurrentExecutorName(userIdMap.get(vo.getCurrentExecutor()));
                for (GdxxWorkOrderPersonnel gdxxWorkOrderPersonnel : vo.getPersonnelList()) {
                    gdxxWorkOrderPersonnel.setPersonnelName(userIdMap.get(gdxxWorkOrderPersonnel.getPersonnelId()));
                }
            }
        }


        if (CollectionUtil.isNotEmpty(departmentIds)) {
            List<SysDept> sysDepts = sysDeptMapper.queryDeptInfoByIdsStr(departmentIds);
            Map<String, String> departmentIdsMap = new HashMap<>();
            for (SysDept sysDept : sysDepts) {
                departmentIdsMap.put(String.valueOf(sysDept.getDeptId()), sysDept.getDeptName());
            }
            vo.setRequesterDepartmentName(departmentIdsMap.get(vo.getRequesterDepartmentId()));
            if (CollectionUtil.isNotEmpty(departmentIdsMap)) {
                for (GdxxWorkOrderPersonnel gdxxWorkOrderPersonnel : vo.getPersonnelList()) {
                    gdxxWorkOrderPersonnel.setDepartmentName(departmentIdsMap.get(gdxxWorkOrderPersonnel.getDepartmentId()));
                }
            }
        }

        userNameSet.add(vo.getCreator());
        userNameSet.add(vo.getModifier());
        List<SysUser> userNameAndNickNameMap
                = sysUserMapper.selectUserNameAndNickNameByUserNameList(userNameSet);
        if (CollectionUtil.isNotEmpty(userNameAndNickNameMap)) {
            Map<String, String> userNameMap = new HashMap<>();
            for (SysUser sysUser : userNameAndNickNameMap) {
                userNameMap.put(String.valueOf(sysUser.getUserName()), sysUser.getNickName());
            }
            vo.setCreatorName(userNameMap.get(vo.getCreator()));
            vo.setModifierName(userNameMap.get(vo.getModifier()));
        }
    }

    /**
     * 执行历史名称赋值
     *
     * @param vo            历史
     * @param personnelList 人员列表
     */
    private void handleNameInfoHistory(GdxxWorkOrderHistoryDetailVO vo, List<GdxxWorkOrderPersonnel> personnelList) {
        Set<String> userIdSet = new HashSet<>();
        Set<String> userNameSet = new HashSet<>();
        Set<Long> departmentIds = new HashSet<>();

        userIdSet.add(vo.getRequesterId());// 需求提出人ID
//        userIdSet.add(vo.getCurrentExecutor());// 当前执行人ID
        for (GdxxWorkOrderPersonnel gdxxWorkOrderPersonnel : personnelList) {
            if (StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getPersonnelId())) {
                userIdSet.add(String.valueOf(gdxxWorkOrderPersonnel.getPersonnelId()));
            }

            if (gdxxWorkOrderPersonnel.getDepartmentId() != null && StringUtils.isNotEmpty(gdxxWorkOrderPersonnel.getDepartmentId())) {
                departmentIds.add(Long.valueOf(gdxxWorkOrderPersonnel.getDepartmentId()));
            }
        }
        if (vo != null && vo.getRequesterDepartmentId() != null && StringUtils.isNotEmpty(vo.getRequesterDepartmentId())) {
            departmentIds.add(Long.valueOf(vo.getRequesterDepartmentId()));
        }
        if (CollectionUtil.isNotEmpty(userIdSet)) {
            List<SysUser> userIdAndNickNameMap
                    = sysUserMapper.selectUserIdAndNickNameByUserIdList(userIdSet);
            if (CollectionUtil.isNotEmpty(userIdAndNickNameMap)) {
                Map<String, String> userIdMap = new HashMap<>();
                for (SysUser sysUser : userIdAndNickNameMap) {
                    userIdMap.put(String.valueOf(sysUser.getUserId()), sysUser.getNickName());
                }
                vo.setRequesterName(userIdMap.get(vo.getRequesterId()));
//                vo.setCurrentExecutorName(userIdMap.get(vo.getCurrentExecutor()));
                for (GdxxWorkOrderPersonnel gdxxWorkOrderPersonnel : vo.getPersonnelList()) {
                    gdxxWorkOrderPersonnel.setPersonnelName(userIdMap.get(gdxxWorkOrderPersonnel.getPersonnelId()));
                }
            }
        }

        if (CollectionUtil.isNotEmpty(departmentIds)) {
            List<SysDept> sysDepts = sysDeptMapper.queryDeptInfoByIdsStr(departmentIds);
            Map<String, String> departmentIdsMap = new HashMap<>();
            for (SysDept sysDept : sysDepts) {
                departmentIdsMap.put(String.valueOf(sysDept.getDeptId()), sysDept.getDeptName());
            }
            if (CollectionUtil.isNotEmpty(departmentIdsMap)) {
                for (GdxxWorkOrderPersonnel gdxxWorkOrderPersonnel : vo.getPersonnelList()) {
                    gdxxWorkOrderPersonnel.setDepartmentName(departmentIdsMap.get(gdxxWorkOrderPersonnel.getDepartmentId()));
                }
                vo.setRequesterDepartmentName(departmentIdsMap.get(vo.getRequesterDepartmentId()));
            }
        }

        userNameSet.add(vo.getCreator());
        userNameSet.add(vo.getModifier());
        List<SysUser> userNameAndNickNameMap
                = sysUserMapper.selectUserNameAndNickNameByUserNameList(userNameSet);
        if (CollectionUtil.isNotEmpty(userNameAndNickNameMap)) {
            Map<String, String> userNameMap = new HashMap<>();
            for (SysUser sysUser : userNameAndNickNameMap) {
                userNameMap.put(String.valueOf(sysUser.getUserName()), sysUser.getNickName());
            }
            vo.setCreatorName(userNameMap.get(vo.getCreator()));
            vo.setModifierName(userNameMap.get(vo.getModifier()));
        }
    }
}
