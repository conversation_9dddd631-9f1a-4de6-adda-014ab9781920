package com.ruoyi.system.service;


import com.ruoyi.system.domain.DDataSql;
import com.ruoyi.system.domain.DDataSqlManualTask;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IDDataSqlManualTaskService.java
 * @Description 手动任务
 * @createTime 2022年04月07日 15:53:00
 */

public interface IDDataSqlManualTaskService {
    /**
     * 任务列表
     * @param dDataSqlManualTask 手动触发数据查询对象
     * @return
     */
    List<DDataSqlManualTask> selectDDataSqlManualTaskList(DDataSqlManualTask dDataSqlManualTask);

    /**
     * 根据主键查询
     * @param id 手动触发数据查询id
     * @return
     */
    DDataSqlManualTask selectDDataSqlManualTaskById(Long id);

    /**
     * 插入
     * @param dDataSqlManualTask 手动触发数据查询对象
     * @return
     */
    int insertDDataSqlManualTask(DDataSqlManualTask dDataSqlManualTask);

    /**
     * 修改
     * @param dDataSqlManualTask 手动触发数据查询对象
     * @return
     */
    int updateDDataSqlManualTask(DDataSqlManualTask dDataSqlManualTask);

    /**
     * 批量删除
     * @param ids 手动触发数据id
     * @return
     */
    int deleteDDataSqlManualTaskByIds(Long[] ids);

    /**
     * 单删
     * @param id 手动触发数据id
     * @return
     */
    int deleteDDataSqlManualTaskById(Long id);

    /**
     * 查询符合条件的任务列表
     * @return
     */
    List<DDataSqlManualTask> queryParamsDataList();


    /**
     * 根据sqlcode查询对应的sql数据
     * @param sqlCode 对应sql id
     * @return
     */
    DDataSql queryTest(String sqlCode);


    /**
     * 获取对应系统的访问地址以及端口
     * @param platformNo 平台编码
     * @return
     */
    String getPlatformRemark(String platformNo);


    /**
     * 得到所有数据sql id
     *
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    List<Map<String, Object>> getAllDataSqlId();



}
