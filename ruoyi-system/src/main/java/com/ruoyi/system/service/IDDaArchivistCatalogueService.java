package com.ruoyi.system.service;

import com.ruoyi.common.core.domain.entity.SysUnit;
import com.ruoyi.system.domain.vo.DaArchivistCataloguevVO;

public interface IDDaArchivistCatalogueService {

    /**
     * 新增归档目录
     *
     * @param
     * @return 结果
     */
    public int insertDaArchivistCatalogue(SysUnit sysUnit, Long unitId);

    /**
     * 修改归档目录
     *
     * @param
     * @return 结果
     */
    public int updateDaArchivistCatalogue(SysUnit sysUnits);

    int getCountByCreateTime(String date);
}
