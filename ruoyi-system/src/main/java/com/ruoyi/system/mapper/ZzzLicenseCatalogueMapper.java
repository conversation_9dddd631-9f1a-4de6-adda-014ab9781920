package com.ruoyi.system.mapper;

import com.ruoyi.system.domain.vo.ZzzLicenseCatalogueVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 证照目录Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
@Mapper
public interface ZzzLicenseCatalogueMapper
{

    /**
     * 新增证照目录
     * 
     * @param ZzLicenseCatalogueVo 证照目录
     * @return 结果
     */
    public int insertZzLicenseCatalogue(ZzzLicenseCatalogueVo ZzLicenseCatalogueVo);

    /**
     * 修改证照目录
     * 
     * @param ZzLicenseCatalogueVo 证照目录
     * @return 结果
     */
    public int updateZzLicenseCatalogue(ZzzLicenseCatalogueVo ZzLicenseCatalogueVo);

    /**
     * 生成证照目录系统编号
     * @param createTime
     * @return
     */
    int getCountByCreateTime(String createTime);
}
