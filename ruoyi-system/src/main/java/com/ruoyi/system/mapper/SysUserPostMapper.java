package com.ruoyi.system.mapper;

import com.ruoyi.common.core.domain.entity.SysUserPost;
import com.ruoyi.system.domain.SysPost;
import com.ruoyi.system.domain.vo.SysUserPostVo;
import com.ruoyi.system.domain.vo.SysUserVo;

import java.util.List;
import java.util.Set;

/**
 * 用户与岗位关联表 数据层
 *
 * <AUTHOR>
 */
public interface SysUserPostMapper
{
    /**
     * 通过用户ID删除用户和岗位关联
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserPostByUserId(Long userId);

    /**
     * 通过岗位ID查询岗位使用数量
     *
     * @param postId 岗位ID
     * @return 结果
     */
    public int countUserPostById(Long postId);

    /**
     * 批量删除用户和岗位关联
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteUserPost(Long[] ids);

    /**
     * 批量新增用户岗位信息
     *
     * @param userPostList 用户角色列表
     * @return 结果
     */
    public int batchUserPost(List<SysUserPost> userPostList);



    /**
     * 通过岗位ID删除用户和岗位关联
     *
     * @param postId 岗位ID
     * @return 结果
     */
    public int deleteUserPostByPostId(Long postId);

    public int insertUserPost(SysUserPost userPost);

    public int deleteUserPostByPostIdUserId(SysUserPost userPost);

    public int isHomePost(SysUserPost userPost);

    public List<SysUserVo>  selectUserPostByPostId(Long[] postId);

    /**
     * 根据用户id查询岗位集合
     */
    public List<SysPost> queryPostListByUserId(Long userId);

    public List<SysUserVo> selectUserPostByUserNames(Set<String> userName);

    /**
     * 根据用户id查询岗位，只取第一条
     * @return
     */
    Long selectUserPostUserId(Long userId);

    /**
     * 查询用户有哪些岗位
     * @param userId
     * @return
     */
    List<SysUserPost> selectUserPostByUserId(Long userId);
}
