package com.ruoyi.system.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 工单信息文件表对象 gdxx_work_order_file
 */
@Data

public class GdxxWorkOrderFile extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 来源一级分类字典id
     */
    @Excel(name = "来源一级分类字典id")
    private String sourceCategory;

    /**
     * 来源表ID
     */
    @Excel(name = "来源表ID")
    private String sourceTableId;

    /**
     * 文件地址
     */
    @Excel(name = "文件地址")
    private String fileAddress;

    /**
     * 状态（0正常 1停用）
     */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /**
     * 文件名称
     */
    @Excel(name = "文件名称")
    private String fileName;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    private Date creationTime;
    /**
     * 创建人
     */
    @Excel(name = "创建人")
    private String creator;
    /**
     * 修改时间
     */
    @Excel(name = "修改时间")
    private Date modificationTime;
    /**
     * 修改人
     */
    @Excel(name = "修改人")
    private String modifier;
} 