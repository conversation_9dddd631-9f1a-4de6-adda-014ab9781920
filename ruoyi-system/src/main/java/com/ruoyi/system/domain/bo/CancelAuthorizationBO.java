package com.ruoyi.system.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 取消授权 - 前端接收信息的BO
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/4/22 14:03
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CancelAuthorizationBO {
    private static final long serialVersionUID = 1L;

    //主键  公司维度查询就是公司id，项目维度查询就是项目id
    private Long id;

    //业务维度 1-项目 2-公司   99-所有
    private String authorizedType;

    //功能模块 功能模块的标签页
    private String authorizedCode;

    //取消授权用户id
    private Long unAuthorizedUserId;

    //有权限的功能模块 选填，填了就是取消具体的某个模块，不填就是取消用户的所有权限
    private String authorizedModule;

    //取消某个具体模块的具体角色
    private String authRoleCode;

    //所有维度取消授权，权限主表id集合
    private List<Long> authIdList;

    //被代理用户的id
    private Long principalId;
}
