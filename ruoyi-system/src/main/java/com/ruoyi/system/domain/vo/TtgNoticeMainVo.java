package com.ruoyi.system.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class TtgNoticeMainVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /** 主键id */
    private Long id;

    /** 通知名称 */
    @Excel(name = "通知名称")
    private String noticeName;

    /** 通知公告类型 */
    @Excel(name = "通知公告类型")
    private Long noticeType;

    /** 发布人 */
    @Excel(name = "发布人")
    private Long publisher;

    /** 发布人姓名 */
    private String publisherNickName;

    /** 发布公司 */
    @Excel(name = "发布公司")
    private Long publishCompany;

    /** 发布公司名称 */
    private String companyShortName;

    /** 内容 */
    @Excel(name = "内容")
    private String content;

    /** 发布状态(0未发布 1已发布) */
    @Excel(name = "发布状态(0未发布 1已发布)")
    private String publishStatus;

    /** 是否置顶(0否 1是) */
    @Excel(name = "是否置顶")
    private String isHeader;

    /** 是否重点(0否 1是) */
    @Excel(name = "是否重点")
    private String isEmphasis;

    /** 删除标识(0未删除 1已删除) */
    private String delFlag;

    /** 公司id集合 */
    private List<Long> companyIdList;

    /** 创建人姓名 */
    private String createrNickName;

    /** 版本 */
    private Integer version;

    /** 分页参数 */
    private Integer pageSize;

    /** 分页参数 */
    private Integer pageNum;

    /** 登录用户id */
    private Long loginUserId;

    /** 公告类型数据集 */
    private String noticeDataName;

    /** 创建时间(条件筛选) */
    @JsonFormat(pattern = "yyyy-MM-dd",timezone="GMT+8")
    private Date sxCreateTime;

}
