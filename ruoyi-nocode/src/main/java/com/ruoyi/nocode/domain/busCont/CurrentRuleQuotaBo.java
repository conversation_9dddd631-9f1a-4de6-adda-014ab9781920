package com.ruoyi.nocode.domain.busCont;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Description：业务管控规则限额及预警信息
 * CreateTime：2024/10/25
 * Author：yu-qiang
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CurrentRuleQuotaBo implements Serializable {

    //预估导入在贷金额
    private BigDecimal predictLoanAmount;

    //项目在贷余额-导入后
    private BigDecimal quotaProjectLoanAmount;

    //项目在贷余额-限额
    private BigDecimal quotaProjectMaxAmount;

    //项目在贷余额-限额 1-是，0-否
    private int  quotaProjectOverLimit;

    //项目保证金比例-导入后
    private BigDecimal quotaBondLoanAmount;

    //项目保证金比例-限额
    private BigDecimal quotaBondLimitAmount;

    //项目保证金比例-限额 1-是，0-否
    private int quotaBondOverLimit;

    //担保公司担保余额-导入后
    private BigDecimal quotaCompanyLoanAmount;

    //担保公司担保余额-限额
    private BigDecimal quotaCompanyMaxAmount;

    //担保公司担保余额-限额 1-是，0-否
    private int  quotaCompanyOverLimit;

    //项目在贷余额-下限值
    private BigDecimal warningProjectLimitAmount;

    //项目在贷余额-上限值
    private BigDecimal warningProjectMaxAmount;

    //项目在贷余额-导入后
    private BigDecimal warningProjectLoanAmount;

    //项目在贷余额-导入后是否超限 1-是，0-否
    private int  warningProjectOverLimit;

    //项目保证金比例-下限
    private BigDecimal warningBondLimitAmount;

    //项目保证金比例-上线
    private BigDecimal warningBondMaxAmount;

    //项目保证金比例-导入后
    private BigDecimal warningBondLoanAmount;

    //项目保证金比例-导入后是否超限 1-是，0-否
    private int warningBondOverLimit;

    //担保公司担保余额-下限额
    private BigDecimal warningCompanyLimitAmount;

    //担保公司担保余额-上限额
    private BigDecimal warningCompanyMaxAmount;

    //担保公司担保余额-导入后
    private BigDecimal warningCompanyLoanAmount;

    //担保公司担保余额-导入后是否超限 1-是，0-否
    private int warningCompanyOverLimit;

}

