package com.ruoyi.nocode.domain.busCont;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Description：管控规则流程记录表
 * CreateTime：2024/11/18
 * Author：yu-qiang
 */
@Data
public class ControlRuleProcessRecord implements Serializable {
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 代表的相关功能。1项目，2产品，3公司 */
    private String applyType;

    /** 流程关联id */
    private String processId;

    /** 相关功能id */
    private List<String> applyIdList;

    private Long oaApplyId;

    /** 操作类型。0新增，1修改，2删除 */
    private String operation;

    /** 记录表修改前json */
    private String oaApplyRecordsOldData;

    /** 记录表修改后json */
    private String oaApplyRecordsNewData;

    /** 编辑人员（存用户id） */
    private Long editUserId;

    /** 编辑时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date editTime;

    /** 修改说明 */
    @Excel(name = "修改说明")
    private String editInfo;

    /** 审核人（存用户id） */
    private Long checkUserId;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date checkTime;

    /** 审核状态 0审核中，1通过，2驳回 */
    private String checkStatus;

    private String nickName;

    /** 将业务管控Id集合转换未字符串 */
    private String applyIds;
}
