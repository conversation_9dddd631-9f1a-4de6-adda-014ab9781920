<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.oasystem.mapper.SysVersionMaintainMapper">
    
    <resultMap type="SysVersionMaintain" id="SysVersionMaintainResult">
        <result property="id"    column="id"    />
        <result property="version"    column="version"    />
        <result property="msg"    column="msg"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSysVersionMaintainVo">
        select id, version, msg, create_by, create_time, update_by, update_time from sys_version_maintain
    </sql>

    <select id="selectSysVersionMaintainList" parameterType="SysVersionMaintain" resultMap="SysVersionMaintainResult">
        <include refid="selectSysVersionMaintainVo"/>
        <where>  
            <if test="version != null  and version != ''"> and version = #{version}</if>
            <if test="msg != null  and msg != ''"> and msg = #{msg}</if>
            <if test="createBy != null  and createBy != ''"> and create_by = #{createBy}</if>
            <if test="createTime != null "> and create_time = #{createTime}</if>
            <if test="updateBy != null  and updateBy != ''"> and update_by = #{updateBy}</if>
            <if test="updateTime != null "> and update_time = #{updateTime}</if>
        </where>
    </select>
    
    <select id="selectSysVersionMaintainById" parameterType="Long" resultMap="SysVersionMaintainResult">
        <include refid="selectSysVersionMaintainVo"/>
        where id = #{id}
    </select>

    <insert id="insertSysVersionMaintain" parameterType="SysVersionMaintain" useGeneratedKeys="true" keyProperty="id">
        insert into sys_version_maintain
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="version != null">version,</if>
            <if test="msg != null">msg,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="version != null">#{version},</if>
            <if test="msg != null">#{msg},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateSysVersionMaintain" parameterType="SysVersionMaintain">
        update sys_version_maintain
        <trim prefix="SET" suffixOverrides=",">
            <if test="version != null">version = #{version},</if>
            <if test="msg != null">msg = #{msg},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSysVersionMaintainById" parameterType="Long">
        delete from sys_version_maintain where id = #{id}
    </delete>

    <delete id="deleteSysVersionMaintainByIds" parameterType="String">
        delete from sys_version_maintain where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="queryVersionInfo" resultType="org.ruoyi.core.oasystem.domain.SysVersionMaintain">
        <include refid="selectSysVersionMaintainVo"/>
        order by create_time desc limit 1
    </select>

</mapper>