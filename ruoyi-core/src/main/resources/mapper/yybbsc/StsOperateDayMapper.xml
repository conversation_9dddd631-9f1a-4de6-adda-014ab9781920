<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.yybbsc.mapper.StsOperateDayMapper">
    
    <resultMap type="org.ruoyi.core.yybbsc.domain.StsOperateDay" id="StsOperateDayResult">
        <result property="id"    column="id"    />
        <result property="reconDate"    column="recon_date"    />
        <result property="productNo"    column="product_no"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="actPrintAmt"    column="act_print_amt"    />
        <result property="intAmt"    column="int_amt"    />
        <result property="ointAmt"    column="oint_amt"    />
        <result property="flAmt"    column="fl_amt"    />
        <result property="advDefineAmt"    column="adv_define_amt"    />
        <result property="deductAmt"    column="deduct_amt"    />
        <result property="reduceAmt"    column="reduce_amt"    />
        <result property="actIntAmt"    column="act_int_amt"    />
        <result property="jtFrAmt"    column="jt_fr_amt"    />
        <result property="fzAmt"    column="fz_amt"    />
        <result property="zbFrAmt"    column="zb_fr_amt"    />
        <result property="fundBalanceAmt"    column="fund_balance_amt"    />
        <result property="userBalanceAmt"    column="user_balance_amt"    />
        <result property="accumProfitAmt"    column="accum_profit_amt"    />
        <result property="compensatePrintAmt"    column="compensate_print_amt"    />
        <result property="compensateIntAmt"    column="compensate_int_amt"    />
        <result property="compensateOintAmt"    column="compensate_oint_amt"    />
        <result property="compensateTotalAmt"    column="compensate_total_amt"    />
        <result property="compensateRepayPrintAmt"    column="compensate_repay_print_amt"    />
        <result property="compensateRepayTotalAmt"    column="compensate_repay_total_amt"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
    <resultMap type="org.ruoyi.core.yybbsc.domain.dto.StsOperateDayDto" id="StsOperateDayDtoResult">
        <result property="reconDate"    column="recon_date"    />
        <result property="loanAmt"    column="loan_amt"    />
        <result property="actPrintAmt"    column="act_print_amt"    />
        <result property="actIntAmt"    column="act_int_amt"    />
        <result property="jtFrAmt"    column="jt_fr_amt"    />
        <result property="zbFrAmt"    column="zb_fr_amt"    />
        <result property="userBalanceAmt"    column="user_balance_amt"    />
    </resultMap>

    <sql id="selectStsOperateDayVo">
        select id, recon_date, product_no, loan_amt, act_print_amt, int_amt, oint_amt, fl_amt, adv_define_amt, deduct_amt, reduce_amt, act_int_amt, jt_fr_amt, fz_amt, zb_fr_amt, fund_balance_amt, user_balance_amt, accum_profit_amt, compensate_print_amt, compensate_int_amt, compensate_oint_amt, compensate_total_amt, compensate_repay_print_amt, compensate_repay_total_amt, create_time, update_time,remarks from sts_operate_day
    </sql>


    <select id="selectStsOperateDayList" resultMap="StsOperateDayResult">
        <include refid="selectStsOperateDayVo"/>
        WHERE product_no=#{productNo,jdbcType=VARCHAR}

        <if test="isAsc == 'desc'">
            ORDER BY recon_date  DESC
        </if>
        <if test="isAsc == 'acs'">
            ORDER BY recon_date  ASC
        </if>
        <if test="isAsc == '' or isAsc == null">
            ORDER BY recon_date  DESC
        </if>
    </select>

    <select id="selectStsOperateDayListByExport" resultMap="StsOperateDayResult">
        <include refid="selectStsOperateDayVo"/>
        WHERE 1=1 and product_no=#{productNo,jdbcType=VARCHAR}
        <if test="isAsc == 'desc'">
            ORDER BY recon_date  DESC
        </if>
        <if test="isAsc == 'acs'">
            ORDER BY recon_date  ASC
        </if>
        <if test="isAsc == '' or isAsc == null">
            ORDER BY recon_date  DESC
        </if>
    </select>

    <select id="selectStsOperateDayID" resultMap="StsOperateDayResult">
        <include refid="selectStsOperateDayVo"/>
        WHERE id=#{id} ORDER BY recon_date DESC
    </select>

    <select id="selectTheLastStsOperateDay" resultMap="StsOperateDayResult">
        <include refid="selectStsOperateDayVo"/>
        WHERE recon_date=#{reconDate,jdbcType=VARCHAR} AND product_no=#{productNo,jdbcType=VARCHAR}
    </select>

    <select id="selectStsOperateDayByReconDate" resultType="java.lang.Long">
        SELECT id FROM sts_operate_day WHERE recon_date=#{reconDate,jdbcType=TIMESTAMP} AND product_no=#{productNo,jdbcType=VARCHAR}
    </select>

    <insert id="insertStsOperateDay" parameterType="StsOperateDay" useGeneratedKeys="true" keyProperty="id">
        insert into sts_operate_day
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reconDate != null">recon_date,</if>
            <if test="productNo != null and productNo != ''">product_no,</if>
            <if test="loanAmt != null">loan_amt,</if>
            <if test="actPrintAmt != null">act_print_amt,</if>
            <if test="intAmt != null">int_amt,</if>
            <if test="ointAmt != null">oint_amt,</if>
            <if test="flAmt != null">fl_amt,</if>
            <if test="advDefineAmt != null">adv_define_amt,</if>
            <if test="deductAmt != null">deduct_amt,</if>
            <if test="reduceAmt != null">reduce_amt,</if>
            <if test="actIntAmt != null">act_int_amt,</if>
            <if test="jtFrAmt != null">jt_fr_amt,</if>
            <if test="fzAmt != null">fz_amt,</if>
            <if test="zbFrAmt != null">zb_fr_amt,</if>
            <if test="fundBalanceAmt != null">fund_balance_amt,</if>
            <if test="userBalanceAmt != null">user_balance_amt,</if>
            <if test="accumProfitAmt != null">accum_profit_amt,</if>
            <if test="compensatePrintAmt != null">compensate_print_amt,</if>
            <if test="compensateIntAmt != null">compensate_int_amt,</if>
            <if test="compensateOintAmt != null">compensate_oint_amt,</if>
            <if test="compensateTotalAmt != null">compensate_total_amt,</if>
            <if test="compensateRepayPrintAmt != null">compensate_repay_print_amt,</if>
            <if test="compensateRepayTotalAmt != null">compensate_repay_total_amt,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reconDate != null">#{reconDate},</if>
            <if test="productNo != null and productNo != ''">#{productNo},</if>
            <if test="loanAmt != null">#{loanAmt},</if>
            <if test="actPrintAmt != null">#{actPrintAmt},</if>
            <if test="intAmt != null">#{intAmt},</if>
            <if test="ointAmt != null">#{ointAmt},</if>
            <if test="flAmt != null">#{flAmt},</if>
            <if test="advDefineAmt != null">#{advDefineAmt},</if>
            <if test="deductAmt != null">#{deductAmt},</if>
            <if test="reduceAmt != null">#{reduceAmt},</if>
            <if test="actIntAmt != null">#{actIntAmt},</if>
            <if test="jtFrAmt != null">#{jtFrAmt},</if>
            <if test="fzAmt != null">#{fzAmt},</if>
            <if test="zbFrAmt != null">#{zbFrAmt},</if>
            <if test="fundBalanceAmt != null">#{fundBalanceAmt},</if>
            <if test="userBalanceAmt != null">#{userBalanceAmt},</if>
            <if test="accumProfitAmt != null">#{accumProfitAmt},</if>
            <if test="compensatePrintAmt != null">#{compensatePrintAmt},</if>
            <if test="compensateIntAmt != null">#{compensateIntAmt},</if>
            <if test="compensateOintAmt != null">#{compensateOintAmt},</if>
            <if test="compensateTotalAmt != null">#{compensateTotalAmt},</if>
            <if test="compensateRepayPrintAmt != null">#{compensateRepayPrintAmt},</if>
            <if test="compensateRepayTotalAmt != null">#{compensateRepayTotalAmt},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateStsOperateDayById" parameterType="StsOperateDay">
        update sts_operate_day
        <trim prefix="SET" suffixOverrides=",">
            <if test="stsOperateDay.reconDate != null">recon_date = #{stsOperateDay.reconDate,jdbcType=TIMESTAMP},</if>
            <if test="stsOperateDay.productNo != null">product_no = #{stsOperateDay.productNo},</if>
            <if test="stsOperateDay.loanAmt != null">loan_amt = #{stsOperateDay.loanAmt},</if>
            <if test="stsOperateDay.actPrintAmt != null">act_print_amt = #{stsOperateDay.actPrintAmt},</if>
            <if test="stsOperateDay.intAmt != null">int_amt = #{stsOperateDay.intAmt},</if>
            <if test="stsOperateDay.ointAmt != null">oint_amt = #{stsOperateDay.ointAmt},</if>
            <if test="stsOperateDay.flAmt != null">fl_amt = #{stsOperateDay.flAmt},</if>
            <if test="stsOperateDay.advDefineAmt != null">adv_define_amt = #{stsOperateDay.advDefineAmt},</if>
            <if test="stsOperateDay.deductAmt != null">deduct_amt = #{stsOperateDay.deductAmt},</if>
            <if test="stsOperateDay.reduceAmt != null">reduce_amt = #{stsOperateDay.reduceAmt},</if>
            <if test="stsOperateDay.actIntAmt != null">act_int_amt = #{stsOperateDay.actIntAmt},</if>
            <if test="stsOperateDay.jtFrAmt != null">jt_fr_amt = #{stsOperateDay.jtFrAmt},</if>
            <if test="stsOperateDay.fzAmt != null">fz_amt = #{stsOperateDay.fzAmt},</if>
            <if test="stsOperateDay.zbFrAmt != null">zb_fr_amt = #{stsOperateDay.zbFrAmt},</if>
            <if test="stsOperateDay.fundBalanceAmt != null">fund_balance_amt = #{stsOperateDay.fundBalanceAmt},</if>
            <if test="stsOperateDay.userBalanceAmt != null">user_balance_amt = #{stsOperateDay.userBalanceAmt},</if>
            <if test="stsOperateDay.accumProfitAmt != null">accum_profit_amt = #{stsOperateDay.accumProfitAmt},</if>
            <if test="stsOperateDay.compensatePrintAmt != null">compensate_print_amt = #{stsOperateDay.compensatePrintAmt},</if>
            <if test="stsOperateDay.compensateIntAmt != null">compensate_int_amt = #{stsOperateDay.compensateIntAmt},</if>
            <if test="stsOperateDay.compensateOintAmt != null">compensate_oint_amt = #{stsOperateDay.compensateOintAmt},</if>
            <if test="stsOperateDay.compensateTotalAmt != null">compensate_total_amt = #{stsOperateDay.compensateTotalAmt},</if>
            <if test="stsOperateDay.compensateRepayPrintAmt != null">compensate_repay_print_amt = #{stsOperateDay.compensateRepayPrintAmt},</if>
            <if test="stsOperateDay.compensateRepayTotalAmt != null">compensate_repay_total_amt = #{stsOperateDay.compensateRepayTotalAmt},</if>
            <if test="stsOperateDay.createTime != null">create_time = #{stsOperateDay.createTime},</if>
            <if test="stsOperateDay.updateTime != null">update_time = #{stsOperateDay.updateTime},</if>
            <if test="stsOperateDay.remarks != null">remarks = #{stsOperateDay.remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectStsOperateDayDtoList" resultMap="StsOperateDayDtoResult">
        SELECT recon_date,loan_amt,act_print_amt,act_int_amt,jt_fr_amt,zb_fr_amt,user_balance_amt FROM sts_operate_day WHERE product_no=#{productNo,jdbcType=VARCHAR}
    </select>

    <select id="selectReconDateListByProductNo" resultType="java.lang.String">
        SELECT date_format(s.recon_date, '%Y-%m-%d') AS dateInfluence FROM sts_operate_day s WHERE recon_date &gt; #{reconDate,jdbcType=VARCHAR} AND product_no=#{productNo,jdbcType=VARCHAR} group by dateInfluence
    </select>

    <select id="selectStsOperateDayListByReconDate" resultMap="StsOperateDayResult">
         <include refid="selectStsOperateDayVo"/>
         WHERE recon_date &gt; #{reconDate,jdbcType=VARCHAR} AND product_no=#{productNo,jdbcType=VARCHAR}
    </select>

    <update id="updateStsOperateDayList" parameterType="java.util.List">
        <foreach collection="stsOperateDayList" item="item" separator=";">
             update sts_operate_day
            <set>
                <if test="item.reconDate != null">recon_date = #{item.reconDate,jdbcType=TIMESTAMP},</if>
                <if test="item.productNo != null">product_no = #{item.productNo},</if>
                <if test="item.loanAmt != null">loan_amt = #{item.loanAmt},</if>
                <if test="item.actPrintAmt != null">act_print_amt = #{item.actPrintAmt},</if>
                <if test="item.intAmt != null">int_amt = #{item.intAmt},</if>
                <if test="item.ointAmt != null">oint_amt = #{item.ointAmt},</if>
                <if test="item.flAmt != null">fl_amt = #{item.flAmt},</if>
                <if test="item.advDefineAmt != null">adv_define_amt = #{item.advDefineAmt},</if>
                <if test="item.deductAmt != null">deduct_amt = #{item.deductAmt},</if>
                <if test="item.reduceAmt != null">reduce_amt = #{item.reduceAmt},</if>
                <if test="item.actIntAmt != null">act_int_amt = #{item.actIntAmt},</if>
                <if test="item.jtFrAmt != null">jt_fr_amt = #{item.jtFrAmt},</if>
                <if test="item.fzAmt != null">fz_amt = #{item.fzAmt},</if>
                <if test="item.zbFrAmt != null">zb_fr_amt = #{item.zbFrAmt},</if>
                <if test="item.fundBalanceAmt != null">fund_balance_amt = #{item.fundBalanceAmt},</if>
                <if test="item.userBalanceAmt != null">user_balance_amt = #{item.userBalanceAmt},</if>
                <if test="item.accumProfitAmt != null">accum_profit_amt = #{item.accumProfitAmt},</if>
                <if test="item.compensatePrintAmt != null">compensate_print_amt = #{item.compensatePrintAmt},</if>
                <if test="item.compensateIntAmt != null">compensate_int_amt = #{item.compensateIntAmt},</if>
                <if test="item.compensateOintAmt != null">compensate_oint_amt = #{item.compensateOintAmt},</if>
                <if test="item.compensateTotalAmt != null">compensate_total_amt = #{item.compensateTotalAmt},</if>
                <if test="item.compensateRepayPrintAmt != null">compensate_repay_print_amt = #{item.compensateRepayPrintAmt},</if>
                <if test="item.compensateRepayTotalAmt != null">compensate_repay_total_amt = #{item.compensateRepayTotalAmt},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
</mapper>