<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.ruoyi.core.offSupply.mapper.OffCategoryMainMapper">
    
    <resultMap type="OffCategoryMain" id="OffCategoryMainResult">
        <result property="id"    column="id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="companyId"    column="company_id"    />
        <result property="parentName"    column="parentName"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="shortNum"    column="short_num"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="OffNotifyUser" id="OffNotifyUserResult">
        <result property="categoryId"    column="category_id"    />
        <result property="userId"    column="user_id"    />
        <result property="userNickName"    column="nick_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectOffCategoryMainVo">
        select id, category_name, parent_id, company_id, status, short_num, del_flag, create_by, create_time, update_by, update_time from off_category_main
    </sql>

    <select id="selectOffCategoryMainList" parameterType="OffCategoryMain" resultType="org.ruoyi.core.offSupply.domain.OffCategoryMain">
        select ocm.id, ocm.category_name AS categoryName, ocm.parent_id AS parentId, ocm2.category_name as parentName, ocm.company_id AS companyId, sc.company_short_name AS companyShortName, ocm.status, ocm.short_num AS shortNum,
               ocm.remark, ocm.del_flag AS delFlag, ocm.create_by AS createBy, su.nick_name AS createByName, ocm.create_time AS createTime, ocm.update_by AS updateBy, ocm.update_time AS updateTime
        from off_category_main ocm
        left join off_category_main ocm2 on ocm.parent_id = ocm2.id
        left join sys_company sc on ocm.company_id = sc.id
        left join sys_user su on ocm.create_by = su.user_name
        <where>
            <if test="authCompanyIds != null and authCompanyIds.size() > 0">
                AND ocm.company_id in
                <foreach collection="authCompanyIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="categoryName != null  and categoryName != ''"> and ocm.category_name like concat('%', #{categoryName}, '%')</if>
            <if test="id != null "> and ocm.parent_id = #{id}</if>
            <if test="companyId != null "> and ocm.company_id = #{companyId}</if>
            <if test="status != null  and status != ''"> and ocm.status = #{status}</if>
             and ocm.del_flag = '0'
        </where>
        /*keep orderby */
        order by ocm.short_num asc, ocm.update_time, ocm.create_time desc
    </select>
    
    <select id="selectOffCategoryMainById" parameterType="Long" resultType="org.ruoyi.core.offSupply.domain.OffCategoryMain">
        select ocm.id, ocm.category_name AS categoryName, ocm.parent_id AS parentId, ocm2.category_name AS parentName, ocm.company_id AS companyId, sc.company_short_name AS companyShortName, ocm.status, ocm.short_num AS shortNum,
               ocm.del_flag AS delFlag, ocm.remark, ocm.create_by AS createBy, ocm.create_time AS createTime, ocm.update_by AS updateBy, ocm.update_time AS updateTime
        from off_category_main ocm
        left join off_category_main ocm2 on ocm.parent_id = ocm2.id
        left join sys_company sc on ocm.company_id = sc.id
        where ocm.id = #{id}
    </select>

    <insert id="insertOffCategoryMain" parameterType="OffCategoryMain" useGeneratedKeys="true" keyProperty="id">
        insert into off_category_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName !=''">category_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="companyId != null">company_id,</if>
            <if test="status != null and status !=''">status,</if>
            <if test="shortNum != null">short_num,</if>
            <if test="delFlag != null and delFlag !=''">del_flag,</if>
            <if test="remark != null and remark !=''">remark,</if>
            <if test="createBy != null and createBy !=''" >create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy !=''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName !=''">#{categoryName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="status != null and status !=''">#{status},</if>
            <if test="shortNum != null">#{shortNum},</if>
            <if test="delFlag != null and delFlag !=''">#{delFlag},</if>
            <if test="remark != null and remark !=''">#{remark},</if>
            <if test="createBy != null and createBy !=''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy !=''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateOffCategoryMain" parameterType="OffCategoryMain">
        update off_category_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName !='' ">category_name = #{categoryName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="status != null and status !=''">status = #{status},</if>
            <if test="shortNum != null">short_num = #{shortNum},</if>
            <if test="delFlag != null and delFlag !=''">del_flag = #{delFlag},</if>
            <if test="remark != null and remark !=''">remark = #{remark},</if>
            <if test="createBy != null and createBy !=''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy !=''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOffCategoryMainById" parameterType="Long">
        update off_category_main set del_flag = '1' where id = #{id}
    </delete>

    <delete id="deleteOffCategoryMainByIds" parameterType="String">
        update off_category_main set del_flag = '1' where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <insert id="batchOffNotifyUser">
        insert into off_notify_user(category_id, user_id, create_by, create_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.categoryId}, #{item.userId}, #{item.createBy}, #{item.createTime})
        </foreach>
    </insert>

    <delete id="deleteOffNotifyUserByCategoryIds" parameterType="String">
        delete from off_notify_user where category_id in
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <delete id="deleteOffNotifyUserByCategoryId" parameterType="Long">
        delete from off_notify_user where category_id = #{categoryId}
    </delete>

    <select id="selectOffCategoryMainByParentId" resultType="org.ruoyi.core.offSupply.domain.OffCategoryMain">
        select id, category_name as categoryName
        from off_category_main
        <where>
            <!-- 如果flag为'1'，则只查询父ID为#{id}的子类别 -->
            <if test="flag != null and flag == '1'.toString()">
                parent_id = #{id}
            </if>
            <!-- 如果flag为'2'，则查询父ID为#{id}的子类别或ID为#{id}的类别本身 -->
            <if test="flag != null and flag == '2'.toString()">
                parent_id = #{id} or id = #{id}
            </if>
            and status = '0' and del_flag = '0'
        </where>
    </select>

    <select id="selectOffNotifyUserByCategoryId" resultMap="OffNotifyUserResult">
        select onu.*,su.nick_name
        from off_notify_user onu
                 left join sys_user su on onu.user_id = su.user_id
        where category_id = #{categoryId}
    </select>

    <select id="selectOffCategoryMainListByIds" resultMap="OffCategoryMainResult">
        select ofm.id, ofm.category_name, ofm.parent_id, ofm1.category_name as parentName, ofm.company_id
        from off_category_main ofm
        left join off_category_main ofm1 on ofm.parent_id = ofm1.id
        where ofm.id in
        <foreach item="id" collection="categoryIdCollect" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>