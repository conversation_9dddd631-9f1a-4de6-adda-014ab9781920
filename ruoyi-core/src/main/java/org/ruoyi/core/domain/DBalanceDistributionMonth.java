package org.ruoyi.core.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 外部系统平台余额分布对象 d_balance_distribution_month
 *
 * <AUTHOR>
 * @date 2022-06-10
 */
public class DBalanceDistributionMonth extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 外部系统平台编码 */
    @Excel(name = "外部系统平台编码",dictType = "platform_no")
    private String platformNo;

    /** 担保公司编码 */
    @Excel(name = "担保公司编码",dictType = "cust_no")
    private String custNo;

    /** 合作方编码 */
    @Excel(name = "合作方编码",dictType = "partner_no")
    private String partnerNo;

    /** 资金方编码 */
    @Excel(name = "资金方编码",dictType = "fund_no")
    private String fundNo;

    /** 产品编码 */
    @Excel(name = "产品编码",dictType = "product_no")
    private String productNo;

    /** 放款月份 */
    @Excel(name = "放款月份")
    private String loanMonth;

    /** 统计月份 */
    @Excel(name = "统计月份")
    private String reconMonth;

    /** 在贷余额 */
    @Excel(name = "在贷余额")
    private BigDecimal loanBalanceAmount;

    /** 在贷笔数 */
    @Excel(name = "在贷笔数")
    private Long loanRemainNumber;

    /** 类型 */
    @Excel(name = "类型")
    private String balanceDistributionType;

    /** Mn笔数 */
    @Excel(name = "Mn笔数")
    private Long mNumber;

    /** Mn贷款余额 */
    @Excel(name = "Mn贷款余额")
    private BigDecimal mBalanceAmount;

    /** 是否映射成功（Y映射成功N映射失败） */
    @Excel(name = "是否映射成功", readConverterExp = "Y=映射成功,N映射失败")
    private String isMapping;

    private Map<String, List<String>> moreSearchMap;

    private String moreSearch;

    public Map<String, List<String>> getMoreSearchMap() {
        return moreSearchMap;
    }

    public void setMoreSearchMap(Map<String, List<String>> moreSearchMap) {
        this.moreSearchMap = moreSearchMap;
    }

    public String getMoreSearch() {
        return moreSearch;
    }

    public void setMoreSearch(String moreSearch) {
        this.moreSearch = moreSearch;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setPlatformNo(String platformNo)
    {
        this.platformNo = platformNo;
    }

    public String getPlatformNo()
    {
        return platformNo;
    }
    public void setCustNo(String custNo)
    {
        this.custNo = custNo;
    }

    public String getCustNo()
    {
        return custNo;
    }
    public void setPartnerNo(String partnerNo)
    {
        this.partnerNo = partnerNo;
    }

    public String getPartnerNo()
    {
        return partnerNo;
    }
    public void setFundNo(String fundNo)
    {
        this.fundNo = fundNo;
    }

    public String getFundNo()
    {
        return fundNo;
    }
    public void setProductNo(String productNo)
    {
        this.productNo = productNo;
    }

    public String getProductNo()
    {
        return productNo;
    }
    public void setLoanMonth(String loanMonth)
    {
        this.loanMonth = loanMonth;
    }

    public String getLoanMonth()
    {
        return loanMonth;
    }
    public void setReconMonth(String reconMonth)
    {
        this.reconMonth = reconMonth;
    }

    public String getReconMonth()
    {
        return reconMonth;
    }
    public void setLoanBalanceAmount(BigDecimal loanBalanceAmount)
    {
        this.loanBalanceAmount = loanBalanceAmount;
    }

    public BigDecimal getLoanBalanceAmount()
    {
        return loanBalanceAmount;
    }
    public void setLoanRemainNumber(Long loanRemainNumber)
    {
        this.loanRemainNumber = loanRemainNumber;
    }

    public Long getLoanRemainNumber()
    {
        return loanRemainNumber;
    }
    public void setBalanceDistributionType(String balanceDistributionType)
    {
        this.balanceDistributionType = balanceDistributionType;
    }

    public String getBalanceDistributionType()
    {
        return balanceDistributionType;
    }
    public void setmNumber(Long mNumber)
    {
        this.mNumber = mNumber;
    }

    public Long getmNumber()
    {
        return mNumber;
    }
    public void setmBalanceAmount(BigDecimal mBalanceAmount)
    {
        this.mBalanceAmount = mBalanceAmount;
    }

    public BigDecimal getmBalanceAmount()
    {
        return mBalanceAmount;
    }
    public void setIsMapping(String isMapping)
    {
        this.isMapping = isMapping;
    }

    public String getIsMapping()
    {
        return isMapping;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("platformNo", getPlatformNo())
            .append("custNo", getCustNo())
            .append("partnerNo", getPartnerNo())
            .append("fundNo", getFundNo())
            .append("productNo", getProductNo())
            .append("loanMonth", getLoanMonth())
            .append("reconMonth", getReconMonth())
            .append("loanBalanceAmount", getLoanBalanceAmount())
            .append("loanRemainNumber", getLoanRemainNumber())
            .append("balanceDistributionType", getBalanceDistributionType())
            .append("mNumber", getmNumber())
            .append("mBalanceAmount", getmBalanceAmount())
            .append("isMapping", getIsMapping())
            .append("remark", getRemark())
            .append("createTime", getCreateTime())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
