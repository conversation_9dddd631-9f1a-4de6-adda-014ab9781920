package org.ruoyi.core.oasystem.util;

import org.apache.poi.ss.usermodel.*;
import org.ruoyi.core.oasystem.domain.dto.ExcelImportOfReceiptAndPaymentInfoDto;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目信息 - 项目收付款  导入excel
 *
 * @Description
 * <AUTHOR>
 * @Date 2025/4/15 14:45
 **/
public class ExcelImportOfReceiptAndPaymentInfo {
    private InputStream inputStream;
    private Workbook wb;

    public void init(InputStream is) throws IOException {
        this.inputStream = is;
        this.wb = WorkbookFactory.create(is);
    }

    /**
     * 导入excel，读取数据
     *
     * @return 转换后集合
     */
    public List<ExcelImportOfReceiptAndPaymentInfoDto> importExcelHandle() throws Exception
    {
        List<ExcelImportOfReceiptAndPaymentInfoDto> excelImportOfReceiptAndPaymentInfoDtoList = new ArrayList<>();
        Workbook sheets = this.wb;
        Sheet sheet = sheets.getSheetAt(0);
        if (sheet == null) {
            throw new IOException("文件sheet不存在");
        }
        int rows = sheet.getLastRowNum();
        for (int i = 1; i <= rows; i++) {
            //从第二行开始读取数据，第一行是表头
            Row row = sheet.getRow(i);
            if (row == null) {
                break; // 遇到空行结束
            }

            ExcelImportOfReceiptAndPaymentInfoDto excelImportOfReceiptAndPaymentInfoDto = new ExcelImportOfReceiptAndPaymentInfoDto();
            excelImportOfReceiptAndPaymentInfoDto.setItemName(getCellValue(row.getCell(0)));
            excelImportOfReceiptAndPaymentInfoDto.setProjectName(getCellValue(row.getCell(1)));
            excelImportOfReceiptAndPaymentInfoDto.setTraderTypeOneAccountName(getCellValue(row.getCell(2)));
            excelImportOfReceiptAndPaymentInfoDto.setTraderTypeOneBankOfDeposit(getCellValue(row.getCell(3)));
            excelImportOfReceiptAndPaymentInfoDto.setTraderTypeOneAccountNumber(getCellValue(row.getCell(4)));
            excelImportOfReceiptAndPaymentInfoDto.setTraderTypeOneTraderType(getCellValue(row.getCell(5))); // 收款人是内部/外部公司
            excelImportOfReceiptAndPaymentInfoDto.setTraderTypeZeroAccountName(getCellValue(row.getCell(6)));
            excelImportOfReceiptAndPaymentInfoDto.setTraderTypeZeroBankOfDeposit(getCellValue(row.getCell(7)));
            excelImportOfReceiptAndPaymentInfoDto.setTraderTypeZeroAccountNumber(getCellValue(row.getCell(8)));
            excelImportOfReceiptAndPaymentInfoDto.setTraderTypeZeroTraderType(getCellValue(row.getCell(9))); // 付款人是内部/外部公司

            // 检查每个属性是否为空
            if (isDtoValid(excelImportOfReceiptAndPaymentInfoDto)) {
                excelImportOfReceiptAndPaymentInfoDtoList.add(excelImportOfReceiptAndPaymentInfoDto);
            }
        }
        this.wb.close();
        this.inputStream.close();
        return excelImportOfReceiptAndPaymentInfoDtoList;
    }


    private boolean isDtoValid(ExcelImportOfReceiptAndPaymentInfoDto dto) {
        return dto.getItemName() != null &&
                dto.getProjectName() != null &&
                dto.getTraderTypeOneAccountName() != null &&
                dto.getTraderTypeOneBankOfDeposit() != null &&
                dto.getTraderTypeOneAccountNumber() != null &&
                dto.getTraderTypeOneTraderType() != null && // 新增检查
                dto.getTraderTypeZeroAccountName() != null &&
                dto.getTraderTypeZeroBankOfDeposit() != null &&
                dto.getTraderTypeZeroAccountNumber() != null &&
                dto.getTraderTypeZeroTraderType() != null; // 新增检查
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return null;
        }
    }
}
