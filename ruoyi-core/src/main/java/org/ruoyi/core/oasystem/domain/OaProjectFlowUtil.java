package org.ruoyi.core.oasystem.domain;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName OaProjectFlowUtil.java
 * @Description TODO
 * @createTime 2023年07月06日 16:57:00
 */
public class OaProjectFlowUtil {
    private String id;
   private String companyNo;
   private String compony;
    private String modelId;
    private String    modelName;
    /** 是否联动财务项目系统 */
    private String isLinkageCwxmgl;

    /** 记账金额字段 */
    private String accountingField;
    /** 记账金额字段名称 */
    private String accountingFieldName;

    private String projectTypeField;
    private String projectTypeFieldName;


    private String feeCompanyField;
    private String feeCompanyFieldName;



    private String projectField;
    private String remark;
    private List<OaProjectFlowUtil2> proList;

    public String getProjectTypeField() {
        return projectTypeField;
    }

    public void setProjectTypeField(String projectTypeField) {
        this.projectTypeField = projectTypeField;
    }

    public String getProjectTypeFieldName() {
        return projectTypeFieldName;
    }

    public void setProjectTypeFieldName(String projectTypeFieldName) {
        this.projectTypeFieldName = projectTypeFieldName;
    }

    public String getFeeCompanyField() {
        return feeCompanyField;
    }

    public void setFeeCompanyField(String feeCompanyField) {
        this.feeCompanyField = feeCompanyField;
    }

    public String getFeeCompanyFieldName() {
        return feeCompanyFieldName;
    }

    public void setFeeCompanyFieldName(String feeCompanyFieldName) {
        this.feeCompanyFieldName = feeCompanyFieldName;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getCompony() {
        return compony;
    }

    public void setCompony(String compony) {
        this.compony = compony;
    }

    public String getModelId() {
        return modelId;
    }

    public void setModelId(String modelId) {
        this.modelId = modelId;
    }

    public String getModelName() {
        return modelName;
    }

    public void setModelName(String modelName) {
        this.modelName = modelName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }


    public OaProjectFlowUtil() {
    }

    public OaProjectFlowUtil(String id, String companyNo, String compony, String modelId, String modelName, String isLinkageCwxmgl, String accountingField, String accountingFieldName, String projectTypeField, String projectTypeFieldName, String feeCompanyField, String feeCompanyFieldName, String projectField, String remark, List<OaProjectFlowUtil2> proList) {
        this.id = id;
        this.companyNo = companyNo;
        this.compony = compony;
        this.modelId = modelId;
        this.modelName = modelName;
        this.isLinkageCwxmgl = isLinkageCwxmgl;
        this.accountingField = accountingField;
        this.accountingFieldName = accountingFieldName;
        this.projectTypeField = projectTypeField;
        this.projectTypeFieldName = projectTypeFieldName;
        this.feeCompanyField = feeCompanyField;
        this.feeCompanyFieldName = feeCompanyFieldName;
        this.projectField = projectField;
        this.remark = remark;
        this.proList = proList;
    }

    public String getProjectField() {
        return projectField;
    }

    public void setProjectField(String projectField) {
        this.projectField = projectField;
    }

    public List<OaProjectFlowUtil2> getProList() {
        return proList;
    }

    public void setProList(List<OaProjectFlowUtil2> proList) {
        this.proList = proList;
    }

    public String getIsLinkageCwxmgl() {
        return isLinkageCwxmgl;
    }

    public void setIsLinkageCwxmgl(String isLinkageCwxmgl) {
        this.isLinkageCwxmgl = isLinkageCwxmgl;
    }

    public String getAccountingField() {
        return accountingField;
    }

    public void setAccountingField(String accountingField) {
        this.accountingField = accountingField;
    }

    public String getAccountingFieldName() {
        return accountingFieldName;
    }

    public void setAccountingFieldName(String accountingFieldName) {
        this.accountingFieldName = accountingFieldName;
    }
}
