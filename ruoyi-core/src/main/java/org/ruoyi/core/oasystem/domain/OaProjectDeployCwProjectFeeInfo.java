package org.ruoyi.core.oasystem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 项目信息-收付款信息表（对照数据库实体）
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/12/23 15:54
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaProjectDeployCwProjectFeeInfo {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目表id */
    private Long oaProjectDeployId;

    /** 公司表id */
    private Long companyId;

    /** 信息费公司名（当公司表id为空时，则为暂不确定公司） */
    private String companyName;

    /** 账号（当公司表id为空时，则为暂不确定） */
    private String accountNumber;

    /** 开户行（当公司表id为空时，则为暂不确定） */
    private String bankOfDeposit;

    /** 费率 */
    private BigDecimal rate;

    /** 税率 */
    private BigDecimal taxRate;

    /** 排序字段 */
    private Integer orderNum;

    /** 状态，0正常 1停用（失效） */
    private String status;

    /** 创建者 */
    private String createBy;

    /** 创建者id */
    private Long createId;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新者id */
    private Long updateId;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
}
