package org.ruoyi.core.oasystem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.oasystem.domain.AddOaAccountRulers;
import org.ruoyi.core.oasystem.domain.OaAccountingVoucherRules;
import org.ruoyi.core.oasystem.domain.OaAccountingVoucherRulesUtils;
import org.ruoyi.core.oasystem.service.IOaAccountingVoucherRulesService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 * 
 * <AUTHOR>
 * @date 2023-07-12
 */
@RestController
@RequestMapping("/oaSystem/voucherRules")
public class OaAccountingVoucherRulesController extends BaseController
{
    @Autowired
    private IOaAccountingVoucherRulesService oaAccountingVoucherRulesService;

    /**
     * 查询【请填写功能名称】列表
     */
//    @PreAuthorize("@ss.hasPermi('system:rules:list')")
    @GetMapping("/list")
    public TableDataInfo list(OaAccountingVoucherRules oaAccountingVoucherRules)
    {
        startPage();
        TableDataInfo tableDataInfo = new TableDataInfo();
        List<AddOaAccountRulers> list = oaAccountingVoucherRulesService.selectOaAccountingVoucherRulesList(oaAccountingVoucherRules);
        Long total  =  oaAccountingVoucherRulesService.selectTotal(oaAccountingVoucherRules);
        tableDataInfo.setMsg("");
        tableDataInfo.setRows(list);
        tableDataInfo.setCode(200);
        tableDataInfo.setTotal(total);
        return tableDataInfo;
    }
//
//    /**
//     * 导出【请填写功能名称】列表
//     */
//    //@PreAuthorize("@ss.hasPermi('system:rules:export')")
//    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, OaAccountingVoucherRules oaAccountingVoucherRules)
//    {
//        List<AddOaAccountRulers> list = oaAccountingVoucherRulesService.selectOaAccountingVoucherRulesList(oaAccountingVoucherRules);
//        ExcelUtil<AddOaAccountRulers> util = new ExcelUtil<OaAccountingVoucherRules>(AddOaAccountRulers.class);
//        util.exportExcel(response, list, "【请填写功能名称】数据");
//    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:rules:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(oaAccountingVoucherRulesService.selectOaAccountingVoucherRulesById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    // @PreAuthorize("@ss.hasPermi('system:rules:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody OaAccountingVoucherRules oaAccountingVoucherRules)
    {
        return toAjax(oaAccountingVoucherRulesService.insertOaAccountingVoucherRules(oaAccountingVoucherRules));
    }

    /**
     * 修改【请填写功能名称】
     */
    // @PreAuthorize("@ss.hasPermi('system:rules:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody OaAccountingVoucherRules oaAccountingVoucherRules)
    {
        return toAjax(oaAccountingVoucherRulesService.updateOaAccountingVoucherRules(oaAccountingVoucherRules));
    }

    /**
     * 删除【请填写功能名称】
     */
//    // @PreAuthorize("@ss.hasPermi('system:rules:remove')")
//    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(oaAccountingVoucherRulesService.deleteOaAccountingVoucherRulesByIds(ids));
//    }



    /**
     * 删除【请填写功能名称】
     */
    // @PreAuthorize("@ss.hasPermi('system:rules:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult removeByAssociationId(@PathVariable String id)
    {
        return toAjax(oaAccountingVoucherRulesService.deleteDataByAssociationId(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping("/addVoucherRules")
    public AjaxResult addVoucherRules(@RequestBody AddOaAccountRulers addOaAccountRulers)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(oaAccountingVoucherRulesService.addOaAccountingVoucherRules(addOaAccountRulers,loginUser));
    }


    /**
     * 获取【请填写功能名称】详细信息
     */
    @GetMapping(value = "/getUpdatedatad/{id}")
    public AjaxResult getUpdataDataById(@PathVariable("id") String id)
    {
        return AjaxResult.success(oaAccountingVoucherRulesService.getDataById(id));
    }



    /**
     * 获取【请填写功能名称】详细信息
     */
    @GetMapping(value = "/handleHistoryData")
    public AjaxResult getHistory()
    {
        return AjaxResult.success(oaAccountingVoucherRulesService.handleHistoryData());
    }


}
