package org.ruoyi.core.oasystem.service;

import org.ruoyi.core.oasystem.domain.OaDictDynamic;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IOaDictDynamicService.java
 * @Description TODO
 * @createTime 2023年07月11日 14:17:00
 */
public interface IOaDictDynamicService {

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public OaDictDynamic selectOaDictDynamicById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param oaDictDynamic 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    public List<OaDictDynamic> selectOaDictDynamicList(OaDictDynamic oaDictDynamic);
    /**
     * 新增【请填写功能名称】
     *
     * @param oaDictDynamic 【请填写功能名称】
     * @return 结果
     */
    public int insertOaDictDynamic(OaDictDynamic oaDictDynamic);

    /**
     * 修改【请填写功能名称】
     *
     * @param oaDictDynamic 【请填写功能名称】
     * @return 结果
     */
    public int updateOaDictDynamic(OaDictDynamic oaDictDynamic);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaDictDynamicByIds(Long[] ids);
    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteOaDictDynamicById(Long id);

}
