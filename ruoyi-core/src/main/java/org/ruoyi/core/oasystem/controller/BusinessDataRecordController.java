package org.ruoyi.core.oasystem.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.oasystem.domain.BusinessDataRecord;
import org.ruoyi.core.oasystem.domain.vo.BusinessDataRecordVo;
import org.ruoyi.core.oasystem.service.IBusinessDataRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;

import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 业务信息审核记录Controller
 *
 * <AUTHOR>
 * @date 2024-06-26
 */
@RestController
@RequestMapping("/businessData/record")
public class BusinessDataRecordController extends BaseController
{
    @Autowired
    private IBusinessDataRecordService businessDataRecordService;

    /**
     * 查询业务信息审核记录列表
     */
//    @PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(BusinessDataRecord businessDataRecord)
    {
        startPage();
        List<BusinessDataRecord> list = businessDataRecordService.selectBusinessDataRecordList(businessDataRecord);
        return getDataTable(list);
    }

    /**
     * 导出业务信息审核记录列表
     */
//    @PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "业务信息审核记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, BusinessDataRecord businessDataRecord)
    {
        List<BusinessDataRecord> list = businessDataRecordService.selectBusinessDataRecordList(businessDataRecord);
        ExcelUtil<BusinessDataRecord> util = new ExcelUtil<BusinessDataRecord>(BusinessDataRecord.class);
        util.exportExcel(response, list, "业务信息审核记录数据");
    }

    /**
     * 获取业务信息审核记录详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(businessDataRecordService.selectBusinessDataRecordById(id));
    }

    /**
     * 发起流程时调用
     */
    @PostMapping("/startProd")
    public AjaxResult startProd(@RequestBody BusinessDataRecord businessDataRecord)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(businessDataRecordService.insertBusinessDataRecord(businessDataRecord,loginUser));
    }


    /**
     * 废弃流程  只传流程关联id就行
     * @param businessDataRecord
     * @return {@link AjaxResult}
     */
    @PostMapping("/abandonedProd")
    public AjaxResult abandonedProd(@RequestBody BusinessDataRecord businessDataRecord)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(businessDataRecordService.abandonedProd(businessDataRecord,loginUser));
    }


    /**
     * 流程结束
     * @param businessDataRecord
     * @return {@link AjaxResult}
     */
    @PostMapping("/overdProd")
    public AjaxResult overdProd(@RequestBody BusinessDataRecord businessDataRecord)
    {
        LoginUser loginUser = getLoginUser();
        return toAjax(businessDataRecordService.overProd(businessDataRecord,loginUser));
    }

    /**
     * 修改业务信息审核记录
     */
    @Log(title = "业务信息审核记录", businessType = BusinessType.UPDATE)
    @PostMapping("/updateData")
    public AjaxResult edit(@RequestBody BusinessDataRecord businessDataRecord)
    {
        return toAjax(businessDataRecordService.updateBusinessDataRecord(businessDataRecord));
    }

    /**
     * 删除业务信息审核记录
     */
//    @PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "业务信息审核记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(businessDataRecordService.deleteBusinessDataRecordByIds(ids));
    }


    @GetMapping("/getParamData")
    public BusinessDataRecord getBusinessDataRecord(BusinessDataRecord businessDataRecord)
    {
        BusinessDataRecord paramDataByBusiness = businessDataRecordService.getParamDataByBusiness(businessDataRecord);
        return  paramDataByBusiness;
    }

    @GetMapping("/getParamDataOfCheckConfig")
    public BusinessDataRecordVo selectByProcessIdOfCheckConfig(BusinessDataRecord businessDataRecord)
    {
        return businessDataRecordService.selectByProcessIdOfCheckConfig(businessDataRecord);
    }

    @GetMapping("/getParamDataOfAchievementEnter")
    public BusinessDataRecordVo selectByProcessIdOfAchievementEnter(BusinessDataRecord businessDataRecord)
    {
        return businessDataRecordService.selectByProcessIdOfAchievementEnter(businessDataRecord);
    }
}
