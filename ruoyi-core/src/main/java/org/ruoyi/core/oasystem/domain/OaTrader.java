package org.ruoyi.core.oasystem.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 【请填写功能名称】对象 oa_trader
 * 
 * <AUTHOR>
 * @date 2023-06-28
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaTrader extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    //主键（业务逻辑使用）
    private Long oaApplyId;

    //公司编码
    private Long companyNo;

    /** 交易人类型0付款人1收款人 9-收/付款人
     * 页面会传入一个字符 0,1，后端来处理 */
    @Excel(name = "交易人类型0付款人1收款人")
    private String traderType;

    //上面处理完之后，给到这个字段来进行入库查询
    private List<String> traderTypeList;

    /** 分类0公司1个人 */
    @Excel(name = "分类0公司1个人")
    private String type;
    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;
    /** 开户行 */
    @Excel(name = "开户行")
    private String bankOfDeposit;

    /** 账号 */
    @Excel(name = "账号")
    private String accountNumber;

    /** 简称 */
    @Excel(name = "简称")
    private String abbreviation;


    /** 账套ID */
    @Excel(name = "账套ID")
    private Long accountId;

    /** 是否关联账套Y是N否 */
    @Excel(name = "是否关联账套Y是N否")
    private String isAccount;




    /** 是否启用Y启用N禁用 */
    @Excel(name = "是否启用Y启用N禁用")
    private String isEnable;

    /** 最后修改时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "最后修改时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date endUpdateTime;

    //编辑后提交之后，展示是否可以编辑的标识，0可以编辑，1不可编辑
    private String addNotApprove;

    //删除标识;0-正常 1-删除
    private String delFlag;

    //修改前数据JSON
    private String oaApplyRecordsOldData;

    //修改后数据JSON
    private String oaApplyRecordsNewData;
}
