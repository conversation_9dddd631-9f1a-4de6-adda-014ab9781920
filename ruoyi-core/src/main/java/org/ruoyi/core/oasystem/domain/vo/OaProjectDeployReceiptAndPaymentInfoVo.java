package org.ruoyi.core.oasystem.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * 项目信息-收付款信息表（返回前端的数据）
 *
 * @Description
 * <AUTHOR>
 * @Date 2024/12/23 16:17
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class OaProjectDeployReceiptAndPaymentInfoVo {
    private static final long serialVersionUID = 1L;

    /** 项目信息id */
    private Long oaProjectDeployId;

    /** 原来拥有的收付款信息id集合 */
    private List<Long> oldIdList;

    /** 收付款类型:1-常规业务收支款项（含助贷平台和资金方） 2-技术服务方支出款项 */
    private String receiptAndPaymentType;

    /** 收付款具体信息集合 */
    private Map<String, Object> oaProjectDeployReceiptAndPaymentInfo;
}
