package org.ruoyi.core.loanMonitor.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 保证金余额监控列表
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MarginMonitoringVo {

    /** 项目id */
    private Long projectId;

    /** 项目名称 */
    private String projectName;

    /** 产品编码 */
    private String productCode;

    /** 当前可放款金额(元) */
    private BigDecimal availableLoanAmount;

    /** 未占用保证金金额(元) */
    private BigDecimal unappropriatedAmount;

    /** 项目保证金账户总金额(元) */
    private BigDecimal totalMargin;

    /** 保证金已占用金额(元) */
    private BigDecimal takenUpAmount;

    /** 机构保证金占用比例 */
    private BigDecimal occupancyRatio;

    /** 机构保证金占用比例字符串 */
    private String occupancyRatioStr;

    /** 当前在贷金额 */
    private BigDecimal loanBalance;

    /** 保证金比例 */
    private BigDecimal marginRate;

    /** 保证金比例字符串 */
    private String marginRateStr;

    /** 昨日实际放款金额(元) */
    private BigDecimal yesterdayLentActualAmount;

    /** 本月实际放款金额(元) */
    private BigDecimal monthLentActualAmount;

    /** 实际更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 预警阈值 */
    private BigDecimal ratioMax;

    /** 是否超过预警阈值标识 */
    private String warningFlag;

    /** 分页参数 */
    private Integer pageNum;

    /** 分页参数 */
    private Integer pageSize;
}
