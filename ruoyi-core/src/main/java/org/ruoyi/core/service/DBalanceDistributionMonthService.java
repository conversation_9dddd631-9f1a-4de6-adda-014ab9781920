package org.ruoyi.core.service;

import org.ruoyi.core.domain.DBalanceDistributionMonth;

import java.util.List;
import java.util.Map;

/**
 * @Author: 左东冉
 * @Create: 2022-06-10 13:55
 * @Description: 余额分布接口
 **/
public interface DBalanceDistributionMonthService {
    /**
     * 选择dbalance分布通过id
     *
     * @param id id
     * @return {@link DBalanceDistributionMonth}
     */
    DBalanceDistributionMonth selectDBalanceDistributionMonthById(Long id);

    /**
     * 选择dbalance分布列表
     *
     * @param dBalanceDistributionMonth d平衡分布月
     * @return {@link List}<{@link DBalanceDistributionMonth}>
     */
    List<DBalanceDistributionMonth> selectDBalanceDistributionMonthList(DBalanceDistributionMonth dBalanceDistributionMonth);

    /**
     * 月出口dbalance分布列表
     *
     * @param dBalanceDistributionMonth d平衡分布月
     * @return {@link List}<{@link DBalanceDistributionMonth}>
     */
    public List<DBalanceDistributionMonth> exportDBalanceDistributionMonthList(DBalanceDistributionMonth dBalanceDistributionMonth);
    /**
     * 插入dbalance月分布
     *
     * @param dBalanceDistributionMonth d平衡分布月
     * @return int
     */
    int insertDBalanceDistributionMonth(DBalanceDistributionMonth dBalanceDistributionMonth);

    /**
     * 更新dbalance月分布
     *
     * @param dBalanceDistributionMonth d平衡分布月
     * @return int
     */
    int updateDBalanceDistributionMonth(DBalanceDistributionMonth dBalanceDistributionMonth);

    /**
     * 删除dbalance分布由ids
     *
     * @param ids id
     * @return int
     */
    int deleteDBalanceDistributionMonthByIds(Long[] ids);

    /**
     * 月通过id删除dbalance分布
     *
     * @param id id
     * @return int
     */
    int deleteDBalanceDistributionMonthById(Long id);


    /**
     * 重新映射数据
     * @return
     */
    void anewMappingData();

    /**
     * 月出口dbalance分布列表
     *
     * @param dBalanceDistributionMonth d平衡分布月
     * @return {@link List}<{@link DBalanceDistributionMonth}>
     */
    List<Map<String, Object>> exportDBalanceDistributionMonthList1(DBalanceDistributionMonth dBalanceDistributionMonth);
}
