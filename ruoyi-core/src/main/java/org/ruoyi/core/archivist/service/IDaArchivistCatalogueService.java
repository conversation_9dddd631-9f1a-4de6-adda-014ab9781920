package org.ruoyi.core.archivist.service;

import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUnit;
import com.ruoyi.system.domain.SysCompany;
import com.ruoyi.system.domain.vo.SysCompanyVo;
import org.ruoyi.core.archivist.domain.DaArchivistCatalogue;
import org.ruoyi.core.archivist.domain.vo.*;
import org.ruoyi.core.cwproject.domain.CwProject;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 归档目录Service接口
 *
 * <AUTHOR>
 * @date 2023-11-27
 */
public interface IDaArchivistCatalogueService
{
    /**
     * 查询归档目录
     *
     * @param id 归档目录主键
     * @return 归档目录
     */
    public DaArchivistCatalogueVo selectDaArchivistCatalogueById(Long id);

    /**
     * 查询归档目录列表
     *
     * @param daArchivistCatalogue 归档目录
     * @return 归档目录集合
     */
    public List<DaArchivistCatalogueVo> selectDaArchivistCatalogueList(DaArchivistCatalogueVo daArchivistCatalogue);

    /**
     * 新增归档目录
     *
     * @param daArchivistCatalogue 归档目录
     * @return 结果
     */
    public int insertDaArchivistCatalogue(DaArchivistCatalogue daArchivistCatalogue);

    /**
     * 修改归档目录
     *
     * @param daArchivistCatalogue 归档目录
     * @return 结果
     */
    public int updateDaArchivistCatalogue(DaArchivistCatalogue daArchivistCatalogue);

    /**
     * 批量删除归档目录
     *
     * @param ids 需要删除的归档目录主键集合
     * @return 结果
     */
    public int deleteDaArchivistCatalogueByIds(Long[] ids);

    /**
     * 删除归档目录信息
     *
     * @param id 归档目录主键
     * @return 结果
     */
    public int deleteDaArchivistCatalogueById(Long id);

    /**
     * 点击’归档‘按钮，生成目录并保存
     * @param daArchivistMainVo 归档信息
     * @return 结果
     */
    AjaxResult createCatalogueSave(DaArchivistMainVo daArchivistMainVo);

    /**
     * 目录树状列表
     * @param pertainArchivist 所属档案库
     * @return
     */
    List<DaArchivistCatalogueVo> getTreeList(String pertainArchivist);

    /**
     * 获取当前数量总和 生成系统目录编号
     * @param createTime
     * @return
     */
    int getCountByCreateTime(String createTime);

    /**
     * 根据部门id查询公司信息
     * @param deptId
     * @return
     */
    SysDept selectCompanyInfoByDeptId(Long deptId);

    /**
     * 拆分表单中的'项目名称'json字符串
     * @param daArchivistJsonProjectVo
     * @return
     */
    DaProjectVo splitJsonStringToQueryProject(DaArchivistJsonProjectVo daArchivistJsonProjectVo);

    /**
     * 根据公司id查询部门树
     */
    List<SysDeptVo> getDeptTreeList(Long companyId);

    /**
     * 获取全量公司信息列表
     * @return
     */
    List<SysUnit> getAllCompany();

    /**
     * 获取当前用户是否有流程发起公司的归档权限
     * @return
     */
    Map<String,Object> getUserAuthCompany(Long companyId);
}
