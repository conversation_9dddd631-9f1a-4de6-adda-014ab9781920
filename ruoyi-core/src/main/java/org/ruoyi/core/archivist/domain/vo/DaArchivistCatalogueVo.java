package org.ruoyi.core.archivist.domain.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;
import org.ruoyi.core.archivist.domain.DaArchivistCatalogue;
import org.ruoyi.core.archivist.domain.DaArchivistMain;
import org.ruoyi.core.archivist.domain.vo.DaArchivistCatalogueProVo;

import java.util.List;
import java.util.Set;

@Data
public class DaArchivistCatalogueVo {

    /** 主键 */
    private Long id;

    /** 目录名称 */
    private String catalogueName;

    /** 上级目录名称 */
    private String parentCatalogueName;

    /** 上级目录id */
    private Long parentId;

    /** 系统目录编号 */
    private String catalogueSystemCode;

    /** 目录编号 */
    private String catalogueCode;

    /** 所属公司id */
    private Long orgId;

    /** 所属公司名称 */
    private String orgName;

    /** 所属部门id */
    private Long deptId;

    /** 所属部门名称 */
    private String deptName;

    /** 所属档案库(HT:合同;JCXZ:基础行政) */
    private String pertainArchivist;

    /** 显示排序 */
    private Long orderNum;

    /** 所属目录*/
    private List<DaArchivistCatalogueProVo> pertainCatalogueProList;

    /** 子集 */
    List<DaArchivistCatalogueVo> fPiattaformas;

    /** 所属卷库 */
    private String pertainLib;

    /** 项目id */
    private Long [] projectId;

    private Integer pageNum;

    private Integer pageSize;

    /** 创建人 */
    private String createBy;

    /** 创建时间 */
    private String createTime;

    /** 备注 */
    private String remark;

    private Long userId;

    List<Long> unitIdList;

    /** 档案id */
    private String archivistId;

    /** 档案名称 */
    private String archivistName;

    /** 项目名称 */
    private String projectName;

    /** 目录id集合 */
    private Set<Long> catalogueIdSet;

    /** 归档流程创建人(发起人) */
    private String flowInitiator;
}
