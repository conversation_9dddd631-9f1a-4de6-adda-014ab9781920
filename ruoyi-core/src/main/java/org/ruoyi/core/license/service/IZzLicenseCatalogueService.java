package org.ruoyi.core.license.service;
import java.util.List;
import java.util.Map;

import com.ruoyi.common.core.domain.TreeSelect;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.entity.SysUnit;
import org.ruoyi.core.license.domain.ZzAuthority;
import org.ruoyi.core.license.domain.ZzLicenseCatalogue;
import org.ruoyi.core.license.domain.ZzLicenseMain;
import org.ruoyi.core.license.domain.vo.ZzLicenseCatalogueVo;
import org.ruoyi.core.license.domain.vo.ZzLicenseMoveVo;

/**
 * 证照目录Service接口
 *
 * <AUTHOR>
 * @date 2024-01-25
 */
public interface IZzLicenseCatalogueService
{
    /**
     * 查询证照目录
     *
     * @param id 证照目录主键
     * @return 证照目录
     */
    public ZzLicenseCatalogueVo selectZzLicenseCatalogueById(Long id);

    /**
     * 查询证照目录列表
     *
     * @param zzLicenseCatalogue 证照目录
     * @return 证照目录集合
     */
    public List<ZzLicenseCatalogue> selectZzLicenseCatalogueList(ZzLicenseCatalogue zzLicenseCatalogue);

    /**
     * 新增证照目录
     *
     * @param zzLicenseCatalogue 证照目录
     * @return 结果
     */
    public int insertZzLicenseCatalogue(ZzLicenseCatalogue zzLicenseCatalogue);

    /**
     * 修改证照目录
     *
     * @param zzLicenseCatalogue 证照目录
     * @return 结果
     */
    public int updateZzLicenseCatalogue(ZzLicenseCatalogue zzLicenseCatalogue);

    /**
     * 批量删除证照目录
     *
     * @param ids 需要删除的证照目录主键集合
     * @return 结果
     */
    public int deleteZzLicenseCatalogueByIds(Long[] ids);

    /**
     * 删除证照目录信息
     *
     * @param id 证照目录主键
     * @return 结果
     */
    public int deleteZzLicenseCatalogueById(Long id);

    /**
     * 获取证照目录树形列表
     * @param zzLicenseCatalogue
     * @return
     */
    public List<ZzLicenseCatalogue> getTreeList(ZzLicenseCatalogue zzLicenseCatalogue);

    /**
     * 获取当前用户所有有权限的目录
     * @return
     */
    List<ZzLicenseCatalogue> selectZzLicenseCatalogueListNoPages();

    /**
     * 获取全量公司信息列表
     * @modelType 模块类型
     * @return
     */
    List<SysUnit> getAllCompany(String modelType);

    /**
     * 根据目录id判断当前目录下是否已存在相同的证照系统编号
     * @param zzLicenseMoveVo
     * @return
     */
    List<ZzLicenseMain> selectLicenseInfoByCatalogueId(ZzLicenseMoveVo zzLicenseMoveVo);

    /**
     * 新增证照目录时可选的所属部门按新权限处理
     * @return
     */
    Map<String,Object> authTreeList();

    /**
     * 查询当前用户所有有权限的目录id集合
     * 证照列表使用
     * @return
     */
    List<ZzLicenseCatalogue> selectCatalogueLicenseList();

}