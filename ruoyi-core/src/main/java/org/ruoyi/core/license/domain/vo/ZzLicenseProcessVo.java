package org.ruoyi.core.license.domain.vo;

import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.List;

@Data
public class ZzLicenseProcessVo {
    /** 主键 */
    private Long id;

    /** 证照id */
    @Excel(name = "证照id")
    private String licenseId;

    private String licenseName;

    /** 流程id */
    @Excel(name = "流程id")
    private String processId;

    /** 流程状态(0已完结 1未完结) */
    @Excel(name = "流程状态(0已完结 1未完结)")
    private String processStatus;

    /** 借用的证照id集合 */
    private List<String> licenseIdList;

    /** 是否签领 */
    private String isSign;


    /** 证照系统编号 */
    private String licenseSystemCode;
}
