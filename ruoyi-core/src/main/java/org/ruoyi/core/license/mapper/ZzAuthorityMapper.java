package org.ruoyi.core.license.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.license.domain.ZzAuthority;
import org.ruoyi.core.license.domain.vo.ZzInitCatalogueAuthority;
import org.ruoyi.core.license.domain.vo.ZzLicenseCatalogueVo;
import org.ruoyi.core.license.domain.vo.ZzUserPostVo;
import org.ruoyi.core.xmglproject.domain.AuthDetailVo;

/**
 * 证照授权Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-25
 */
@Mapper
public interface ZzAuthorityMapper 
{
    /**
     * 查询证照授权
     * 
     * @param billId 证照授权主键
     * @return 证照授权
     */
    public ZzAuthority selectZzAuthorityByBillId(Long billId);

    /**
     * 查询证照授权列表
     * 
     * @param zzAuthority 证照授权
     * @return 证照授权集合
     */
    public List<ZzAuthority> selectZzAuthorityList(ZzAuthority zzAuthority);

    /**
     * 新增证照授权
     * 
     * @param zzAuthority 证照授权
     * @return 结果
     */
    public int insertZzAuthority(@Param("zzAuthority") List<ZzAuthority> zzAuthority);

    /**
     * 修改证照授权
     * 
     * @param zzAuthority 证照授权
     * @return 结果
     */
    public int updateZzAuthority(ZzAuthority zzAuthority);

    /**
     * 删除证照授权
     * 
     * @param billId 证照授权主键
     * @return 结果
     */
    public int deleteZzAuthorityByBillId(Long billId);

    /**
     * 批量删除证照授权
     * 
     * @param billIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteZzAuthorityByBillIds(Long[] billIds);

    /**
     * 根据目录id查询授权数据
     * @param billId
     * @return
     */
    List<ZzAuthority> queryZzAuthorityListByBillId(Long billId);

    /**
     * 根据目录id和授权类型删除数据
     * @param billId
     * @param authorityType
     * @return
     */
    int deleteZzAuthorityByBillIdAndAuthorityType(@Param("billId") Long billId, @Param("authorityType") String authorityType);

    /**
     * 新权限-数据初始化
     * 根据授权类型查询授权记录
     * @param authType
     * @return
     */
    List<ZzAuthority> selectZzAuthorityListByType(@Param("authType") String authType, @Param("ord") String ord);

    /**
     * 根据部门id查询有效的用户集合
     * @param authorityId
     * @return
     */
    List<ZzInitCatalogueAuthority> selectZzAuthorityByDeptId(@Param("authorityId") Long authorityId, @Param("type") String type);

    /**
     * 查询用户是否有公司证照权限
     * @param userId 用户id
     * @param companyId 公司id
     * @param moduleType 模块类型
     * @param roleType 角色类型
     * @return
     */
    List<AuthDetailVo> selectEffectiveAuthorityByUserIdAndCompanyId(@Param("userId")Long userId, @Param("companyId")Long companyId, @Param("moduleType")String moduleType, @Param("roleType")String roleType);

    /**
     * 根据关联权限id查询数据
     * @param authorityId
     * @return
     */
    List<ZzLicenseCatalogueVo> selectAuthorityByAuthorityId(@Param("authorityId") Long authorityId, @Param("type") String type);

    /**
     * 根据目录id集合查询目录所属公司
     * @param userAuthority
     * @return
     */
    List<ZzInitCatalogueAuthority> queryCatalogueInfoListByIds(@Param("userAuthority") List<ZzAuthority> userAuthority);

    /**
     * 根据用户id查询主岗位信息
     * @param userId
     * @return
     */
    ZzUserPostVo selectLoginUserInfo(Long userId);
}
