package org.ruoyi.core.license.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class ZzPendingDetailVo {
    /** id */
    private Long id;

    /** 流程id */
    private String businessId;

    /** 证照id */
    private String licenseId;

    /** 证照名称 */
    private String licenseName;

    /** 证照是否在库状态 */
    private String licenseStatus;

    /** 主题 */
    private String themes;

    /** 所属公司(借用人) */
    private Long pertainCompanyId;

    /** 所属公司名称 */
    private String pertainCompanyName;

    /** 借用人 */
    private String borrowPerson;

    /** 签领收回状态(1待签领;2审核中;3已签领;4不签领;5:已收回) */
    private String signStatus;

    /** 签领收回状态字典值 */
    private String signStatusLabel;

    /** 证照状态(1未到期，2即将到期，3已到期) */
    private String backTimeStatus;

    /** 证照状态字典值 */
    private String backTimeStatusLabel;

    /** 借用开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "借用开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date borrowStartTime;

    /** 借用结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "借用结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date borrowEndTime;

    /** 不签领原因 */
    private String reason;

    /** 签领时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签领时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date isSignTime;

    /** 发证日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签领时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date issuingTime;

    /** 保管人姓名 */
    private String costodyPerson;

    /** 版本号 */
    private Integer version;

    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
