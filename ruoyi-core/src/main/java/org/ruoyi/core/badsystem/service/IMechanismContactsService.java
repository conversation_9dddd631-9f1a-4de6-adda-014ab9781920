package org.ruoyi.core.badsystem.service;

import org.ruoyi.core.badsystem.domain.MechanismContacts;

import java.util.List;

/**
 * 机构-联系人Service接口
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
public interface IMechanismContactsService
{
    /**
     * 查询机构-联系人
     *
     * @param id 机构-联系人主键
     * @return 机构-联系人
     */
    public MechanismContacts selectMechanismContactsById(Long id);

    /**
     * 查询机构-联系人列表
     *
     * @param mechanismContacts 机构-联系人
     * @return 机构-联系人集合
     */
    public List<MechanismContacts> selectMechanismContactsList(MechanismContacts mechanismContacts);

    /**
     * 新增机构-联系人
     *
     * @param mechanismContacts 机构-联系人
     * @return 结果
     */
    public int insertMechanismContacts(MechanismContacts mechanismContacts);

    /**
     * 修改机构-联系人
     *
     * @param mechanismContacts 机构-联系人
     * @return 结果
     */
    public int updateMechanismContacts(MechanismContacts mechanismContacts);

    /**
     * 批量删除机构-联系人
     *
     * @param ids 需要删除的机构-联系人主键集合
     * @return 结果
     */
    public int deleteMechanismContactsByIds(Long[] ids);

    /**
     * 删除机构-联系人信息
     *
     * @param id 机构-联系人主键
     * @return 结果
     */
    public int deleteMechanismContactsById(Long id);

    public int batchMechanismContacts(List<MechanismContacts> mechanismContacts);

    public int deleteByMechanismId(Long mechanismId);
}
