package org.ruoyi.core.kaohe.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import org.ruoyi.core.kaohe.domain.CheckConfigTemplateSlave;
import org.ruoyi.core.kaohe.service.ICheckConfigTemplateSlaveService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 考核配置模版从Controller
 *
 * <AUTHOR>
 * @date 2024-07-30
 */
@RestController
@RequestMapping("/check/config/template/slave")
public class CheckConfigTemplateSlaveController extends BaseController
{
    @Autowired
    private ICheckConfigTemplateSlaveService checkConfigTemplateSlaveService;

    /**
     * 查询考核配置模版从列表
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:list')")
    @GetMapping("/list")
    public TableDataInfo list(CheckConfigTemplateSlave checkConfigTemplateSlave)
    {
        startPage();
        List<CheckConfigTemplateSlave> list = checkConfigTemplateSlaveService.selectCheckConfigTemplateSlaveList(checkConfigTemplateSlave);
        return getDataTable(list);
    }

    /**
     * 导出考核配置模版从列表
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:export')")
    @Log(title = "考核配置模版从", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, CheckConfigTemplateSlave checkConfigTemplateSlave)
    {
        List<CheckConfigTemplateSlave> list = checkConfigTemplateSlaveService.selectCheckConfigTemplateSlaveList(checkConfigTemplateSlave);
        ExcelUtil<CheckConfigTemplateSlave> util = new ExcelUtil<CheckConfigTemplateSlave>(CheckConfigTemplateSlave.class);
        util.exportExcel(response, list, "考核配置模版从数据");
    }

    /**
     * 获取考核配置模版从详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(checkConfigTemplateSlaveService.selectCheckConfigTemplateSlaveById(id));
    }

    /**
     * 新增考核配置模版从
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:add')")
    @Log(title = "考核配置模版从", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody CheckConfigTemplateSlave checkConfigTemplateSlave)
    {
        return toAjax(checkConfigTemplateSlaveService.insertCheckConfigTemplateSlave(checkConfigTemplateSlave));
    }

    /**
     * 修改考核配置模版从
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:edit')")
    @Log(title = "考核配置模版从", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody CheckConfigTemplateSlave checkConfigTemplateSlave)
    {
        return toAjax(checkConfigTemplateSlaveService.updateCheckConfigTemplateSlave(checkConfigTemplateSlave));
    }

    /**
     * 删除考核配置模版从
     */
    //@PreAuthorize("@ss.hasPermi('system:slave:remove')")
    @Log(title = "考核配置模版从", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(checkConfigTemplateSlaveService.deleteCheckConfigTemplateSlaveByIds(ids));
    }
}
