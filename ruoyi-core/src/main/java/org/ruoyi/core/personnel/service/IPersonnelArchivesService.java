package org.ruoyi.core.personnel.service;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysDept;
import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.personnel.domain.PersonnelArchives;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesExcel;
import org.ruoyi.core.personnel.domain.vo.PersonnelArchivesVo;
import org.ruoyi.core.personnel.domain.vo.PersonnelOrganizationVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 人员入职Service接口
 *
 * <AUTHOR>
 * @date 2023-12-25
 */
/**
 * 人员档案Service接口
 *
 * <AUTHOR>
 * @date 2024-01-02
 */
public interface IPersonnelArchivesService
{
    /**
     * 查询人员档案
     *
     * @param id 人员档案主键
     * @return 人员档案
     */
    public PersonnelArchives selectPersonnelArchivesById(Long id);

    public String getDeptBreadcrumb(SysDept sysDept);

    /**
     * 查询人员档案列表
     *
     * @param personnelArchivesVo 人员档案
     * @return 人员档案集合
     */
    public List<PersonnelArchivesVo> selectPersonnelArchivesList(PersonnelArchivesVo personnelArchivesVo);

    /**
     * 新增人员档案
     *
     * @param personnelArchives 人员档案
     * @return 结果
     */
    public int insertPersonnelArchives(PersonnelArchives personnelArchives);

    /**
     * 修改人员档案
     *
     * @param personnelArchives 人员档案
     * @return 结果
     */
    public int updatePersonnelArchives(PersonnelArchives personnelArchives);

    /**
     * 批量删除人员档案
     *
     * @param ids 需要删除的人员档案主键集合
     * @return 结果
     */
    public int deletePersonnelArchivesByIds(Long[] ids);

    /**
     * 删除人员档案信息
     *
     * @param id 人员档案主键
     * @return 结果
     */
    public int deletePersonnelArchivesById(Long id);

    /**
     * 批量新增
     * @param personnelArchivesList
     */
    public int batchInsert(List<PersonnelArchives> personnelArchivesList);

    public String importData(List<PersonnelArchivesExcel> personnelArchivesList);

    public List<PersonnelArchives> selectListByIdCard(String[] idCards);

    public int getCountByIdCard(String idCard);

    public int getCountByCreateTime(String createTime);

    public List<PersonnelArchivesVo> selectPersonnelArchivesListForTransfer(PersonnelArchivesVo personnelArchives);

    /**
     * 查询人员未转正列表
     *
     * @param personnelArchives 人员档案
     * @return 人员档案集合
     */
    public List<PersonnelArchives> getFormalBeforeList(PersonnelArchivesVo personnelArchives);

    public AjaxResult uploadFile(MultipartFile file);

    public List<PersonnelArchivesVo> listForResignation(PersonnelArchivesVo personnelArchivesVo);


    public int updatePersonnelStateBySysName(PersonnelArchives personnelArchives);

    public Map<String,List<Long>> getDataRange(LoginUser loginUser);

    public Map<String, List<Long>> getDataRangeFilterRoleKey(LoginUser loginUser,String roleKey);

    public List<PersonnelArchivesVo> exportList(PersonnelArchivesVo personnelArchivesVo);

    public PersonnelArchivesVo subordinate(Long principalId);

    public List<PersonnelOrganizationVo> getPersonnelOrganizationList();

    public List<PersonnelOrganizationVo> getPersonnelOrganizationOfSelf();

    public List<PersonnelArchivesVo> selectListOfMonthLog();

    public List<PersonnelArchivesVo> selectListOfMonthLogUserId(Long userId);

    public List<PersonnelArchivesVo> getSubordinateList();

    /**
     * 查询当前登录用户的下级、下下级、下下下级...
     * @param userName
     * @return
     */
    PersonnelArchivesVo getPersonnelOrganization(String userName);

    List<PersonnelArchivesVo> getPerOrganizationList(PersonnelArchivesVo vos);

    PersonnelArchivesVo subordinateForAgencyAuth();

    PersonnelArchivesVo selectPersonnelArchivesByName(String userName);

    PersonnelArchivesVo selectPersonnelArchivesInfoBysysName(String sysName);

    public List<PersonnelArchivesVo> selectPersonnelArchivesVoList(PersonnelArchivesVo personnelArchivesVo);

    public List<PersonnelArchives> selectListBySysNames(String[] sysNames);
}
