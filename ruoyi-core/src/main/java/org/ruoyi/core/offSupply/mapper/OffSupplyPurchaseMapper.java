package org.ruoyi.core.offSupply.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.offSupply.domain.OffSupplyPurchase;

/**
 * 办公用品采购单Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-03-24
 */
public interface OffSupplyPurchaseMapper 
{
    /**
     * 查询办公用品采购单
     * 
     * @param id 办公用品采购单主键
     * @return 办公用品采购单
     */
    public OffSupplyPurchase selectOffSupplyPurchaseById(Long id);

    /**
     * 查询办公用品采购单列表
     * 
     * @param offSupplyPurchase 办公用品采购单
     * @return 办公用品采购单集合
     */
    public List<OffSupplyPurchase> selectOffSupplyPurchaseList(OffSupplyPurchase offSupplyPurchase);

    /**
     * 新增办公用品采购单
     * 
     * @param offSupplyPurchase 办公用品采购单
     * @return 结果
     */
    public int insertOffSupplyPurchase(OffSupplyPurchase offSupplyPurchase);

    /**
     * 修改办公用品采购单
     * 
     * @param offSupplyPurchase 办公用品采购单
     * @return 结果
     */
    public int updateOffSupplyPurchase(OffSupplyPurchase offSupplyPurchase);

    /**
     * 删除办公用品采购单
     * 
     * @param id 办公用品采购单主键
     * @return 结果
     */
    public int deleteOffSupplyPurchaseById(Long id);

    /**
     * 批量删除办公用品采购单
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOffSupplyPurchaseByIds(Long[] ids);

    /**
     * 根据流程id查询办公用品采购单
     * @param processId 流程id
     * @return 结果
     */
    List<OffSupplyPurchase> selectOffSupplyPurchaseByProcessId(String processId);

    /**
     * 根据采购单id数组批量更新状态
     * @param ids
     * @param processId
     * @param companyId
     */
    int updateOffSupplyPurchaseByIds(@Param("ids") Long[] ids, @Param("processId") String processId, @Param("companyId") Long companyId, @Param("status") String status);

    /**
     * 更新采购单状态状态
     * @param supplyPurchase
     */
    void updateOffSupplyPurchaseStatus(OffSupplyPurchase supplyPurchase);
}
