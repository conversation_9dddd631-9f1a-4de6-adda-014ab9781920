package org.ruoyi.core.yybbsc.domain;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 收入预测报-还款信息对象 sts_income_forecast_repayment_info
 *
 * <AUTHOR>
 * @date 2023-01-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class StsIncomeForecastRepaymentInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 预留产品代码字段：0-富邦 */
    private String productNo;

    /** 放款月份 */
    private String loanMonth;

    /** 0-无，1-随借随还，2-等本等息，3-先息后本 */
    @Excel(name = "0-无，1-随借随还，2-等本等息，3-先息后本")
    private String productType;

    /** 期数 */
    @Excel(name = "期数")
    private Integer phase;

    /** 还款月份 */
    @Excel(name = "还款月份")
    private String repaymentMonth;

    /** 导入月份 */
    @Excel(name = "导入月份")
    private String importMonth;

    /** 还款本金 */
    @Excel(name = "还款本金")
    private BigDecimal repaymentPrintAmount;

    /** 还款利息 */
    @Excel(name = "还款利息")
    private BigDecimal repaymentIntAmount;

    /** 还款罚息 */
    @Excel(name = "还款罚息")
    private BigDecimal repaymentOintAmt;

    /** 还款复利 */
    @Excel(name = "还款复利")
    private BigDecimal repaymentFlAmt;

    /** 提前还款违约金 */
    @Excel(name = "提前还款违约金")
    private BigDecimal advDefineAmt;

    /** 活动抵扣金额 */
    @Excel(name = "活动抵扣金额")
    private BigDecimal deductAmt;

    /** 红线减免金额 */
    @Excel(name = "红线减免金额")
    private BigDecimal reduceAmt;
}
