package org.ruoyi.core.yybbsc.domain.dto;

import lombok.*;

import java.math.BigDecimal;

/**
 * 读取流的业务对象 - 代偿明细表
 *
 * <AUTHOR>
 * @date 2023-01-17
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Builder
public class CompensatoryInputStreamDto {

    private static final long serialVersionUID = 1L;

    /** 放款月份 */
    private String loanMonth;

    /** 代偿月份(与还款月份一致) */
    private String compensatoryMonth;

    /** 合计 */
    private BigDecimal totalAmount;
}
