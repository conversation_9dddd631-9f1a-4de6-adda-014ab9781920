package org.ruoyi.core.yybbsc.mapper;

import org.ruoyi.core.yybbsc.domain.StsIncomeForecastInfo;
import org.ruoyi.core.yybbsc.domain.StsIncomeForecastInfoCompensatory;
import org.ruoyi.core.yybbsc.domain.StsIncomeForecastLoanInfo;
import org.ruoyi.core.yybbsc.domain.StsIncomeForecastRepaymentInfo;
import org.ruoyi.core.yybbsc.domain.dto.StsIncomeForecastTotalDtoPart1;
import org.ruoyi.core.yybbsc.domain.dto.StsIncomeForecastTotalDtoPart2;
import org.ruoyi.core.yybbsc.domain.dto.TechnicalServiceFee;
import org.ruoyi.core.yybbsc.domain.vo.StsIncomeForecastLoanVo;
import org.ruoyi.core.yybbsc.domain.vo.StsIncomeForecastRepayMonthVo;

import java.util.List;

/**
 * 收入预测报Mapper接口
 *
 * <AUTHOR>
 * @date 2023-01-16
 */
public interface StsIncomeForecastMapper {
    /**
     * 新增放款信息
     *
     * @param stsIncomeForecastLoanInfo 收入预测报-放款信息
     * @return 结果
     */
    public int insertStsIncomeForecastLoanInfo(StsIncomeForecastLoanInfo stsIncomeForecastLoanInfo);

    /**
     * 新增收入预测信息
     *
     * @param stsIncomeForecastRepaymentInfo 收入预测报-还款信息
     * @return 结果
     */
    public int insertStsIncomeForecastRepaymentInfo(StsIncomeForecastRepaymentInfo stsIncomeForecastRepaymentInfo);

    /**
     * 新增还款信息
     *
     * @param stsIncomeForecastInfo 收入预测报-还款信息
     * @return 结果
     */
    public int insertStsIncomeForecastInfo(StsIncomeForecastInfo stsIncomeForecastInfo);

    /**
     * 新增代偿信息
     *
     * @param stsIncomeForecastInfoCompensatory 收入预测报-代偿信息
     * @return 结果
     */
    int insertStsIncomeForecastInfoCompensatory(StsIncomeForecastInfoCompensatory stsIncomeForecastInfoCompensatory);

    /**
     * 查所有预测表的数据
     *
     * @return 结果
     */
    List<StsIncomeForecastInfo> queryAllInfo();

    /**
     * 根据放款月份查放款信息表和还款信息表
     *
     * @param loanMonth 还款月份
     * @return 结果
     */
    List<StsIncomeForecastLoanVo> queryLoanInfoAndRepayInfoByLoanMonth(String loanMonth);

    /**
     * 根据放款月份查info表和代偿信息表
     *
     * @param loanMonth 还款月份
     * @return 结果
     */
    List<StsIncomeForecastRepayMonthVo> queryInfoAndInfoCompensatoryByLoanMonth(String loanMonth);

    /**
     * 进入页面首先查库里已有的数据的放款月份供用户进行选择查询
     *
     * @return 结果
     */
    List<String> queryLoanMonthDateFirst();

    List<StsIncomeForecastLoanVo> queryAllLoanInfoAndRepayInfo();

    List<StsIncomeForecastRepayMonthVo> queryAllRepayInfoAndInfo();

    List<String> queryLoanMonthDateFirst1();

    List<StsIncomeForecastTotalDtoPart1> queryAllTotalInfoPart1();

    List<StsIncomeForecastTotalDtoPart2> queryAllTotalInfoPart2();

    List<TechnicalServiceFee> queryAllInfoTechnicalServiceFee();

    /**
     * 通过放款月份删除导入的放款信息
     *
     * @param loanMonth 放款月份
     * @return 结果
     */
    int deleteLoanInfoByLoanMonth(String loanMonth);

    /**
     * 通过还款月份删除导入的还款信息
     *
     * @param repaymentMonth 还款月份
     * @return 结果
     */
    int deleteRepayInfoByRepaymentMonth(String repaymentMonth);

    /**
     * 通过导入月份删除导入的还款信息
     *
     * @param importMonth 还款月份
     * @return 结果
     */
    int deleteRepayInfoByImportMonth(String importMonth);

    /**
     * 通过还款月份删除导入的info表信息
     *
     * @param repaymentMonth 还款月份
     * @return 结果
     */
    int deleteInfoByLoanMonth(String repaymentMonth);

    /**
     * 通过代偿月份删除导入的代偿表信息
     *
     * @param compensatoryMonth 代偿月份
     * @return 结果
     */
    int deleteInfoCompensatoryByCompensatoryMonth(String compensatoryMonth);

    /**
     * 查所有还款信息
     *
     * @return 结果
     */
    List<StsIncomeForecastLoanInfo> queryAllLoanInfo();

    /**
     * 查所有放款信息
     *
     * @return 结果
     */
    List<StsIncomeForecastRepaymentInfo> queryAllRepayInfo();

    /**
     * 通过还款月查还款表信息
     *
     * @return 结果
     */
    List<StsIncomeForecastRepaymentInfo> queryRepayinfoByRepaymentMonth(String repaymentMonth);

    /**
     * 通过还款月查info表信息
     *
     * @return 结果
     */
    List<StsIncomeForecastInfo> queryInfoByRepaymentMonth(String repaymentMonth);

    /**
     * 通过还款月查info表和代偿表信息
     *
     * @return 结果
     */
    List<StsIncomeForecastRepayMonthVo> queryInfoAndInfoCompensatoryByRepaymentMonth(String repaymentMonth);

    /**
     * 通过放款月查放款表和还款表的合计
     *
     * @return 结果
     */
    List<StsIncomeForecastLoanVo> querySumLoanInfoAndRepayInfoByLoanMonth(String loanMonth);

    /**
     * 查所有的还款和放款信息
     *
     * @return 结果
     */
    List<StsIncomeForecastLoanVo> querySumLoanInfoAndRepayInfoAll();

    /**
     * 通过导入文件的月份查是否有之前的放款款月
     *
     * @return 结果
     */
    String queryLoanMonthByImportMonth(String importMonth);

    /**
     * 查所有放款月
     *
     * @return 结果
     */
    List<String> queryLoanMonthDateFirstStr();
}
