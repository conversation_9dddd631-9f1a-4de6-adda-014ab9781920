package org.ruoyi.core.xmglproject.mapper;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.xmglproject.domain.XmglProjectCompanyRelevance;
import org.ruoyi.core.xmglproject.domain.XmglProjectTypeRelevance;

import java.util.List;
public interface XmglRelevanceMapper {


    int insertProjectCompanyRelevanceList(@Param("xmglProjectCompanyRelevanceList") List<XmglProjectCompanyRelevance> xmglProjectCompanyRelevanceList);

    int insertProjectTypeRelevanceList(@Param("xmglProjectTypeRelevanceList")List<XmglProjectTypeRelevance> xmglProjectTypeRelevanceList);

    List<XmglProjectCompanyRelevance> queryDataObjectByTypeAndId(@Param("dataType") String dataType, @Param("id") Long id);

    List<XmglProjectCompanyRelevance> queryDataObjectByProjectId(@Param("projectId") Long projectId);

    List<XmglProjectTypeRelevance> queryProjectObject(@Param("dataType") String dataType, @Param("id") Long id);

    List<XmglProjectTypeRelevance> getTypeByProjectId(Long projectId);

    List<XmglProjectTypeRelevance> getTypeByProjectIds(List<Long> projectIds);
    /**
     * 删除担保公司/资产方/资金方/其他公司关联关系
     * @param deployId
     * @param projectId
     * @return
     */
    int deleteProjectCompanyRelevance(@Param("deployId") Long deployId, @Param("projectId") Long projectId);

    /**
     * 删除项目类型/业务类型关联关系
     * @param deployId 项目名称id
     * @param projectId 立项项目id
     * @return
     */
    int deleteProjectTypeRelevance(@Param("deployId") Long deployId, @Param("projectId") Long projectId);
}
