package org.ruoyi.core.dm.mapper;

import org.ruoyi.core.dm.domain.DmAqaFpdAnalysis;
import org.ruoyi.core.dm.domain.vo.DmAqaCpstVintageAnalysisVo;
import org.ruoyi.core.dm.domain.vo.DmAqaFpdAnalysisVo;
import org.ruoyi.core.domain.DProjectParameter;

import java.util.List;

/**
 * 产品首逾率Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-23
 */
public interface DmAqaFpdAnalysisMapper
{
    /**
     * 查询产品首逾率
     *
     * @param id 产品首逾率主键
     * @return 产品首逾率
     */
    public DmAqaFpdAnalysis selectDmAqaFpdAnalysisById(Long id);

    /**
     * 查询产品首逾率列表
     *
     * @param dmAqaFpdAnalysis 产品首逾率
     * @return 产品首逾率集合
     */
    public List<DmAqaFpdAnalysisVo> selectDmAqaFpdAnalysisList(DmAqaFpdAnalysisVo dmAqaFpdAnalysis);

    /**
     * 新增产品首逾率
     *
     * @param dmAqaFpdAnalysis 产品首逾率
     * @return 结果
     */
    public int insertDmAqaFpdAnalysis(DmAqaFpdAnalysis dmAqaFpdAnalysis);

    /**
     * 修改产品首逾率
     *
     * @param dmAqaFpdAnalysis 产品首逾率
     * @return 结果
     */
    public int updateDmAqaFpdAnalysis(DmAqaFpdAnalysis dmAqaFpdAnalysis);

    /**
     * 删除产品首逾率
     *
     * @param id 产品首逾率主键
     * @return 结果
     */
    public int deleteDmAqaFpdAnalysisById(Long id);

    /**
     * 批量删除产品首逾率
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDmAqaFpdAnalysisByIds(Long[] ids);

    public List<DProjectParameter> selectDProjectParameter(DmAqaFpdAnalysisVo dmAqaFpdAnalysisVo);
}
