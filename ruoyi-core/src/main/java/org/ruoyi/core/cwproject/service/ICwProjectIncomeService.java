package org.ruoyi.core.cwproject.service;

import com.ruoyi.common.core.domain.model.LoginUser;
import org.ruoyi.core.cwproject.domain.AddCwProjectIncomeAndFee;
import org.ruoyi.core.cwproject.domain.CwProject;
import org.ruoyi.core.cwproject.domain.CwProjectIncome;
import org.ruoyi.core.cwproject.domain.dto.CwProjectIncomeForLawDto;
import org.ruoyi.core.cwproject.domain.projectVO.AddFeePay;
import org.ruoyi.core.cwproject.domain.projectVO.AddParojectVo;
import org.ruoyi.core.cwproject.domain.projectVO.AddfeeVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 财务项目管理-收入Service接口
 *
 * <AUTHOR>
 * @date 2022-11-09
 */
public interface ICwProjectIncomeService
{
    /**
     * 查询财务项目管理-收入
     *
     * @param id 财务项目管理-收入主键
     * @return 财务项目管理-收入
     */
    public CwProjectIncome selectCwProjectIncomeById(Long id);

    /**
     * 查询财务项目管理-收入列表
     *
     * @param cwProjectIncome 财务项目管理-收入
     * @return 财务项目管理-收入集合
     */
    public List<CwProjectIncome> selectCwProjectIncomeList(CwProjectIncome cwProjectIncome);

    /**
     * 新增财务项目管理-收入
     *
     * @param cwProjectIncome 财务项目管理-收入
     * @return 结果
     */
    public int insertCwProjectIncome(CwProjectIncome cwProjectIncome);

    /**
     * 修改财务项目管理-收入
     *
     * @param cwProjectIncome 财务项目管理-收入
     * @return 结果
     */
    public int updateCwProjectIncome(CwProjectIncome cwProjectIncome);

    /**
     * 批量删除财务项目管理-收入
     *
     * @param ids 需要删除的财务项目管理-收入主键集合
     * @return 结果
     */
    public int deleteCwProjectIncomeByIds(Long[] ids);

    /**
     * 删除财务项目管理-收入信息
     *
     * @param id 财务项目管理-收入主键
     * @return 结果
     */
    public int deleteCwProjectIncomeById(Long id);

    /**
     * 查询财务项目管理-待录入收入状态
     *
     * @param cwProjectIncome 财务项目管理-收入
     * @return 财务项目管理-收入集合
     */
    List<Map<String, Object>> selectCwProjectIncomeListFlagZero(CwProjectIncome cwProjectIncome, Long userId, LoginUser loginUser, List<Long> projectIds);

    /**
     * 查询财务项目管理-已录入未确认收入状态
     *
     * @param cwProjectIncome 财务项目管理-收入
     * @return 财务项目管理-收入集合
     */
    List<Map<String, Object>> selectCwProjectIncomeListFlagOne(CwProjectIncome cwProjectIncome, Long userId, LoginUser loginUser,List<Long> projectIds);

    List<CwProjectIncome> selectCwProjectIncomeListAll();

    Map<String, Object> addIncomeQiCi(CwProjectIncome cwProjectIncome, LoginUser loginUser);

    boolean updateIncomeQiCi(CwProjectIncome cwProjectIncome, LoginUser loginUser);

    boolean addIncomeShouRu(CwProjectIncome cwProjectIncome, LoginUser loginUser);


    boolean updateIncomeShouRu(CwProjectIncome cwProjectIncome, String username);

    boolean updateSubmitStatus(CwProjectIncome cwProjectIncome,LoginUser loginUser);

    boolean updateYewuUser(AddParojectVo addParojectVo, String username);

    boolean addFeeList(AddCwProjectIncomeAndFee addCwProjectIncomeAndFee, LoginUser loginUser);

    boolean updateFeeList(AddfeeVO addfeeVO, String username);

    boolean querenFee(CwProjectIncome cwProjectIncome, LoginUser loginUser);

    boolean addFeePayList(AddFeePay addFeePay, String username);

    List getyewuByIdList(CwProject cwProject);

    boolean querenDakuan(CwProjectIncome cwProjectIncome, LoginUser loginUser);

    /**
     * 新增期次 - 法催项目
     */
    Map<String, Object> addQiCiForLaw(CwProjectIncome cwProjectIncome, LoginUser loginUser);

    /**
     * 新增收入 - 法催项目
     */
    boolean addIncomeForLaw(List<CwProjectIncomeForLawDto> cwProjectIncomeForLawDtoList, LoginUser loginUser);

    /**
     * 确认收入与返费 - 法催项目
     */
    boolean querenIncomeAndFeeForLaw(CwProjectIncome cwProjectIncome, LoginUser loginUser);

    /**
     * 新增打款 - 法催项目
     */
    boolean addLawFeePayList(AddFeePay addFeePay, String username);

    /**
     * 确认打款 - 法催项目
     */
    boolean querenDakuanFowLaw(CwProjectIncome cwProjectIncome, LoginUser loginUser);

    /**
     * 删除期次 - 法催项目
     */
    int updateCwProjectIncomeLaw(CwProjectIncome cwProjectIncome);

    //法催项目 - 通过期次id删除期次下的收入以及返费和动态表和通知表信息
    int deleteCwProjectIncomeByPhaseId(Long phaseId);

    //普通项目 - 驳回收入金额
    boolean rejectionIncome(CwProjectIncome cwProjectIncome, LoginUser loginUser);

    //普通项目 - 驳回收入后修改收入金额
    boolean updateIncomeSubmit(AddCwProjectIncomeAndFee addCwProjectIncomeAndFee, LoginUser loginUser);

    //普通项目 - 驳回返费
    boolean rejectionFee(CwProjectIncome cwProjectIncome, LoginUser loginUser);

    //普通项目 - 驳回收入后修改返费信息
    boolean updateFeeSubmit(AddfeeVO addfeeVO, LoginUser loginUser);

    //普通项目 - 修改收入与返费
    boolean updateIncomeAndFee(AddCwProjectIncomeAndFee addCwProjectIncomeAndFee, LoginUser loginUser);

    public void createProof(Long incomeId,String status);

    public void importExcelOfCwProject(MultipartFile file) throws Exception;
}