package org.ruoyi.core.cwproject.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 财务项目管理-法催收入与返费对象
 *
 * <AUTHOR>
 * @date 2023-02-16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CwProjectIncomeForLawDto extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private int hebingId;

    /** 收入表主键 */
    private Long projectIncomeId;

    /** 返费公司表主键 */
    private Long custId;

    private Long feeId;

    private String term;

    /** 项目管理表主键 */
//    @Excel(name = "项目管理表主键")
    private Long projectId;

    /** 业务期次所属月份 */
//    @Excel(name = "业务期次所属月份")
    private String termMonth;

    /** 服务商名称 */
//    @Excel(name = "业务期次所属月份")
    private String serviceProviderName;

    /** 服务商收入金额 元 */
//    @Excel(name = "服务商收入金额 元")
    private BigDecimal serviceProviderIncome;

    /** 二级服务商名称 */
    private String serviceProviderSecondName;

    /** 二级服务商收入金额 元 */
    private BigDecimal serviceProviderSecondIncome;

    /** 出返费公司名称 */
    private String custName;

    /** 返费公司名称 */
    private String feeCustName;

    /** 真实回款金额 元 */
    private BigDecimal trueComeAmt;

    /** 服务费 元 */
    private BigDecimal serviceFee;

    /** 本金 元 */
    private BigDecimal principal;

    /** 返费 元 */
    private BigDecimal feeAmt;

    /** 返费取整 元 */
    private String feeRound;
    private BigDecimal feeRoundDecimal;

    /** 提成返费 元 */
    private String feeAmt2;
    private BigDecimal feeAmt2Decimal;

    /** 毛利金额 元 */
//    @Excel(name = "毛利金额 元")
    private BigDecimal grossProfitAmt;

    /** 提成毛利金额 元 */
//    @Excel(name = "提成毛利金额 元")
    private BigDecimal grossProfitAmt2;

//    /** 返费已结清金额 元 */
//    @Excel(name = "返费已结清金额 元")
//    private BigDecimal feeAmt;
//
//    /** 返费未结清金额 元 */
//    @Excel(name = "返费未结清金额 元")
//    private BigDecimal unfeeAmt;

//    /** 状态，0正常 1禁用 */
//    @Excel(name = "状态，0正常 1禁用")
//    private String status;

//    /** 录入状态，0未录入 1已录入 2已确认 */
//    @Excel(name = "录入状态，0未录入 1已录入 2已确认")
//    private String incomeFlag;

    /** 期次状态：7法催项目-录入收入与返费 8法催项目-确认收入与返费 9法催项目-出纳打款 10法催项目-完成 */
//    @Excel(name = "期次状态：a1已完成;a2未完成;a3待录入;a4待出纳打款")
//    private String phaseStatus;

    /** 期次标识 */
    private String phaseFlag;

    /** 返费标识 */
    private String feeFlag;

    private String serviceProviderFlag;

    /** 备注 */
    private String remark;

    private Long phaseId;

    //返费挂起标识 - fee表入库用
    private String suspendFlag;

    //挂起金额对应的返费id - 字符串类型的 例如："944,927"
    private String feeAmtSuspendFlagIsOneId;

    //本期返费
    private BigDecimal currentFee;

    //累计挂起金额
    private BigDecimal feeAmtSuspendFlagSum;

    //挂起清除标志
    private int guaqiqingchuFlag;

    //挂起清除id
    private Long suspendClearId;

    //收款时间
    @JsonFormat(pattern = "yyyy-MM")
    @Excel(name = "收款时间", width = 30, dateFormat = "yyyy年MM月")
    private Date collectionTime;

    //借条分润
    private BigDecimal jtfrAmt;

    //一级借条分润
    private BigDecimal serviceProviderJtfrAmt;

    //法催利润
    private BigDecimal lawProfit;
}
