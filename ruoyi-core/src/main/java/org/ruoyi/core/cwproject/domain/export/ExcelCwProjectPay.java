package org.ruoyi.core.cwproject.domain.export;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import lombok.Data;

/**
 * 财务项目管理-打款信息对象 cw_project_pay
 * 
 * <AUTHOR>
 * @date 2022-11-10
 */
@Data
public class ExcelCwProjectPay extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目管理表主键 */
    @Excel(name = "项目管理表主键")
    private Long projectId;

    /** 项目收入表主键 */
    @Excel(name = "项目收入表主键")
    private Long projectIncomeId;

    /** 项目返费表主键 */
    @Excel(name = "项目返费表主键")
    private Long projectFeeId;

    /** 打款日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "打款日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date payDate;

    /** 实际打款金额 元 */
    @Excel(name = "实际打款金额 元")
    private BigDecimal payAmt;

    /** 抹平差额 元 */
    @Excel(name = "抹平差额 元")
    private BigDecimal differenceAmt;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;

    /** 打款状态，0未打款 1已打款 2已确认 */
    @Excel(name = "打款状态，0未打款 1已打款 2已确认")
    private String payFlag;

}
