package org.ruoyi.core.cwproject.domain.dto;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.ruoyi.core.cwproject.domain.CwProjectPay;

import java.math.BigDecimal;

/**
 * 财务项目管理-返费对象 cw_project_fee
 * 
 * <AUTHOR>
 * @date 2023-02-20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class CwProjectLawFeeDto extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 项目管理表主键 */
    @Excel(name = "项目管理表主键")
    private Long projectId;

    @Excel(name = "项目管理表主键")
    private Long custId;

    /** 项目收入表主键 */
    @Excel(name = "项目收入表主键")
    private Long projectIncomeId;

    /** 出返费公司 */
    @Excel(name = "出返费公司")
    private String custName;

    /** 返费公司 */
    @Excel(name = "返费公司")
    private String feeCustName;

    /** 计算方式，0自动 1手动 */
    @Excel(name = "计算方式，0自动 1手动")
    private String calculateType;

    /** 返费金额 元 */
    @Excel(name = "返费金额 元")
    private BigDecimal feeAmt;

    /** 提成返费金额 元 */
    @Excel(name = "提成返费金额 元")
    private BigDecimal feeAmt2;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;

    /** 录入状态，0未录入 1已录入 2已确认 */
    @Excel(name = "录入状态，0未录入 1已录入 2已确认")
    private String feeFlag;

    /** 打款信息对象 */
    @Excel(name = "打款信息对象")
    private CwProjectPay cwProjectPay;

    private BigDecimal feeRound;

    private String suspendFlag;

    private String serviceProviderFlag;
    private String serviceProvider;
    private String serviceProviderSecond;

    //分组标识，用来做累加工作
    private Integer serviceGroupFlag;
    //打款标识，用于控制打款按钮
    private String payFlag;
}
