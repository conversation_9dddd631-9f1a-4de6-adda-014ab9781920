package org.ruoyi.core.modules.fcdataquery.controller;

import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.PageUtils;
import org.ruoyi.core.modules.fcdataquery.po.CompensateDetailPo;
import org.ruoyi.core.modules.fcdataquery.po.CompensatePo;
import org.ruoyi.core.modules.fcdataquery.service.IFcCompensateService;
import org.ruoyi.core.modules.fcdataquery.vo.CompensateDetailVo;
import org.ruoyi.core.modules.fcdataquery.vo.CompensateVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 运营部-代偿款Controller
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
@RestController
@RequestMapping("/fc/compensate")
public class CompensateController extends BaseController
{
    @Autowired
    private IFcCompensateService fcCompensateService;

    /**
     * 查询代偿款列表
     */
    @PreAuthorize("@ss.hasPermi('fc:compensate:list')")
    @GetMapping("/list")
    public TableDataInfo list(CompensateVo compensateVo, int pageNum, int pageSize)
    {
        //查询平台方保证金
        List<CompensatePo> compensateList = fcCompensateService.selectCompensateList(compensateVo);
        PageInfo<CompensatePo> pageInfo = PageUtils.getPageInfo(pageNum, pageSize, compensateList);
        TableDataInfo tableDataInfo = getDataTable(pageInfo.getList());
        tableDataInfo.setTotal(pageInfo.getTotal());
        Map<Object, Object> map = new HashMap<>();
        //收到代偿款合计
        map.put("sumReceiveCompensateAmt", compensateList.stream().map(CompensatePo::getReceiveCompensateAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        //实际代偿款合计
        map.put("sumActCompensateAmt", compensateList.stream().map(CompensatePo::getActCompensateAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        //余额合计
        map.put("sumBalanceAmt", compensateList.stream().map(CompensatePo::getBalanceAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        //累计代偿款合计
        map.put("sumAccumCompensateAmt", compensateList.stream().map(CompensatePo::getAccumCompensateAmt).reduce(BigDecimal.ZERO, BigDecimal::add));
        tableDataInfo.setMap(map);
        return tableDataInfo;
    }

    /**
     * <AUTHOR>
     * @Description 获取代偿款详情
     * @Date 2024/11/20 16:13
     * @Param [compensateDetailVo, pageNum, pageSize]
     * @return com.ruoyi.common.core.page.TableDataInfo
     **/
    @PostMapping("/detail")
    public TableDataInfo compensateDetail(@RequestBody CompensateDetailVo compensateDetailVo){
        Integer pageNum = compensateDetailVo.getPageNum();
        Integer pageSize = compensateDetailVo.getPageSize();
        compensateDetailVo.setPageNum(null);
        compensateDetailVo.setPageSize(null);

        List<CompensateDetailPo> compensateDetailPos = fcCompensateService.getCompensateDetail(compensateDetailVo);
        PageInfo<CompensateDetailPo> pageInfo = PageUtils.getPageInfo(pageNum, pageSize, compensateDetailPos);
        TableDataInfo tableDataInfo = getDataTable(pageInfo.getList());
        tableDataInfo.setTotal(pageInfo.getTotal());
        return tableDataInfo;
    }

    /**
     * 导出平台方保证金
     */
    @PreAuthorize("@ss.hasPermi('fc:compensate:export')")
    @Log(title = "经营分析-运营部-代偿款导出", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public List<CompensatePo> export(@RequestBody CompensateVo compensateVo)
    {
        return fcCompensateService.selectCompensateList(compensateVo);
    }
}
