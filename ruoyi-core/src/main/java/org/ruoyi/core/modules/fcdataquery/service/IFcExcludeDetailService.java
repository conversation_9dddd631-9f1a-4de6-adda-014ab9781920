package org.ruoyi.core.modules.fcdataquery.service;

import java.util.List;
import org.ruoyi.core.modules.fcdataquery.domain.FcExcludeDetail;

/**
 * 财务剔除明细Service接口
 * 
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IFcExcludeDetailService 
{
    /**
     * 查询财务剔除明细
     * 
     * @param id 财务剔除明细主键
     * @return 财务剔除明细
     */
    public FcExcludeDetail selectFcExcludeDetailById(Long id);

    /**
     * 查询财务剔除明细列表
     * 
     * @param fcExcludeDetail 财务剔除明细
     * @return 财务剔除明细集合
     */
    public List<FcExcludeDetail> selectFcExcludeDetailList(FcExcludeDetail fcExcludeDetail);

    /**
     * 新增财务剔除明细
     * 
     * @param fcExcludeDetail 财务剔除明细
     * @return 结果
     */
    public int insertFcExcludeDetail(FcExcludeDetail fcExcludeDetail);

    /**
     * 修改财务剔除明细
     * 
     * @param fcExcludeDetail 财务剔除明细
     * @return 结果
     */
    public int updateFcExcludeDetail(FcExcludeDetail fcExcludeDetail);

    /**
     * 批量删除财务剔除明细
     * 
     * @param ids 需要删除的财务剔除明细主键集合
     * @return 结果
     */
    public int deleteFcExcludeDetailByIds(Long[] ids);

    /**
     * 删除财务剔除明细信息
     * 
     * @param id 财务剔除明细主键
     * @return 结果
     */
    public int deleteFcExcludeDetailById(Long id);
}
