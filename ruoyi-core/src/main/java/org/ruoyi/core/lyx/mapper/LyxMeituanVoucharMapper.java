package org.ruoyi.core.lyx.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.ruoyi.core.lyx.domain.LyxMeituanVouchar;

/**
 * 【请填写功能名称】Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-03-14
 */
public interface LyxMeituanVoucharMapper 
{
    /**
     * 查询【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public LyxMeituanVouchar selectLyxMeituanVoucharById(Long id);

    /**
     * 查询【请填写功能名称】列表
     * 
     * @param lyxMeituanVouchar 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<LyxMeituanVouchar> selectLyxMeituanVoucharList(LyxMeituanVouchar lyxMeituanVouchar);

    /**
     * 新增【请填写功能名称】
     * 
     * @param lyxMeituanVouchar 【请填写功能名称】
     * @return 结果
     */
    public int insertLyxMeituanVouchar(LyxMeituanVouchar lyxMeituanVouchar);

    /**
     * 修改【请填写功能名称】
     * 
     * @param lyxMeituanVouchar 【请填写功能名称】
     * @return 结果
     */
    public int updateLyxMeituanVouchar(LyxMeituanVouchar lyxMeituanVouchar);

    /**
     * 删除【请填写功能名称】
     * 
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteLyxMeituanVoucharById(Long id);

    /**
     * 批量删除【请填写功能名称】
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLyxMeituanVoucharByIds(Long[] ids);

    List<LyxMeituanVouchar> selectVoucharByWithId(@Param("id") Long id);
}
