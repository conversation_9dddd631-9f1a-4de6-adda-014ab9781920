package org.ruoyi.core.lyx.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 lyx_revenue_item_dict
 * 
 * <AUTHOR>
 * @date 2024-03-11
 */
public class LyxRevenueItemDict extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 收入项类型（0美团券1非美团券） */
    @Excel(name = "收入项类型", readConverterExp = "0=美团券1非美团券")
    private String type;

    /** 收入项名称 */
    @Excel(name = "收入项名称")
    private String revenueItem;

    private String fieldType;

    /** 状态 */
    @Excel(name = "状态")
    private String status;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRevenueItem() {
        return revenueItem;
    }

    public void setRevenueItem(String revenueItem) {
        this.revenueItem = revenueItem;
    }

    public String getFieldType() {
        return fieldType;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public LyxRevenueItemDict(Long id, String type, String revenueItem, String fieldType, String status) {
        this.id = id;
        this.type = type;
        this.revenueItem = revenueItem;
        this.fieldType = fieldType;
        this.status = status;
    }

    public LyxRevenueItemDict() {
    }

    @Override
    public String toString() {
        return "LyxRevenueItemDict{" +
                "id=" + id +
                ", type='" + type + '\'' +
                ", revenueItem='" + revenueItem + '\'' +
                ", fieldType='" + fieldType + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
