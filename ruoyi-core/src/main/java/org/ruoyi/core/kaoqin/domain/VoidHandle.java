package org.ruoyi.core.kaoqin.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 废弃考勤处理对象 kq_void_handle
 *
 * <AUTHOR>
 * @date 2024-07-05
 */
@Data
public class VoidHandle extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 通知模块 */
    @Excel(name = "通知模块 1.请假 2.加班 3.奖惩")
    private String type;

    /** 关联id */
    @Excel(name = "关联id")
    private Long correlationId;

    /** 状态 1.未处理 2.已处理 */
    @Excel(name = "状态 1.未处理 2.已处理")
    private String listState;

    /** 处理状态 0.同意 1.拒绝 */
    @Excel(name = "处理状态  1.同意 2.拒绝")
    private String handleState;

    /** 拒绝原因 */
    @Excel(name = "拒绝原因")
    private String refuseReason;

    /** 创建者id */
    @Excel(name = "创建者id")
    private Long createById;

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("type", getType())
            .append("correlationId", getCorrelationId())
            .append("handleState", getHandleState())
            .append("refuseReason", getRefuseReason())
            .append("createById", getCreateById())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}
