package org.ruoyi.core.kaoqin.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.ruoyi.core.kaoqin.domain.DayLog;

import java.util.Date;
import java.util.List;

@Data
public class DayLogListVo extends DayLog {
    /** 日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date date;

    /** 填报时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportTime;

    /** 填报人 */
    private String reportBy;

    /** 状态 1.节假日 2.工作日 3.加班 4.请假*/
    private String holiday;

    /** 状态 1.节假日 2.可填报 3.已填报*/
    private String state;

    private List<DayLog> dayLogList;

    private String monthCondition;

    private String monthStart;

    private String monthEnd;
    /**
     * 上报天数
     */
    private String reportingNum;
    /**
     * 应报天数
     */
    private String shouldNum;
    /**
     * 缺报天数
     */
    private String missingNum;
    /**
     * 请假天数
     */
    private String leaveNum;
    /**
     * 加班天数
     */
    private String overtimeNum;

    private String deptName;

    private String nickName;

    private Long deptId;

    private Long userId;

    private List<Long> deptIds;

    private List<Long> unitIds;

    /** 条件:填报时间开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportTimeStart;

    /**  条件:填报时间开始 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date reportTimeEnd;

    /** 条件:日志时间开始 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date logDateStart;

    /**  条件:日志时间开始 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date logDateEnd;

    private List<String> createByList;

    private List<Long> groupUserIdList;

    private List<String> logDateList;

    private List<Date> overtimeList;

    private Long groupId;

    private String processId;
}
