package org.ruoyi.core.kaoqin.service;

import org.ruoyi.core.kaoqin.domain.Notify;
import org.ruoyi.core.kaoqin.domain.VoidHandle;
import org.ruoyi.core.kaoqin.domain.vo.NotifyVo;
import org.ruoyi.core.personnel.domain.vo.ProcessEndTime;

import java.util.List;

/**
 * 月报提醒Service接口
 *
 * <AUTHOR>
 * @date 2024-03-08
 */
public interface INotifyService
{
    /**
     * 查询月报提醒
     *
     * @param id 月报提醒主键
     * @return 月报提醒
     */
    public Notify selectMonthLogNotifyById(Long id);

    /**
     * 查询月报提醒列表
     *
     * @param monthLogNotify 月报提醒
     * @return 月报提醒集合
     */
    public List<Notify> selectMonthLogNotifyList(Notify monthLogNotify);

    /**
     * 新增月报提醒
     *
     * @param monthLogNotify 月报提醒
     * @return 结果
     */
    public int insertMonthLogNotify(Notify monthLogNotify);

    public int insertWorkOverTimeNotify(NotifyVo notify);


    public int insertAskLeaveNotify(NotifyVo notify);

    /**
     * 修改月报提醒
     *
     * @param monthLogNotify 月报提醒
     * @return 结果
     */
    public int updateMonthLogNotify(Notify monthLogNotify);

    /**
     * 批量删除月报提醒
     *
     * @param ids 需要删除的月报提醒主键集合
     * @return 结果
     */
    public int deleteMonthLogNotifyByIds(Long[] ids);

    /**
     * 删除月报提醒信息
     *
     * @param id 月报提醒主键
     * @return 结果
     */
    public int deleteMonthLogNotifyById(Long id);

    public int reviewedNotify(Notify monthLogNotify);

    public int confirmNotify(Notify monthLogNotify);

    List<ProcessEndTime> getProcessEndTime(String businessKey);

     int insertMonthLogNotifyBatch(List<NotifyVo> notifyList);

    public int insertVoidHandle(VoidHandle notifyVo);
}
