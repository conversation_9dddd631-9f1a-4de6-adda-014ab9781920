package org.ruoyi.core.cdlb.service;

import org.ruoyi.core.cdlb.domain.CdlbProjectUser;

import java.util.List;

/**
 * 车贷绿本管理-成员Service接口
 * 
 * <AUTHOR>
 * @date 2023-03-02
 */
public interface ICdlbProjectUserService 
{
    /**
     * 查询车贷绿本管理-成员
     * 
     * @param id 车贷绿本管理-成员主键
     * @return 车贷绿本管理-成员
     */
    public CdlbProjectUser selectCdlbProjectUserById(Long id);

    /**
     * 查询车贷绿本管理-成员列表
     * 
     * @param cdlbProjectUser 车贷绿本管理-成员
     * @return 车贷绿本管理-成员集合
     */
    public List<CdlbProjectUser> selectCdlbProjectUserList(CdlbProjectUser cdlbProjectUser);

    /**
     * 新增车贷绿本管理-成员
     * 
     * @param cdlbProjectUser 车贷绿本管理-成员
     * @return 结果
     */
    public int insertCdlbProjectUser(CdlbProjectUser cdlbProjectUser);

    /**
     * 修改车贷绿本管理-成员
     * 
     * @param cdlbProjectUser 车贷绿本管理-成员
     * @return 结果
     */
    public int updateCdlbProjectUser(CdlbProjectUser cdlbProjectUser);

    /**
     * 批量删除车贷绿本管理-成员
     * 
     * @param ids 需要删除的车贷绿本管理-成员主键集合
     * @return 结果
     */
    public int deleteCdlbProjectUserByIds(Long[] ids);

    /**
     * 删除车贷绿本管理-成员信息
     * 
     * @param id 车贷绿本管理-成员主键
     * @return 结果
     */
    public int deleteCdlbProjectUserById(Long id);
    public int deleteCdlbProjectUserByProjectId(Long id);
}