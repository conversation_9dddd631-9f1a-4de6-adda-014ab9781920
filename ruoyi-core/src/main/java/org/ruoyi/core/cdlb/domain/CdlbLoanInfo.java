package org.ruoyi.core.cdlb.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 车贷借据信息对象 cdlb_loan_info
 * 
 * <AUTHOR>
 * @date 2023-03-09
 */
public class CdlbLoanInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long id;

    /** 是否绑定绿本信息 Y是 N否 */
    @Excel(name = "是否绑定绿本信息 Y是 N否")
    private String cdlbBinding;

    /** 已绑定的绿本信息表ID,未绑定时默认为0 */
    @Excel(name = "已绑定的绿本信息表ID,未绑定时默认为0")
    private Long cdlbId;

    /** 外部系统平台编码 */
    @Excel(name = "外部系统平台编码")
    private String platformNo;

    /** 担保公司编码 */
    @Excel(name = "担保公司编码")
    private String custNo;

    private String custName;

    /** 合作方编码 */
    @Excel(name = "合作方编码")
    private String partnerNo;

    /** 资金方编码 */
    @Excel(name = "资金方编码")
    private String fundNo;

    /** 产品编号 */
    @Excel(name = "产品编号")
    private String productNo;
    /**
     * 产品名称
     */
    private String productName;

    /** 借据申请编号 */
    @Excel(name = "借据申请编号")
    private String loanNo;

    /** 客户姓名 */
    @Excel(name = "客户姓名")
    private String clientName;

    /** 身份证号码 */
    @Excel(name = "身份证号码")
    private String clientCardId;

    /** 身份证地址 */
    @Excel(name = "身份证地址")
    private String clientCardAddress;

    /** 借据状态 */
    @Excel(name = "借据状态")
    private String loanStatus;

    /** 进件时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "进件时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date applyTime;

    /** 借款金额（元） */
    @Excel(name = "借款金额", readConverterExp = "元=")
    private BigDecimal loanAmt;

    /** 在贷余额（元） */
    @Excel(name = "在贷余额", readConverterExp = "元=")
    private BigDecimal balanceAmt;

    /** 借款期限（期数） */
    @Excel(name = "借款期限", readConverterExp = "期=数")
    private Integer totalTerm;

    /** 放款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "放款时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date loanTime;

    /** 到期日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "到期日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dueDate;
  /** 登记日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入库登记日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date registerTime;

    /** 放款流水号 */
    @Excel(name = "放款流水号")
    private String loanReqNo;

    /** 还款方式 */
    @Excel(name = "还款方式")
    private String repayWay;

    /** 借款用途 */
    @Excel(name = "借款用途")
    private String loanUse;

    /** 车辆品牌 */
    @Excel(name = "车辆品牌")
    private String carBrandName;

    /** 车牌号 */
    @Excel(name = "车牌号")
    private String carNo;

    /** 车架号 */
    @Excel(name = "车架号")
    private String carVin;

    /** 状态，0正常 1禁用 */
    @Excel(name = "状态，0正常 1禁用")
    private String status;
    /**
     * 是否办理出库记录 （是：Y，否：N）
     */

    @Excel(name = " 是否办理出库记录 （是：Y，否：N）")
    private String cdlbRecord;


    /** 对帐日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "对帐日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date reconciliationTime;

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    public Date getReconciliationTime() {
        return reconciliationTime;
    }

    public void setReconciliationTime(Date reconciliationTime) {
        this.reconciliationTime = reconciliationTime;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCdlbRecord() {
        return cdlbRecord;
    }

    public void setCdlbRecord(String cdlbRecord) {
        this.cdlbRecord = cdlbRecord;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }
    public void setCdlbBinding(String cdlbBinding) 
    {
        this.cdlbBinding = cdlbBinding;
    }

    public String getCdlbBinding() 
    {
        return cdlbBinding;
    }
    public void setCdlbId(Long cdlbId) 
    {
        this.cdlbId = cdlbId;
    }

    public Long getCdlbId() 
    {
        return cdlbId;
    }
    public void setPlatformNo(String platformNo) 
    {
        this.platformNo = platformNo;
    }

    public String getPlatformNo() 
    {
        return platformNo;
    }
    public void setCustNo(String custNo) 
    {
        this.custNo = custNo;
    }

    public String getCustNo() 
    {
        return custNo;
    }
    public void setPartnerNo(String partnerNo) 
    {
        this.partnerNo = partnerNo;
    }

    public String getPartnerNo() 
    {
        return partnerNo;
    }
    public void setFundNo(String fundNo) 
    {
        this.fundNo = fundNo;
    }

    public String getFundNo() 
    {
        return fundNo;
    }
    public void setProductNo(String productNo) 
    {
        this.productNo = productNo;
    }

    public String getProductNo() 
    {
        return productNo;
    }
    public void setLoanNo(String loanNo) 
    {
        this.loanNo = loanNo;
    }

    public String getLoanNo() 
    {
        return loanNo;
    }
    public void setClientName(String clientName) 
    {
        this.clientName = clientName;
    }

    public String getClientName() 
    {
        return clientName;
    }
    public void setClientCardId(String clientCardId) 
    {
        this.clientCardId = clientCardId;
    }

    public String getClientCardId() 
    {
        return clientCardId;
    }
    public void setClientCardAddress(String clientCardAddress) 
    {
        this.clientCardAddress = clientCardAddress;
    }

    public String getClientCardAddress() 
    {
        return clientCardAddress;
    }
    public void setLoanStatus(String loanStatus) 
    {
        this.loanStatus = loanStatus;
    }

    public String getLoanStatus() 
    {
        return loanStatus;
    }
    public void setApplyTime(Date applyTime) 
    {
        this.applyTime = applyTime;
    }

    public Date getApplyTime() 
    {
        return applyTime;
    }
    public void setLoanAmt(BigDecimal loanAmt) 
    {
        this.loanAmt = loanAmt;
    }

    public BigDecimal getLoanAmt() 
    {
        return loanAmt;
    }
    public void setBalanceAmt(BigDecimal balanceAmt) 
    {
        this.balanceAmt = balanceAmt;
    }

    public BigDecimal getBalanceAmt() 
    {
        return balanceAmt;
    }
    public void setTotalTerm(Integer totalTerm) 
    {
        this.totalTerm = totalTerm;
    }

    public Integer getTotalTerm() 
    {
        return totalTerm;
    }
    public void setLoanTime(Date loanTime) 
    {
        this.loanTime = loanTime;
    }

    public Date getLoanTime() 
    {
        return loanTime;
    }
    public void setDueDate(Date dueDate) 
    {
        this.dueDate = dueDate;
    }

    public Date getDueDate() 
    {
        return dueDate;
    }
    public void setLoanReqNo(String loanReqNo) 
    {
        this.loanReqNo = loanReqNo;
    }

    public String getLoanReqNo() 
    {
        return loanReqNo;
    }
    public void setRepayWay(String repayWay) 
    {
        this.repayWay = repayWay;
    }

    public String getRepayWay() 
    {
        return repayWay;
    }
    public void setLoanUse(String loanUse) 
    {
        this.loanUse = loanUse;
    }

    public String getLoanUse() 
    {
        return loanUse;
    }
    public void setCarBrandName(String carBrandName) 
    {
        this.carBrandName = carBrandName;
    }

    public String getCarBrandName() 
    {
        return carBrandName;
    }
    public void setCarNo(String carNo) 
    {
        this.carNo = carNo;
    }

    public String getCarNo() 
    {
        return carNo;
    }
    public void setCarVin(String carVin) 
    {
        this.carVin = carVin;
    }

    public String getCarVin() 
    {
        return carVin;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("cdlbBinding", getCdlbBinding())
            .append("cdlbId", getCdlbId())
            .append("platformNo", getPlatformNo())
            .append("custNo", getCustNo())
            .append("partnerNo", getPartnerNo())
            .append("fundNo", getFundNo())
            .append("productNo", getProductNo())
            .append("loanNo", getLoanNo())
            .append("clientName", getClientName())
            .append("clientCardId", getClientCardId())
            .append("clientCardAddress", getClientCardAddress())
            .append("loanStatus", getLoanStatus())
            .append("applyTime", getApplyTime())
            .append("loanAmt", getLoanAmt())
            .append("balanceAmt", getBalanceAmt())
            .append("totalTerm", getTotalTerm())
            .append("loanTime", getLoanTime())
            .append("dueDate", getDueDate())
            .append("loanReqNo", getLoanReqNo())
            .append("repayWay", getRepayWay())
            .append("loanUse", getLoanUse())
            .append("carBrandName", getCarBrandName())
            .append("carNo", getCarNo())
            .append("carVin", getCarVin())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .toString();
    }
}