package org.ruoyi.core.cdlb.service.impl;


import com.ruoyi.common.utils.DateUtils;
import org.ruoyi.core.cdlb.domain.CdlbFiles;
import org.ruoyi.core.cdlb.mapper.CdlbFilesMapper;
import org.ruoyi.core.cdlb.service.ICdlbFilesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 绿本文件关联Service业务层处理
 * 
 * <AUTHOR>
 * @date 2023-06-13
 */
@Service
public class CdlbFilesServiceImpl implements ICdlbFilesService
{
    @Autowired
    private CdlbFilesMapper cdlbFilesMapper;

    /**
     * 查询绿本文件关联
     * 
     * @param id 绿本文件关联主键
     * @return 绿本文件关联
     */
    @Override
    public CdlbFiles selectCdlbFilesById(Integer id)
    {
        return cdlbFilesMapper.selectCdlbFilesById(id);
    }

    /**
     * 查询绿本文件关联列表
     * 
     * @param cdlbFiles 绿本文件关联
     * @return 绿本文件关联
     */
    @Override
    public List<CdlbFiles> selectCdlbFilesList(CdlbFiles cdlbFiles)
    {
        return cdlbFilesMapper.selectCdlbFilesList(cdlbFiles);
    }

    /**
     * 新增绿本文件关联
     * 
     * @param cdlbFiles 绿本文件关联
     * @return 结果
     */
    @Override
    public int insertCdlbFiles(CdlbFiles cdlbFiles)
    {
        cdlbFiles.setCreateTime(DateUtils.getNowDate());
        return cdlbFilesMapper.insertCdlbFiles(cdlbFiles);
    }

    /**
     * 修改绿本文件关联
     * 
     * @param cdlbFiles 绿本文件关联
     * @return 结果
     */
    @Override
    public int updateCdlbFiles(CdlbFiles cdlbFiles)
    {
        cdlbFiles.setUpdateTime(DateUtils.getNowDate());
        return cdlbFilesMapper.updateCdlbFiles(cdlbFiles);
    }

    /**
     * 批量删除绿本文件关联
     * 
     * @param ids 需要删除的绿本文件关联主键
     * @return 结果
     */
    @Override
    public int deleteCdlbFilesByIds(Integer[] ids)
    {
        return cdlbFilesMapper.deleteCdlbFilesByIds(ids);
    }

    /**
     * 删除绿本文件关联信息
     * 
     * @param id 绿本文件关联主键
     * @return 结果
     */
    @Override
    public int deleteCdlbFilesById(Integer id)
    {
        return cdlbFilesMapper.deleteCdlbFilesById(id);
    }
}
