package com.ruoyi.financial.mapper;

import com.ruoyi.financial.domain.FinancialU8CRule;
import com.ruoyi.financial.domain.FinancialU8CRuleDetails;
import com.ruoyi.financial.domain.FinancialU8CVoucherSet;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 凭证推送规则Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Mapper
public interface FinancialU8CRuleMapper
{
    /**
     * 查询凭证推送规则
     *
     * @param code 规则编码
     * @return 凭证推送规则
     */
    public List<FinancialU8CRule> selectFinancialU8CRuleByCode(String code);

    /**
     * 查询凭证推送规则
     *
     * @param id 凭证推送规则主键
     * @return 凭证推送规则
     */
    public FinancialU8CRule selectFinancialU8CRuleById(Long id);

    /**
     * 查询凭证推送规则列表
     *
     * @param financialU8CRule 凭证推送规则
     * @return 凭证推送规则集合
     */
    public List<FinancialU8CRule> selectFinancialU8CRuleList(FinancialU8CRule financialU8CRule);

    /**
     * 新增凭证推送规则
     *
     * @param financialU8CRule 凭证推送规则
     * @return 结果
     */
    public int insertFinancialU8CRule(FinancialU8CRule financialU8CRule);

    /**
     * 修改凭证推送规则
     *
     * @param financialU8CRule 凭证推送规则
     * @return 结果
     */
    public int updateFinancialU8CRule(FinancialU8CRule financialU8CRule);

    /**
     * 删除凭证推送规则
     *
     * @param id 凭证推送规则主键
     * @return 结果
     */
    public int deleteFinancialU8CRuleById(Long id);

    /**
     * 批量删除凭证推送规则
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinancialU8CRuleByIds(Long[] ids);

    /**
     * 批量删除凭证推送规则明细表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFinancialU8CRuleDetailsByRuleIds(Long[] ids);

    /**
     * 批量新增凭证推送规则明细表
     *
     * @param financialU8CRuleDetailsList 凭证推送规则明细表列表
     * @return 结果
     */
    public int batchFinancialU8CRuleDetails(List<FinancialU8CRuleDetails> financialU8CRuleDetailsList);


    /**
     * 通过凭证推送规则主键删除凭证推送规则明细表信息
     *
     * @param id 凭证推送规则ID
     * @return 结果
     */
    public int deleteFinancialU8CRuleDetailsByRuleId(Long id);

    /**
     * 增加规则和账套关联表
     * @param id    规则主表ID
     * @param accounts  关联账套ID
     * @return
     */
    public int insertFinancialU8CRuleAccount(@Param("id") Long id, @Param("accounts") Long[] accounts);

    /**
     * 修改历史数据状态
     * @param id    数据ID
     * @param state 状态
     * @return
     */
    int updateOldData(@Param("id") Long id,@Param("state") String state);

    /**
     * 删除规则关联账套
     * @param ids   规则ID
     * @return
     */
    int deleteFinancialU8CRuleAccountByRuleIds(Long[] ids);

    /**
     * 批量查询子表数据
     * @param financialU8CRules 主表主键
     * @return
     */
    List<FinancialU8CRuleDetails> selectDetailsByIds(@Param("financialU8CRules") List<FinancialU8CRule> financialU8CRules);

    /**
     * 查询可用账套
     * @param financialU8CRules 主表主键
     * @return
     */
    List<FinancialU8CRule> selectAccpuntByIds(@Param("financialU8CRules") List<FinancialU8CRule> financialU8CRules);

    /**
     * 通过规则ID获取规则详情
     * @param ruleId
     * @return
     */
    List<FinancialU8CRuleDetails> selectRuleById(Long ruleId);

    /**
     * 通过账套ID获取规则详情
     * @param financialU8CRule
     * @return
     */
    List<FinancialU8CRule> selectChooseListByAccountId(FinancialU8CRule financialU8CRule);

    List<FinancialU8CRule> selectRuleByIds(@Param("set") List<FinancialU8CVoucherSet> financialU8CVoucherSets);
}

