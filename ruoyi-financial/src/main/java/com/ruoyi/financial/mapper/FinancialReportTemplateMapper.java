package com.ruoyi.financial.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.financial.domain.FinancialReportTemplate;
import com.ruoyi.financial.po.ReportTemplatePo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>****************************************************************************</p>
 * <p><b>Copyright © 2010-2019 soho team All Rights Reserved<b></p>
 * <ul style="margin:15px;">
 * <li>Description : cn.gson.financial.kernel.model.mapper</li>
 * <li>Version     : 1.0</li>
 * <li>Creation    : 2019年09月05日</li>
 * <li><AUTHOR> ____′↘夏悸</li>
 * </ul>
 * <p>****************************************************************************</p>
 */
public interface FinancialReportTemplateMapper extends BaseMapper<FinancialReportTemplate> {
    int batchInsert(@Param("list") List<FinancialReportTemplate> list);

    /**
     * <AUTHOR>
     * @Description 查询模板列表项信息
     * @Date 2024/10/24 14:58
     * @Param [templateKey, title]
     * @return java.util.List<com.ruoyi.financial.po.ReportTemplatePo>
     **/
    List<ReportTemplatePo> selectReportItemInfo(@Param("templateKey") String templateKey, @Param("title") String title);
}